[{"/Users/<USER>/Downloads/Flood/frontend/src/index.js": "1", "/Users/<USER>/Downloads/Flood/frontend/src/App.js": "2", "/Users/<USER>/Downloads/Flood/frontend/src/components/ResultDisplay.js": "3", "/Users/<USER>/Downloads/Flood/frontend/src/components/Header.js": "4", "/Users/<USER>/Downloads/Flood/frontend/src/components/FloodMap.js": "5", "/Users/<USER>/Downloads/Flood/frontend/src/components/RiskFactorsChart.js": "6", "/Users/<USER>/Downloads/Flood/frontend/src/components/PredictionForm.js": "7", "/Users/<USER>/Downloads/Flood/frontend/src/components/TimelineRiskPredictor.js": "8", "/Users/<USER>/Downloads/Flood/frontend/src/components/VoiceEmergencyAssistant.js": "9", "/Users/<USER>/Downloads/Flood/frontend/src/components/CommunityReports.js": "10", "/Users/<USER>/Downloads/Flood/frontend/src/components/animations/AnimatedComponents.js": "11", "/Users/<USER>/Downloads/Flood/frontend/src/hooks/useScrollAnimation.js": "12", "/Users/<USER>/Downloads/Flood/frontend/src/components/animations/LoadingAnimation.js": "13", "/Users/<USER>/Downloads/Flood/frontend/src/components/LocationSelector.js": "14", "/Users/<USER>/Downloads/Flood/frontend/src/theme/neuromorphicUtils.js": "15", "/Users/<USER>/Downloads/Flood copy/frontend/src/index.js": "16", "/Users/<USER>/Downloads/Flood copy/frontend/src/App.js": "17", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/Header.js": "18", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/PredictionForm.js": "19", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/ResultDisplay.js": "20", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/RiskFactorsChart.js": "21", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/TimelineRiskPredictor.js": "22", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/FloodMap.js": "23", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/CommunityReports.js": "24", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/VoiceEmergencyAssistant.js": "25", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/animations/AnimatedComponents.js": "26", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/animations/LoadingAnimation.js": "27", "/Users/<USER>/Downloads/Flood copy/frontend/src/theme/neuromorphicUtils.js": "28", "/Users/<USER>/Downloads/Flood copy/frontend/src/components/LocationSelector.js": "29", "/Users/<USER>/Downloads/Flood copy/frontend/src/hooks/useScrollAnimation.js": "30", "/Users/<USER>/Downloads/Flood copy/frontend/src/theme/floodguardTheme.js": "31"}, {"size": 254, "mtime": 1747753433573, "results": "32", "hashOfConfig": "33"}, {"size": 49395, "mtime": 1747801561833, "results": "34", "hashOfConfig": "33"}, {"size": 17971, "mtime": 1747764370243, "results": "35", "hashOfConfig": "33"}, {"size": 7400, "mtime": 1747754619314, "results": "36", "hashOfConfig": "33"}, {"size": 52763, "mtime": 1747764850190, "results": "37", "hashOfConfig": "33"}, {"size": 4057, "mtime": 1747753554779, "results": "38", "hashOfConfig": "33"}, {"size": 13816, "mtime": 1747764231471, "results": "39", "hashOfConfig": "33"}, {"size": 33887, "mtime": 1747800864886, "results": "40", "hashOfConfig": "33"}, {"size": 11970, "mtime": 1747757312237, "results": "41", "hashOfConfig": "33"}, {"size": 28486, "mtime": 1747799193533, "results": "42", "hashOfConfig": "33"}, {"size": 5615, "mtime": 1747761188824, "results": "43", "hashOfConfig": "33"}, {"size": 1564, "mtime": 1747761161281, "results": "44", "hashOfConfig": "33"}, {"size": 4145, "mtime": 1747761615192, "results": "45", "hashOfConfig": "33"}, {"size": 8969, "mtime": 1747799643617, "results": "46", "hashOfConfig": "33"}, {"size": 3672, "mtime": 1747801467631, "results": "47", "hashOfConfig": "33"}, {"size": 254, "mtime": 1747753433573, "results": "48", "hashOfConfig": "49"}, {"size": 35948, "mtime": 1749057688590, "results": "50", "hashOfConfig": "49"}, {"size": 13275, "mtime": 1749057569090, "results": "51", "hashOfConfig": "49"}, {"size": 17691, "mtime": 1749055333795, "results": "52", "hashOfConfig": "49"}, {"size": 17971, "mtime": 1747764370243, "results": "53", "hashOfConfig": "49"}, {"size": 4057, "mtime": 1747753554779, "results": "54", "hashOfConfig": "49"}, {"size": 33887, "mtime": 1747800864886, "results": "55", "hashOfConfig": "49"}, {"size": 62798, "mtime": 1747901159903, "results": "56", "hashOfConfig": "49"}, {"size": 28486, "mtime": 1747799193533, "results": "57", "hashOfConfig": "49"}, {"size": 11970, "mtime": 1747757312237, "results": "58", "hashOfConfig": "49"}, {"size": 5615, "mtime": 1747761188824, "results": "59", "hashOfConfig": "49"}, {"size": 4145, "mtime": 1747761615192, "results": "60", "hashOfConfig": "49"}, {"size": 7578, "mtime": 1749057133984, "results": "61", "hashOfConfig": "49"}, {"size": 8969, "mtime": 1747799643617, "results": "62", "hashOfConfig": "49"}, {"size": 1564, "mtime": 1747761161281, "results": "63", "hashOfConfig": "49"}, {"size": 16100, "mtime": 1749057699126, "results": "64", "hashOfConfig": "49"}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xqa9ev", {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1x8qpam", {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Downloads/Flood/frontend/src/index.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/App.js", ["158", "159", "160"], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/ResultDisplay.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/Header.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/FloodMap.js", ["161", "162", "163", "164", "165", "166", "167"], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/RiskFactorsChart.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/PredictionForm.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/TimelineRiskPredictor.js", ["168", "169"], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/VoiceEmergencyAssistant.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/CommunityReports.js", ["170", "171", "172", "173"], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/animations/AnimatedComponents.js", ["174"], [], "/Users/<USER>/Downloads/Flood/frontend/src/hooks/useScrollAnimation.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/animations/LoadingAnimation.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/LocationSelector.js", ["175", "176", "177"], [], "/Users/<USER>/Downloads/Flood/frontend/src/theme/neuromorphicUtils.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/index.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/App.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/Header.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/PredictionForm.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/ResultDisplay.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/RiskFactorsChart.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/TimelineRiskPredictor.js", ["178", "179"], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/FloodMap.js", ["180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190"], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/CommunityReports.js", ["191", "192", "193", "194"], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/VoiceEmergencyAssistant.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/animations/AnimatedComponents.js", ["195"], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/animations/LoadingAnimation.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/theme/neuromorphicUtils.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/components/LocationSelector.js", ["196", "197", "198"], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/hooks/useScrollAnimation.js", [], [], "/Users/<USER>/Downloads/Flood copy/frontend/src/theme/floodguardTheme.js", [], [], {"ruleId": "199", "severity": 1, "message": "200", "line": 16, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 16, "endColumn": 28}, {"ruleId": "199", "severity": 1, "message": "203", "line": 16, "column": 54, "nodeType": "201", "messageId": "202", "endLine": 16, "endColumn": 75}, {"ruleId": "199", "severity": 1, "message": "204", "line": 16, "column": 77, "nodeType": "201", "messageId": "202", "endLine": 16, "endColumn": 93}, {"ruleId": "199", "severity": 1, "message": "205", "line": 9, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 9, "endColumn": 14}, {"ruleId": "199", "severity": 1, "message": "206", "line": 10, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 10, "endColumn": 12}, {"ruleId": "199", "severity": 1, "message": "207", "line": 20, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 20, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "208", "line": 22, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 22, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "209", "line": 23, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 23, "endColumn": 14}, {"ruleId": "199", "severity": 1, "message": "210", "line": 44, "column": 8, "nodeType": "201", "messageId": "202", "endLine": 44, "endColumn": 18}, {"ruleId": "199", "severity": 1, "message": "211", "line": 445, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 445, "endColumn": 14}, {"ruleId": "199", "severity": 1, "message": "212", "line": 14, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 14, "endColumn": 10}, {"ruleId": "199", "severity": 1, "message": "213", "line": 35, "column": 8, "nodeType": "201", "messageId": "202", "endLine": 35, "endColumn": 22}, {"ruleId": "199", "severity": 1, "message": "214", "line": 26, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 26, "endColumn": 9}, {"ruleId": "215", "severity": 1, "message": "216", "line": 176, "column": 6, "nodeType": "217", "endLine": 176, "endColumn": 8, "suggestions": "218"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 199, "column": 6, "nodeType": "217", "endLine": 199, "endColumn": 8, "suggestions": "219"}, {"ruleId": "199", "severity": 1, "message": "220", "line": 252, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 252, "endColumn": 27}, {"ruleId": "221", "severity": 1, "message": "222", "line": 225, "column": 1, "nodeType": "223", "endLine": 234, "endColumn": 3}, {"ruleId": "199", "severity": 1, "message": "224", "line": 49, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 49, "endColumn": 21}, {"ruleId": "199", "severity": 1, "message": "225", "line": 49, "column": 23, "nodeType": "201", "messageId": "202", "endLine": 49, "endColumn": 37}, {"ruleId": "215", "severity": 1, "message": "226", "line": 112, "column": 6, "nodeType": "217", "endLine": 112, "endColumn": 24, "suggestions": "227"}, {"ruleId": "199", "severity": 1, "message": "212", "line": 14, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 14, "endColumn": 10}, {"ruleId": "199", "severity": 1, "message": "213", "line": 35, "column": 8, "nodeType": "201", "messageId": "202", "endLine": 35, "endColumn": 22}, {"ruleId": "199", "severity": 1, "message": "205", "line": 9, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 9, "endColumn": 14}, {"ruleId": "199", "severity": 1, "message": "206", "line": 10, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 10, "endColumn": 12}, {"ruleId": "199", "severity": 1, "message": "228", "line": 12, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 12, "endColumn": 10}, {"ruleId": "199", "severity": 1, "message": "207", "line": 21, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 21, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "208", "line": 23, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 23, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "209", "line": 24, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 24, "endColumn": 14}, {"ruleId": "199", "severity": 1, "message": "229", "line": 49, "column": 8, "nodeType": "201", "messageId": "202", "endLine": 49, "endColumn": 15}, {"ruleId": "199", "severity": 1, "message": "210", "line": 50, "column": 8, "nodeType": "201", "messageId": "202", "endLine": 50, "endColumn": 18}, {"ruleId": "199", "severity": 1, "message": "230", "line": 552, "column": 26, "nodeType": "201", "messageId": "202", "endLine": 552, "endColumn": 43}, {"ruleId": "199", "severity": 1, "message": "231", "line": 568, "column": 13, "nodeType": "201", "messageId": "202", "endLine": 568, "endColumn": 23}, {"ruleId": "199", "severity": 1, "message": "211", "line": 653, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 653, "endColumn": 14}, {"ruleId": "199", "severity": 1, "message": "214", "line": 26, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 26, "endColumn": 9}, {"ruleId": "215", "severity": 1, "message": "216", "line": 176, "column": 6, "nodeType": "217", "endLine": 176, "endColumn": 8, "suggestions": "232"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 199, "column": 6, "nodeType": "217", "endLine": 199, "endColumn": 8, "suggestions": "233"}, {"ruleId": "199", "severity": 1, "message": "220", "line": 252, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 252, "endColumn": 27}, {"ruleId": "221", "severity": 1, "message": "222", "line": 225, "column": 1, "nodeType": "223", "endLine": 234, "endColumn": 3}, {"ruleId": "199", "severity": 1, "message": "224", "line": 49, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 49, "endColumn": 21}, {"ruleId": "199", "severity": 1, "message": "225", "line": 49, "column": 23, "nodeType": "201", "messageId": "202", "endLine": 49, "endColumn": 37}, {"ruleId": "215", "severity": 1, "message": "226", "line": 112, "column": 6, "nodeType": "217", "endLine": 112, "endColumn": 24, "suggestions": "234"}, "no-unused-vars", "'neuromorphicStyles' is defined but never used.", "Identifier", "unusedVar", "'getNeuromorphicShadow' is defined but never used.", "'getPressedEffect' is defined but never used.", "'ZoomControl' is defined but never used.", "'Rectangle' is defined but never used.", "'Chip' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'LayersIcon' is defined but never used.", "'theme' is assigned a value but never used.", "'Divider' is defined but never used.", "'LocationOnIcon' is defined but never used.", "'Rating' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'mapAlertToReport'. Either include it or remove the dependency array.", "ArrayExpression", ["235"], ["236"], "'handleSliderChange' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'searchQuery' is assigned a value but never used.", "'setSearchQuery' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'findNearestCity'. Either include it or remove the dependency array.", ["237"], "'Polygon' is defined but never used.", "'MapIcon' is defined but never used.", "'setHeatMapVisible' is assigned a value but never used.", "'zoneCenter' is assigned a value but never used.", ["238"], ["239"], ["240"], {"desc": "241", "fix": "242"}, {"desc": "241", "fix": "243"}, {"desc": "244", "fix": "245"}, {"desc": "241", "fix": "246"}, {"desc": "241", "fix": "247"}, {"desc": "244", "fix": "248"}, "Update the dependencies array to be: [mapAlertToReport]", {"range": "249", "text": "250"}, {"range": "251", "text": "250"}, "Update the dependencies array to be: [findNearestCity, onLocationSelect]", {"range": "252", "text": "253"}, {"range": "254", "text": "250"}, {"range": "255", "text": "250"}, {"range": "256", "text": "253"}, [4769, 4771], "[mapAlertToReport]", [5425, 5427], [4524, 4542], "[findNearestCity, onLocationSelect]", [4769, 4771], [5425, 5427], [4524, 4542]]