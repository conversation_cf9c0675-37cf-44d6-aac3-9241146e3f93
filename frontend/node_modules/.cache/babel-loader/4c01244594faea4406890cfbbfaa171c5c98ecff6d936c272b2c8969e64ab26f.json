{"ast": null, "code": "'use strict';\n\n// Few cool transports do work only for same-origin. In order to make\n// them work cross-domain we shall use iframe, served from the\n// remote domain. New browsers have capabilities to communicate with\n// cross domain iframe using postMessage(). In IE it was implemented\n// from IE 8+, but of course, IE got some details wrong:\n//    http://msdn.microsoft.com/en-us/library/cc197015(v=VS.85).aspx\n//    http://stevesouders.com/misc/test-postmessage.php\nvar inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter,\n  version = require('../version'),\n  urlUtils = require('../utils/url'),\n  iframeUtils = require('../utils/iframe'),\n  eventUtils = require('../utils/event'),\n  random = require('../utils/random');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:transport:iframe');\n}\nfunction IframeTransport(transport, transUrl, baseUrl) {\n  if (!IframeTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  EventEmitter.call(this);\n  var self = this;\n  this.origin = urlUtils.getOrigin(baseUrl);\n  this.baseUrl = baseUrl;\n  this.transUrl = transUrl;\n  this.transport = transport;\n  this.windowId = random.string(8);\n  var iframeUrl = urlUtils.addPath(baseUrl, '/iframe.html') + '#' + this.windowId;\n  debug(transport, transUrl, iframeUrl);\n  this.iframeObj = iframeUtils.createIframe(iframeUrl, function (r) {\n    debug('err callback');\n    self.emit('close', 1006, 'Unable to load an iframe (' + r + ')');\n    self.close();\n  });\n  this.onmessageCallback = this._message.bind(this);\n  eventUtils.attachEvent('message', this.onmessageCallback);\n}\ninherits(IframeTransport, EventEmitter);\nIframeTransport.prototype.close = function () {\n  debug('close');\n  this.removeAllListeners();\n  if (this.iframeObj) {\n    eventUtils.detachEvent('message', this.onmessageCallback);\n    try {\n      // When the iframe is not loaded, IE raises an exception\n      // on 'contentWindow'.\n      this.postMessage('c');\n    } catch (x) {\n      // intentionally empty\n    }\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n    this.onmessageCallback = this.iframeObj = null;\n  }\n};\nIframeTransport.prototype._message = function (e) {\n  debug('message', e.data);\n  if (!urlUtils.isOriginEqual(e.origin, this.origin)) {\n    debug('not same origin', e.origin, this.origin);\n    return;\n  }\n  var iframeMessage;\n  try {\n    iframeMessage = JSON.parse(e.data);\n  } catch (ignored) {\n    debug('bad json', e.data);\n    return;\n  }\n  if (iframeMessage.windowId !== this.windowId) {\n    debug('mismatched window id', iframeMessage.windowId, this.windowId);\n    return;\n  }\n  switch (iframeMessage.type) {\n    case 's':\n      this.iframeObj.loaded();\n      // window global dependency\n      this.postMessage('s', JSON.stringify([version, this.transport, this.transUrl, this.baseUrl]));\n      break;\n    case 't':\n      this.emit('message', iframeMessage.data);\n      break;\n    case 'c':\n      var cdata;\n      try {\n        cdata = JSON.parse(iframeMessage.data);\n      } catch (ignored) {\n        debug('bad json', iframeMessage.data);\n        return;\n      }\n      this.emit('close', cdata[0], cdata[1]);\n      this.close();\n      break;\n  }\n};\nIframeTransport.prototype.postMessage = function (type, data) {\n  debug('postMessage', type, data);\n  this.iframeObj.post(JSON.stringify({\n    windowId: this.windowId,\n    type: type,\n    data: data || ''\n  }), this.origin);\n};\nIframeTransport.prototype.send = function (message) {\n  debug('send', message);\n  this.postMessage('m', message);\n};\nIframeTransport.enabled = function () {\n  return iframeUtils.iframeEnabled;\n};\nIframeTransport.transportName = 'iframe';\nIframeTransport.roundTrips = 2;\nmodule.exports = IframeTransport;", "map": {"version": 3, "names": ["inherits", "require", "EventEmitter", "version", "urlUtils", "iframe<PERSON><PERSON>s", "eventUtils", "random", "debug", "process", "env", "NODE_ENV", "IframeTransport", "transport", "transUrl", "baseUrl", "enabled", "Error", "call", "self", "origin", "<PERSON><PERSON><PERSON><PERSON>", "windowId", "string", "iframeUrl", "addPath", "iframeObj", "createIframe", "r", "emit", "close", "onmessageCallback", "_message", "bind", "attachEvent", "prototype", "removeAllListeners", "detachEvent", "postMessage", "x", "cleanup", "e", "data", "isOriginEqual", "iframeMessage", "JSON", "parse", "ignored", "type", "loaded", "stringify", "cdata", "post", "send", "message", "iframeEnabled", "transportName", "roundTrips", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/iframe.js"], "sourcesContent": ["'use strict';\n\n// Few cool transports do work only for same-origin. In order to make\n// them work cross-domain we shall use iframe, served from the\n// remote domain. New browsers have capabilities to communicate with\n// cross domain iframe using postMessage(). In IE it was implemented\n// from IE 8+, but of course, IE got some details wrong:\n//    http://msdn.microsoft.com/en-us/library/cc197015(v=VS.85).aspx\n//    http://stevesouders.com/misc/test-postmessage.php\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , version = require('../version')\n  , urlUtils = require('../utils/url')\n  , iframeUtils = require('../utils/iframe')\n  , eventUtils = require('../utils/event')\n  , random = require('../utils/random')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:transport:iframe');\n}\n\nfunction IframeTransport(transport, transUrl, baseUrl) {\n  if (!IframeTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  EventEmitter.call(this);\n\n  var self = this;\n  this.origin = urlUtils.getOrigin(baseUrl);\n  this.baseUrl = baseUrl;\n  this.transUrl = transUrl;\n  this.transport = transport;\n  this.windowId = random.string(8);\n\n  var iframeUrl = urlUtils.addPath(baseUrl, '/iframe.html') + '#' + this.windowId;\n  debug(transport, transUrl, iframeUrl);\n\n  this.iframeObj = iframeUtils.createIframe(iframeUrl, function(r) {\n    debug('err callback');\n    self.emit('close', 1006, 'Unable to load an iframe (' + r + ')');\n    self.close();\n  });\n\n  this.onmessageCallback = this._message.bind(this);\n  eventUtils.attachEvent('message', this.onmessageCallback);\n}\n\ninherits(IframeTransport, EventEmitter);\n\nIframeTransport.prototype.close = function() {\n  debug('close');\n  this.removeAllListeners();\n  if (this.iframeObj) {\n    eventUtils.detachEvent('message', this.onmessageCallback);\n    try {\n      // When the iframe is not loaded, IE raises an exception\n      // on 'contentWindow'.\n      this.postMessage('c');\n    } catch (x) {\n      // intentionally empty\n    }\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n    this.onmessageCallback = this.iframeObj = null;\n  }\n};\n\nIframeTransport.prototype._message = function(e) {\n  debug('message', e.data);\n  if (!urlUtils.isOriginEqual(e.origin, this.origin)) {\n    debug('not same origin', e.origin, this.origin);\n    return;\n  }\n\n  var iframeMessage;\n  try {\n    iframeMessage = JSON.parse(e.data);\n  } catch (ignored) {\n    debug('bad json', e.data);\n    return;\n  }\n\n  if (iframeMessage.windowId !== this.windowId) {\n    debug('mismatched window id', iframeMessage.windowId, this.windowId);\n    return;\n  }\n\n  switch (iframeMessage.type) {\n  case 's':\n    this.iframeObj.loaded();\n    // window global dependency\n    this.postMessage('s', JSON.stringify([\n      version\n    , this.transport\n    , this.transUrl\n    , this.baseUrl\n    ]));\n    break;\n  case 't':\n    this.emit('message', iframeMessage.data);\n    break;\n  case 'c':\n    var cdata;\n    try {\n      cdata = JSON.parse(iframeMessage.data);\n    } catch (ignored) {\n      debug('bad json', iframeMessage.data);\n      return;\n    }\n    this.emit('close', cdata[0], cdata[1]);\n    this.close();\n    break;\n  }\n};\n\nIframeTransport.prototype.postMessage = function(type, data) {\n  debug('postMessage', type, data);\n  this.iframeObj.post(JSON.stringify({\n    windowId: this.windowId\n  , type: type\n  , data: data || ''\n  }), this.origin);\n};\n\nIframeTransport.prototype.send = function(message) {\n  debug('send', message);\n  this.postMessage('m', message);\n};\n\nIframeTransport.enabled = function() {\n  return iframeUtils.iframeEnabled;\n};\n\nIframeTransport.transportName = 'iframe';\nIframeTransport.roundTrips = 2;\n\nmodule.exports = IframeTransport;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,YAAY,GAAGD,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY;EAC7CC,OAAO,GAAGF,OAAO,CAAC,YAAY,CAAC;EAC/BG,QAAQ,GAAGH,OAAO,CAAC,cAAc,CAAC;EAClCI,WAAW,GAAGJ,OAAO,CAAC,iBAAiB,CAAC;EACxCK,UAAU,GAAGL,OAAO,CAAC,gBAAgB,CAAC;EACtCM,MAAM,GAAGN,OAAO,CAAC,iBAAiB,CAAC;AAGvC,IAAIO,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGP,OAAO,CAAC,OAAO,CAAC,CAAC,gCAAgC,CAAC;AAC5D;AAEA,SAASW,eAAeA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACrD,IAAI,CAACH,eAAe,CAACI,OAAO,CAAC,CAAC,EAAE;IAC9B,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACAf,YAAY,CAACgB,IAAI,CAAC,IAAI,CAAC;EAEvB,IAAIC,IAAI,GAAG,IAAI;EACf,IAAI,CAACC,MAAM,GAAGhB,QAAQ,CAACiB,SAAS,CAACN,OAAO,CAAC;EACzC,IAAI,CAACA,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACD,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACD,SAAS,GAAGA,SAAS;EAC1B,IAAI,CAACS,QAAQ,GAAGf,MAAM,CAACgB,MAAM,CAAC,CAAC,CAAC;EAEhC,IAAIC,SAAS,GAAGpB,QAAQ,CAACqB,OAAO,CAACV,OAAO,EAAE,cAAc,CAAC,GAAG,GAAG,GAAG,IAAI,CAACO,QAAQ;EAC/Ed,KAAK,CAACK,SAAS,EAAEC,QAAQ,EAAEU,SAAS,CAAC;EAErC,IAAI,CAACE,SAAS,GAAGrB,WAAW,CAACsB,YAAY,CAACH,SAAS,EAAE,UAASI,CAAC,EAAE;IAC/DpB,KAAK,CAAC,cAAc,CAAC;IACrBW,IAAI,CAACU,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,4BAA4B,GAAGD,CAAC,GAAG,GAAG,CAAC;IAChET,IAAI,CAACW,KAAK,CAAC,CAAC;EACd,CAAC,CAAC;EAEF,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC;EACjD3B,UAAU,CAAC4B,WAAW,CAAC,SAAS,EAAE,IAAI,CAACH,iBAAiB,CAAC;AAC3D;AAEA/B,QAAQ,CAACY,eAAe,EAAEV,YAAY,CAAC;AAEvCU,eAAe,CAACuB,SAAS,CAACL,KAAK,GAAG,YAAW;EAC3CtB,KAAK,CAAC,OAAO,CAAC;EACd,IAAI,CAAC4B,kBAAkB,CAAC,CAAC;EACzB,IAAI,IAAI,CAACV,SAAS,EAAE;IAClBpB,UAAU,CAAC+B,WAAW,CAAC,SAAS,EAAE,IAAI,CAACN,iBAAiB,CAAC;IACzD,IAAI;MACF;MACA;MACA,IAAI,CAACO,WAAW,CAAC,GAAG,CAAC;IACvB,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV;IAAA;IAEF,IAAI,CAACb,SAAS,CAACc,OAAO,CAAC,CAAC;IACxB,IAAI,CAACd,SAAS,GAAG,IAAI;IACrB,IAAI,CAACK,iBAAiB,GAAG,IAAI,CAACL,SAAS,GAAG,IAAI;EAChD;AACF,CAAC;AAEDd,eAAe,CAACuB,SAAS,CAACH,QAAQ,GAAG,UAASS,CAAC,EAAE;EAC/CjC,KAAK,CAAC,SAAS,EAAEiC,CAAC,CAACC,IAAI,CAAC;EACxB,IAAI,CAACtC,QAAQ,CAACuC,aAAa,CAACF,CAAC,CAACrB,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,EAAE;IAClDZ,KAAK,CAAC,iBAAiB,EAAEiC,CAAC,CAACrB,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC;IAC/C;EACF;EAEA,IAAIwB,aAAa;EACjB,IAAI;IACFA,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACL,CAAC,CAACC,IAAI,CAAC;EACpC,CAAC,CAAC,OAAOK,OAAO,EAAE;IAChBvC,KAAK,CAAC,UAAU,EAAEiC,CAAC,CAACC,IAAI,CAAC;IACzB;EACF;EAEA,IAAIE,aAAa,CAACtB,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;IAC5Cd,KAAK,CAAC,sBAAsB,EAAEoC,aAAa,CAACtB,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC;IACpE;EACF;EAEA,QAAQsB,aAAa,CAACI,IAAI;IAC1B,KAAK,GAAG;MACN,IAAI,CAACtB,SAAS,CAACuB,MAAM,CAAC,CAAC;MACvB;MACA,IAAI,CAACX,WAAW,CAAC,GAAG,EAAEO,IAAI,CAACK,SAAS,CAAC,CACnC/C,OAAO,EACP,IAAI,CAACU,SAAS,EACd,IAAI,CAACC,QAAQ,EACb,IAAI,CAACC,OAAO,CACb,CAAC,CAAC;MACH;IACF,KAAK,GAAG;MACN,IAAI,CAACc,IAAI,CAAC,SAAS,EAAEe,aAAa,CAACF,IAAI,CAAC;MACxC;IACF,KAAK,GAAG;MACN,IAAIS,KAAK;MACT,IAAI;QACFA,KAAK,GAAGN,IAAI,CAACC,KAAK,CAACF,aAAa,CAACF,IAAI,CAAC;MACxC,CAAC,CAAC,OAAOK,OAAO,EAAE;QAChBvC,KAAK,CAAC,UAAU,EAAEoC,aAAa,CAACF,IAAI,CAAC;QACrC;MACF;MACA,IAAI,CAACb,IAAI,CAAC,OAAO,EAAEsB,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;MACtC,IAAI,CAACrB,KAAK,CAAC,CAAC;MACZ;EACF;AACF,CAAC;AAEDlB,eAAe,CAACuB,SAAS,CAACG,WAAW,GAAG,UAASU,IAAI,EAAEN,IAAI,EAAE;EAC3DlC,KAAK,CAAC,aAAa,EAAEwC,IAAI,EAAEN,IAAI,CAAC;EAChC,IAAI,CAAChB,SAAS,CAAC0B,IAAI,CAACP,IAAI,CAACK,SAAS,CAAC;IACjC5B,QAAQ,EAAE,IAAI,CAACA,QAAQ;IACvB0B,IAAI,EAAEA,IAAI;IACVN,IAAI,EAAEA,IAAI,IAAI;EAChB,CAAC,CAAC,EAAE,IAAI,CAACtB,MAAM,CAAC;AAClB,CAAC;AAEDR,eAAe,CAACuB,SAAS,CAACkB,IAAI,GAAG,UAASC,OAAO,EAAE;EACjD9C,KAAK,CAAC,MAAM,EAAE8C,OAAO,CAAC;EACtB,IAAI,CAAChB,WAAW,CAAC,GAAG,EAAEgB,OAAO,CAAC;AAChC,CAAC;AAED1C,eAAe,CAACI,OAAO,GAAG,YAAW;EACnC,OAAOX,WAAW,CAACkD,aAAa;AAClC,CAAC;AAED3C,eAAe,CAAC4C,aAAa,GAAG,QAAQ;AACxC5C,eAAe,CAAC6C,UAAU,GAAG,CAAC;AAE9BC,MAAM,CAACC,OAAO,GAAG/C,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}