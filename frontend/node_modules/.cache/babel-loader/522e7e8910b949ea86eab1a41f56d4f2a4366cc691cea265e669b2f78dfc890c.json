{"ast": null, "code": "/**\n * @internal\n */\nexport function augmentWebsocket(webSocket, debug) {\n  webSocket.terminate = function () {\n    const noOp = () => {};\n    // set all callbacks to no op\n    this.onerror = noOp;\n    this.onmessage = noOp;\n    this.onopen = noOp;\n    const ts = new Date();\n    const id = Math.random().toString().substring(2, 8); // A simulated id\n    const origOnClose = this.onclose;\n    // Track delay in actual closure of the socket\n    this.onclose = closeEvent => {\n      const delay = new Date().getTime() - ts.getTime();\n      debug(`Discarded socket (#${id})  closed after ${delay}ms, with code/reason: ${closeEvent.code}/${closeEvent.reason}`);\n    };\n    this.close();\n    origOnClose?.call(webSocket, {\n      code: 4001,\n      reason: `Quick discarding socket (#${id}) without waiting for the shutdown sequence.`,\n      wasClean: false\n    });\n  };\n}", "map": {"version": 3, "names": ["augmentWebsocket", "webSocket", "debug", "terminate", "noOp", "onerror", "onmessage", "onopen", "ts", "Date", "id", "Math", "random", "toString", "substring", "origOnClose", "onclose", "closeEvent", "delay", "getTime", "code", "reason", "close", "call", "<PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@stomp/stompjs/src/augment-websocket.ts"], "sourcesContent": ["import { IStompSocket } from './types.js';\n\n/**\n * @internal\n */\nexport function augmentWebsocket(\n  webSocket: IStompSocket,\n  debug: (msg: string) => void\n) {\n  webSocket.terminate = function () {\n    const noOp = () => {};\n\n    // set all callbacks to no op\n    this.onerror = noOp;\n    this.onmessage = noOp;\n    this.onopen = noOp;\n\n    const ts = new Date();\n    const id = Math.random().toString().substring(2, 8); // A simulated id\n\n    const origOnClose = this.onclose;\n\n    // Track delay in actual closure of the socket\n    this.onclose = closeEvent => {\n      const delay = new Date().getTime() - ts.getTime();\n      debug(\n        `Discarded socket (#${id})  closed after ${delay}ms, with code/reason: ${closeEvent.code}/${closeEvent.reason}`\n      );\n    };\n\n    this.close();\n\n    origOnClose?.call(webSocket, {\n      code: 4001,\n      reason: `Quick discarding socket (#${id}) without waiting for the shutdown sequence.`,\n      wasClean: false,\n    });\n  };\n}\n"], "mappings": "AAEA;;;AAGA,OAAM,SAAUA,gBAAgBA,CAC9BC,SAAuB,EACvBC,KAA4B;EAE5BD,SAAS,CAACE,SAAS,GAAG;IACpB,MAAMC,IAAI,GAAGA,CAAA,KAAK,CAAE,CAAC;IAErB;IACA,IAAI,CAACC,OAAO,GAAGD,IAAI;IACnB,IAAI,CAACE,SAAS,GAAGF,IAAI;IACrB,IAAI,CAACG,MAAM,GAAGH,IAAI;IAElB,MAAMI,EAAE,GAAG,IAAIC,IAAI,EAAE;IACrB,MAAMC,EAAE,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,EAAE,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAErD,MAAMC,WAAW,GAAG,IAAI,CAACC,OAAO;IAEhC;IACA,IAAI,CAACA,OAAO,GAAGC,UAAU,IAAG;MAC1B,MAAMC,KAAK,GAAG,IAAIT,IAAI,EAAE,CAACU,OAAO,EAAE,GAAGX,EAAE,CAACW,OAAO,EAAE;MACjDjB,KAAK,CACH,sBAAsBQ,EAAE,mBAAmBQ,KAAK,yBAAyBD,UAAU,CAACG,IAAI,IAAIH,UAAU,CAACI,MAAM,EAAE,CAChH;IACH,CAAC;IAED,IAAI,CAACC,KAAK,EAAE;IAEZP,WAAW,EAAEQ,IAAI,CAACtB,SAAS,EAAE;MAC3BmB,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,6BAA6BX,EAAE,8CAA8C;MACrFc,QAAQ,EAAE;KACX,CAAC;EACJ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}