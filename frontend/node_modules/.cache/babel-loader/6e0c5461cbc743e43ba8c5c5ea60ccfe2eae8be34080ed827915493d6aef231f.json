{"ast": null, "code": "// src/Animated.ts\nimport { defineHidden } from \"@react-spring/shared\";\nvar $node = Symbol.for(\"Animated:node\");\nvar isAnimated = value => !!value && value[$node] === value;\nvar getAnimated = owner => owner && owner[$node];\nvar setAnimated = (owner, node) => defineHidden(owner, $node, node);\nvar getPayload = owner => owner && owner[$node] && owner[$node].getPayload();\nvar Animated = class {\n  constructor() {\n    setAnimated(this, this);\n  }\n  /** Get every `AnimatedValue` used by this node. */\n  getPayload() {\n    return this.payload || [];\n  }\n};\n\n// src/AnimatedValue.ts\nimport { is } from \"@react-spring/shared\";\nvar AnimatedValue = class extends Animated {\n  constructor(_value) {\n    super();\n    this._value = _value;\n    this.done = true;\n    this.durationProgress = 0;\n    if (is.num(this._value)) {\n      this.lastPosition = this._value;\n    }\n  }\n  /** @internal */\n  static create(value) {\n    return new AnimatedValue(value);\n  }\n  getPayload() {\n    return [this];\n  }\n  getValue() {\n    return this._value;\n  }\n  setValue(value, step) {\n    if (is.num(value)) {\n      this.lastPosition = value;\n      if (step) {\n        value = Math.round(value / step) * step;\n        if (this.done) {\n          this.lastPosition = value;\n        }\n      }\n    }\n    if (this._value === value) {\n      return false;\n    }\n    this._value = value;\n    return true;\n  }\n  reset() {\n    const {\n      done\n    } = this;\n    this.done = false;\n    if (is.num(this._value)) {\n      this.elapsedTime = 0;\n      this.durationProgress = 0;\n      this.lastPosition = this._value;\n      if (done) this.lastVelocity = null;\n      this.v0 = null;\n    }\n  }\n};\n\n// src/AnimatedString.ts\nimport { is as is2, createInterpolator } from \"@react-spring/shared\";\nvar AnimatedString = class extends AnimatedValue {\n  constructor(value) {\n    super(0);\n    this._string = null;\n    this._toString = createInterpolator({\n      output: [value, value]\n    });\n  }\n  /** @internal */\n  static create(value) {\n    return new AnimatedString(value);\n  }\n  getValue() {\n    const value = this._string;\n    return value == null ? this._string = this._toString(this._value) : value;\n  }\n  setValue(value) {\n    if (is2.str(value)) {\n      if (value == this._string) {\n        return false;\n      }\n      this._string = value;\n      this._value = 1;\n    } else if (super.setValue(value)) {\n      this._string = null;\n    } else {\n      return false;\n    }\n    return true;\n  }\n  reset(goal) {\n    if (goal) {\n      this._toString = createInterpolator({\n        output: [this.getValue(), goal]\n      });\n    }\n    this._value = 0;\n    super.reset();\n  }\n};\n\n// src/AnimatedArray.ts\nimport { isAnimatedString } from \"@react-spring/shared\";\n\n// src/AnimatedObject.ts\nimport { each, eachProp, getFluidValue, hasFluidValue } from \"@react-spring/shared\";\n\n// src/context.ts\nvar TreeContext = {\n  dependencies: null\n};\n\n// src/AnimatedObject.ts\nvar AnimatedObject = class extends Animated {\n  constructor(source) {\n    super();\n    this.source = source;\n    this.setValue(source);\n  }\n  getValue(animated) {\n    const values = {};\n    eachProp(this.source, (source, key) => {\n      if (isAnimated(source)) {\n        values[key] = source.getValue(animated);\n      } else if (hasFluidValue(source)) {\n        values[key] = getFluidValue(source);\n      } else if (!animated) {\n        values[key] = source;\n      }\n    });\n    return values;\n  }\n  /** Replace the raw object data */\n  setValue(source) {\n    this.source = source;\n    this.payload = this._makePayload(source);\n  }\n  reset() {\n    if (this.payload) {\n      each(this.payload, node => node.reset());\n    }\n  }\n  /** Create a payload set. */\n  _makePayload(source) {\n    if (source) {\n      const payload = /* @__PURE__ */new Set();\n      eachProp(source, this._addToPayload, payload);\n      return Array.from(payload);\n    }\n  }\n  /** Add to a payload set. */\n  _addToPayload(source) {\n    if (TreeContext.dependencies && hasFluidValue(source)) {\n      TreeContext.dependencies.add(source);\n    }\n    const payload = getPayload(source);\n    if (payload) {\n      each(payload, node => this.add(node));\n    }\n  }\n};\n\n// src/AnimatedArray.ts\nvar AnimatedArray = class extends AnimatedObject {\n  constructor(source) {\n    super(source);\n  }\n  /** @internal */\n  static create(source) {\n    return new AnimatedArray(source);\n  }\n  getValue() {\n    return this.source.map(node => node.getValue());\n  }\n  setValue(source) {\n    const payload = this.getPayload();\n    if (source.length == payload.length) {\n      return payload.map((node, i) => node.setValue(source[i])).some(Boolean);\n    }\n    super.setValue(source.map(makeAnimated));\n    return true;\n  }\n};\nfunction makeAnimated(value) {\n  const nodeType = isAnimatedString(value) ? AnimatedString : AnimatedValue;\n  return nodeType.create(value);\n}\n\n// src/getAnimatedType.ts\nimport { is as is3, isAnimatedString as isAnimatedString2 } from \"@react-spring/shared\";\nfunction getAnimatedType(value) {\n  const parentNode = getAnimated(value);\n  return parentNode ? parentNode.constructor : is3.arr(value) ? AnimatedArray : isAnimatedString2(value) ? AnimatedString : AnimatedValue;\n}\n\n// src/createHost.ts\nimport { is as is5, eachProp as eachProp2 } from \"@react-spring/shared\";\n\n// src/withAnimated.tsx\nimport * as React from \"react\";\nimport { forwardRef, useRef, useCallback, useEffect } from \"react\";\nimport { is as is4, each as each2, raf, useForceUpdate, useOnce, addFluidObserver, removeFluidObserver, useIsomorphicLayoutEffect } from \"@react-spring/shared\";\nvar withAnimated = (Component, host) => {\n  const hasInstance =\n  // Function components must use \"forwardRef\" to avoid being\n  // re-rendered on every animation frame.\n  !is4.fun(Component) || Component.prototype && Component.prototype.isReactComponent;\n  return forwardRef((givenProps, givenRef) => {\n    const instanceRef = useRef(null);\n    const ref = hasInstance &&\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useCallback(value => {\n      instanceRef.current = updateRef(givenRef, value);\n    }, [givenRef]);\n    const [props, deps] = getAnimatedState(givenProps, host);\n    const forceUpdate = useForceUpdate();\n    const callback = () => {\n      const instance = instanceRef.current;\n      if (hasInstance && !instance) {\n        return;\n      }\n      const didUpdate = instance ? host.applyAnimatedValues(instance, props.getValue(true)) : false;\n      if (didUpdate === false) {\n        forceUpdate();\n      }\n    };\n    const observer = new PropsObserver(callback, deps);\n    const observerRef = useRef();\n    useIsomorphicLayoutEffect(() => {\n      observerRef.current = observer;\n      each2(deps, dep => addFluidObserver(dep, observer));\n      return () => {\n        if (observerRef.current) {\n          each2(observerRef.current.deps, dep => removeFluidObserver(dep, observerRef.current));\n          raf.cancel(observerRef.current.update);\n        }\n      };\n    });\n    useEffect(callback, []);\n    useOnce(() => () => {\n      const observer2 = observerRef.current;\n      each2(observer2.deps, dep => removeFluidObserver(dep, observer2));\n    });\n    const usedProps = host.getComponentProps(props.getValue());\n    return /* @__PURE__ */React.createElement(Component, {\n      ...usedProps,\n      ref\n    });\n  });\n};\nvar PropsObserver = class {\n  constructor(update, deps) {\n    this.update = update;\n    this.deps = deps;\n  }\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      raf.write(this.update);\n    }\n  }\n};\nfunction getAnimatedState(props, host) {\n  const dependencies = /* @__PURE__ */new Set();\n  TreeContext.dependencies = dependencies;\n  if (props.style) props = {\n    ...props,\n    style: host.createAnimatedStyle(props.style)\n  };\n  props = new AnimatedObject(props);\n  TreeContext.dependencies = null;\n  return [props, dependencies];\n}\nfunction updateRef(ref, value) {\n  if (ref) {\n    if (is4.fun(ref)) ref(value);else ref.current = value;\n  }\n  return value;\n}\n\n// src/createHost.ts\nvar cacheKey = Symbol.for(\"AnimatedComponent\");\nvar createHost = (components, {\n  applyAnimatedValues = () => false,\n  createAnimatedStyle = style => new AnimatedObject(style),\n  getComponentProps = props => props\n} = {}) => {\n  const hostConfig = {\n    applyAnimatedValues,\n    createAnimatedStyle,\n    getComponentProps\n  };\n  const animated = Component => {\n    const displayName = getDisplayName(Component) || \"Anonymous\";\n    if (is5.str(Component)) {\n      Component = animated[Component] || (animated[Component] = withAnimated(Component, hostConfig));\n    } else {\n      Component = Component[cacheKey] || (Component[cacheKey] = withAnimated(Component, hostConfig));\n    }\n    Component.displayName = `Animated(${displayName})`;\n    return Component;\n  };\n  eachProp2(components, (Component, key) => {\n    if (is5.arr(components)) {\n      key = getDisplayName(Component);\n    }\n    animated[key] = animated(Component);\n  });\n  return {\n    animated\n  };\n};\nvar getDisplayName = arg => is5.str(arg) ? arg : arg && is5.str(arg.displayName) ? arg.displayName : is5.fun(arg) && arg.name || null;\nexport { Animated, AnimatedArray, AnimatedObject, AnimatedString, AnimatedValue, createHost, getAnimated, getAnimatedType, getPayload, isAnimated, setAnimated };", "map": {"version": 3, "names": ["defineHidden", "$node", "Symbol", "for", "isAnimated", "value", "getAnimated", "owner", "setAnimated", "node", "getPayload", "Animated", "constructor", "payload", "is", "AnimatedValue", "_value", "done", "durationProgress", "num", "lastPosition", "create", "getValue", "setValue", "step", "Math", "round", "reset", "elapsedTime", "lastVelocity", "v0", "is2", "createInterpolator", "AnimatedString", "_string", "_toString", "output", "str", "goal", "isAnimatedString", "each", "eachProp", "getFluidValue", "hasFluidValue", "TreeContext", "dependencies", "AnimatedObject", "source", "animated", "values", "key", "_makePayload", "Set", "_addToPayload", "Array", "from", "add", "AnimatedArray", "map", "length", "i", "some", "Boolean", "makeAnimated", "nodeType", "is3", "isAnimatedString2", "getAnimatedType", "parentNode", "arr", "is5", "eachProp2", "React", "forwardRef", "useRef", "useCallback", "useEffect", "is4", "each2", "raf", "useForceUpdate", "useOnce", "addFluidObserver", "removeFluidObserver", "useIsomorphicLayoutEffect", "withAnimated", "Component", "host", "hasInstance", "fun", "prototype", "isReactComponent", "givenProps", "givenRef", "instanceRef", "ref", "current", "updateRef", "props", "deps", "getAnimatedState", "forceUpdate", "callback", "instance", "didUpdate", "applyAnimatedValues", "observer", "PropsObserver", "observerRef", "dep", "cancel", "update", "observer2", "usedProps", "getComponentProps", "createElement", "eventObserved", "event", "type", "write", "style", "createAnimatedStyle", "cache<PERSON>ey", "createHost", "components", "hostConfig", "displayName", "getDisplayName", "arg", "name"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/animated/src/Animated.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/animated/src/AnimatedValue.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/animated/src/AnimatedString.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/animated/src/AnimatedArray.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/animated/src/AnimatedObject.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/animated/src/context.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/animated/src/getAnimatedType.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/animated/src/createHost.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/animated/src/withAnimated.tsx"], "sourcesContent": ["import { defineHidden } from '@react-spring/shared'\nimport { AnimatedValue } from './AnimatedValue'\n\nconst $node: any = Symbol.for('Animated:node')\n\nexport const isAnimated = <T = any>(value: any): value is Animated<T> =>\n  !!value && value[$node] === value\n\n/** Get the owner's `Animated` node. */\nexport const getAnimated = <T = any>(owner: any): Animated<T> | undefined =>\n  owner && owner[$node]\n\n/** Set the owner's `Animated` node. */\nexport const setAnimated = (owner: any, node: Animated) =>\n  defineHidden(owner, $node, node)\n\n/** Get every `AnimatedValue` in the owner's `Animated` node. */\nexport const getPayload = (owner: any): AnimatedValue[] | undefined =>\n  owner && owner[$node] && owner[$node].getPayload()\n\nexport abstract class Animated<T = any> {\n  /** The cache of animated values */\n  protected payload?: Payload\n\n  constructor() {\n    // This makes \"isAnimated\" return true.\n    setAnimated(this, this)\n  }\n\n  /** Get the current value. Pass `true` for only animated values. */\n  abstract getValue(animated?: boolean): T\n\n  /** Set the current value. Returns `true` if the value changed. */\n  abstract setValue(value: T): boolean | void\n\n  /** Reset any animation state. */\n  abstract reset(goal?: T): void\n\n  /** Get every `AnimatedValue` used by this node. */\n  getPayload(): Payload {\n    return this.payload || []\n  }\n}\n\nexport type Payload = readonly AnimatedValue[]\n", "import { is } from '@react-spring/shared'\nimport { Animated, Payload } from './Animated'\n\n/** An animated number or a native attribute value */\nexport class AnimatedValue<T = any> extends Animated {\n  done = true\n  elapsedTime!: number\n  lastPosition!: number\n  lastVelocity?: number | null\n  v0?: number | null\n  durationProgress = 0\n\n  constructor(protected _value: T) {\n    super()\n    if (is.num(this._value)) {\n      this.lastPosition = this._value\n    }\n  }\n\n  /** @internal */\n  static create(value: any) {\n    return new AnimatedValue(value)\n  }\n\n  getPayload(): Payload {\n    return [this]\n  }\n\n  getValue() {\n    return this._value\n  }\n\n  setValue(value: T, step?: number) {\n    if (is.num(value)) {\n      this.lastPosition = value\n      if (step) {\n        value = (Math.round(value / step) * step) as any\n        if (this.done) {\n          this.lastPosition = value as any\n        }\n      }\n    }\n    if (this._value === value) {\n      return false\n    }\n    this._value = value\n    return true\n  }\n\n  reset() {\n    const { done } = this\n    this.done = false\n    if (is.num(this._value)) {\n      this.elapsedTime = 0\n      this.durationProgress = 0\n      this.lastPosition = this._value\n      if (done) this.lastVelocity = null\n      this.v0 = null\n    }\n  }\n}\n", "import { AnimatedValue } from './AnimatedValue'\nimport { is, createInterpolator } from '@react-spring/shared'\n\ntype Value = string | number\n\nexport class AnimatedString extends AnimatedValue<Value> {\n  protected declare _value: number\n  protected _string: string | null = null\n  protected _toString: (input: number) => string\n\n  constructor(value: string) {\n    super(0)\n    this._toString = createInterpolator({\n      output: [value, value],\n    })\n  }\n\n  /** @internal */\n  static create(value: string) {\n    return new AnimatedString(value)\n  }\n\n  getValue() {\n    const value = this._string\n    return value == null ? (this._string = this._toString(this._value)) : value\n  }\n\n  setValue(value: Value) {\n    if (is.str(value)) {\n      if (value == this._string) {\n        return false\n      }\n      this._string = value\n      this._value = 1\n    } else if (super.setValue(value)) {\n      this._string = null\n    } else {\n      return false\n    }\n    return true\n  }\n\n  reset(goal?: string) {\n    if (goal) {\n      this._toString = createInterpolator({\n        output: [this.getValue(), goal],\n      })\n    }\n    this._value = 0\n    super.reset()\n  }\n}\n", "import { isAnimatedString } from '@react-spring/shared'\nimport { AnimatedObject } from './AnimatedObject'\nimport { AnimatedString } from './AnimatedString'\nimport { AnimatedValue } from './AnimatedValue'\n\ntype Value = number | string\ntype Source = AnimatedValue<Value>[]\n\n/** An array of animated nodes */\nexport class AnimatedArray<\n  T extends ReadonlyArray<Value> = Value[],\n> extends AnimatedObject {\n  protected declare source: Source\n  constructor(source: T) {\n    super(source)\n  }\n\n  /** @internal */\n  static create<T extends ReadonlyArray<Value>>(source: T) {\n    return new AnimatedArray(source)\n  }\n\n  getValue(): T {\n    return this.source.map(node => node.getValue()) as any\n  }\n\n  setValue(source: T) {\n    const payload = this.getPayload()\n    // Reuse the payload when lengths are equal.\n    if (source.length == payload.length) {\n      return payload.map((node, i) => node.setValue(source[i])).some(Boolean)\n    }\n    // Remake the payload when length changes.\n    super.setValue(source.map(makeAnimated))\n    return true\n  }\n}\n\nfunction makeAnimated(value: any) {\n  const nodeType = isAnimatedString(value) ? AnimatedString : AnimatedValue\n  return nodeType.create(value)\n}\n", "import { Lookup } from '@react-spring/types'\nimport {\n  each,\n  eachProp,\n  getFluidValue,\n  hasFluidValue,\n} from '@react-spring/shared'\nimport { Animated, isAnimated, getPayload } from './Animated'\nimport { AnimatedValue } from './AnimatedValue'\nimport { TreeContext } from './context'\n\n/** An object containing `Animated` nodes */\nexport class AnimatedObject extends Animated {\n  constructor(protected source: Lookup) {\n    super()\n    this.setValue(source)\n  }\n\n  getValue(animated?: boolean) {\n    const values: Lookup = {}\n    eachProp(this.source, (source, key) => {\n      if (isAnimated(source)) {\n        values[key] = source.getValue(animated)\n      } else if (hasFluidValue(source)) {\n        values[key] = getFluidValue(source)\n      } else if (!animated) {\n        values[key] = source\n      }\n    })\n    return values\n  }\n\n  /** Replace the raw object data */\n  setValue(source: Lookup) {\n    this.source = source\n    this.payload = this._makePayload(source)\n  }\n\n  reset() {\n    if (this.payload) {\n      each(this.payload, node => node.reset())\n    }\n  }\n\n  /** Create a payload set. */\n  protected _makePayload(source: Lookup) {\n    if (source) {\n      const payload = new Set<AnimatedValue>()\n      eachProp(source, this._addToPayload, payload)\n      return Array.from(payload)\n    }\n  }\n\n  /** Add to a payload set. */\n  protected _addToPayload(this: Set<AnimatedValue>, source: any) {\n    if (TreeContext.dependencies && hasFluidValue(source)) {\n      TreeContext.dependencies.add(source)\n    }\n    const payload = getPayload(source)\n    if (payload) {\n      each(payload, node => this.add(node))\n    }\n  }\n}\n", "import { FluidValue } from '@react-spring/shared'\n\nexport type TreeContext = {\n  /**\n   * Any animated values found when updating the payload of an `AnimatedObject`\n   * are also added to this `Set` to be observed by an animated component.\n   */\n  dependencies: Set<FluidValue> | null\n}\n\nexport const TreeContext: TreeContext = { dependencies: null }\n", "import { is, isAnimatedString } from '@react-spring/shared'\nimport { AnimatedType } from './types'\nimport { AnimatedArray } from './AnimatedArray'\nimport { AnimatedString } from './AnimatedString'\nimport { AnimatedValue } from './AnimatedValue'\nimport { getAnimated } from './Animated'\n\n/** Return the `Animated` node constructor for a given value */\nexport function getAnimatedType(value: any): AnimatedType {\n  const parentNode = getAnimated(value)\n  return parentNode\n    ? (parentNode.constructor as any)\n    : is.arr(value)\n      ? AnimatedArray\n      : isAnimatedString(value)\n        ? AnimatedString\n        : AnimatedValue\n}\n", "import { Lookup } from '@react-spring/types'\nimport { is, eachProp } from '@react-spring/shared'\nimport { AnimatableComponent, withAnimated } from './withAnimated'\nimport { Animated } from './Animated'\nimport { AnimatedObject } from './AnimatedObject'\n\nexport interface HostConfig {\n  /** Provide custom logic for native updates */\n  applyAnimatedValues: (node: any, props: Lookup) => boolean | void\n  /** Wrap the `style` prop with an animated node */\n  createAnimatedStyle: (style: Lookup) => Animated\n  /** Intercept props before they're passed to an animated component */\n  getComponentProps: (props: Lookup) => typeof props\n}\n\n// A stub type that gets replaced by @react-spring/web and others.\ntype WithAnimated = {\n  (Component: AnimatableComponent): any\n  [key: string]: any\n}\n\n// For storing the animated version on the original component\nconst cacheKey = Symbol.for('AnimatedComponent')\n\nexport const createHost = (\n  components: AnimatableComponent[] | { [key: string]: AnimatableComponent },\n  {\n    applyAnimatedValues = () => false,\n    createAnimatedStyle = style => new AnimatedObject(style),\n    getComponentProps = props => props,\n  }: Partial<HostConfig> = {}\n) => {\n  const hostConfig: HostConfig = {\n    applyAnimatedValues,\n    createAnimatedStyle,\n    getComponentProps,\n  }\n\n  const animated: WithAnimated = (Component: any) => {\n    const displayName = getDisplayName(Component) || 'Anonymous'\n\n    if (is.str(Component)) {\n      Component =\n        animated[Component] ||\n        (animated[Component] = withAnimated(Component, hostConfig))\n    } else {\n      Component =\n        Component[cacheKey] ||\n        (Component[cacheKey] = withAnimated(Component, hostConfig))\n    }\n\n    Component.displayName = `Animated(${displayName})`\n    return Component\n  }\n\n  eachProp(components, (Component, key) => {\n    if (is.arr(components)) {\n      key = getDisplayName(Component)!\n    }\n    animated[key] = animated(Component)\n  })\n\n  return {\n    animated,\n  }\n}\n\nconst getDisplayName = (arg: AnimatableComponent) =>\n  is.str(arg)\n    ? arg\n    : arg && is.str(arg.displayName)\n      ? arg.displayName\n      : (is.fun(arg) && arg.name) || null\n", "import * as React from 'react'\nimport { forwardRef, useRef, Ref, useCallback, useEffect } from 'react'\nimport {\n  is,\n  each,\n  raf,\n  useForceUpdate,\n  useOnce,\n  FluidEvent,\n  FluidValue,\n  addFluidObserver,\n  removeFluidObserver,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\nimport { ElementType } from '@react-spring/types'\n\nimport { AnimatedObject } from './AnimatedObject'\nimport { TreeContext } from './context'\nimport { HostConfig } from './createHost'\n\nexport type AnimatableComponent = string | Exclude<ElementType, string>\n\nexport const withAnimated = (Component: any, host: HostConfig) => {\n  const hasInstance: boolean =\n    // Function components must use \"forwardRef\" to avoid being\n    // re-rendered on every animation frame.\n    !is.fun(Component) ||\n    (Component.prototype && Component.prototype.isReactComponent)\n\n  return forwardRef((givenProps: any, givenRef: Ref<any>) => {\n    const instanceRef = useRef<any>(null)\n\n    // The `hasInstance` value is constant, so we can safely avoid\n    // the `useCallback` invocation when `hasInstance` is false.\n    const ref =\n      hasInstance &&\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useCallback(\n        (value: any) => {\n          instanceRef.current = updateRef(givenRef, value)\n        },\n        [givenRef]\n      )\n\n    const [props, deps] = getAnimatedState(givenProps, host)\n\n    const forceUpdate = useForceUpdate()\n\n    const callback = () => {\n      const instance = instanceRef.current\n      if (hasInstance && !instance) {\n        // Either this component was unmounted before changes could be\n        // applied, or the wrapped component forgot to forward its ref.\n        return\n      }\n\n      const didUpdate = instance\n        ? host.applyAnimatedValues(instance, props.getValue(true))\n        : false\n\n      // Re-render the component when native updates fail.\n      if (didUpdate === false) {\n        forceUpdate()\n      }\n    }\n\n    const observer = new PropsObserver(callback, deps)\n\n    const observerRef = useRef<PropsObserver>()\n    useIsomorphicLayoutEffect(() => {\n      observerRef.current = observer\n\n      // Observe the latest dependencies.\n      each(deps, dep => addFluidObserver(dep, observer))\n\n      return () => {\n        // Stop observing previous dependencies.\n        if (observerRef.current) {\n          each(observerRef.current.deps, dep =>\n            removeFluidObserver(dep, observerRef.current!)\n          )\n          raf.cancel(observerRef.current.update)\n        }\n      }\n    })\n\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    useEffect(callback, [])\n    // Stop observing on unmount.\n    useOnce(() => () => {\n      const observer = observerRef.current!\n      each(observer.deps, dep => removeFluidObserver(dep, observer))\n    })\n\n    const usedProps = host.getComponentProps(props.getValue())\n    return <Component {...usedProps} ref={ref} />\n  })\n}\n\nclass PropsObserver {\n  constructor(\n    readonly update: () => void,\n    readonly deps: Set<FluidValue>\n  ) {}\n  eventObserved(event: FluidEvent) {\n    if (event.type == 'change') {\n      raf.write(this.update)\n    }\n  }\n}\n\ntype AnimatedState = [props: AnimatedObject, dependencies: Set<FluidValue>]\n\nfunction getAnimatedState(props: any, host: HostConfig): AnimatedState {\n  const dependencies = new Set<FluidValue>()\n  TreeContext.dependencies = dependencies\n\n  // Search the style for dependencies.\n  if (props.style)\n    props = {\n      ...props,\n      style: host.createAnimatedStyle(props.style),\n    }\n\n  // Search the props for dependencies.\n  props = new AnimatedObject(props)\n\n  TreeContext.dependencies = null\n  return [props, dependencies]\n}\n\nfunction updateRef<T>(ref: Ref<T>, value: T) {\n  if (ref) {\n    if (is.fun(ref)) ref(value)\n    else (ref as any).current = value\n  }\n  return value\n}\n"], "mappings": ";AAAA,SAASA,YAAA,QAAoB;AAG7B,IAAMC,KAAA,GAAaC,MAAA,CAAOC,GAAA,CAAI,eAAe;AAEtC,IAAMC,UAAA,GAAuBC,KAAA,IAClC,CAAC,CAACA,KAAA,IAASA,KAAA,CAAMJ,KAAK,MAAMI,KAAA;AAGvB,IAAMC,WAAA,GAAwBC,KAAA,IACnCA,KAAA,IAASA,KAAA,CAAMN,KAAK;AAGf,IAAMO,WAAA,GAAcA,CAACD,KAAA,EAAYE,IAAA,KACtCT,YAAA,CAAaO,KAAA,EAAON,KAAA,EAAOQ,IAAI;AAG1B,IAAMC,UAAA,GAAcH,KAAA,IACzBA,KAAA,IAASA,KAAA,CAAMN,KAAK,KAAKM,KAAA,CAAMN,KAAK,EAAES,UAAA,CAAW;AAE5C,IAAeC,QAAA,GAAf,MAAiC;EAItCC,YAAA,EAAc;IAEZJ,WAAA,CAAY,MAAM,IAAI;EACxB;EAAA;EAYAE,WAAA,EAAsB;IACpB,OAAO,KAAKG,OAAA,IAAW,EAAC;EAC1B;AACF;;;AC1CA,SAASC,EAAA,QAAU;AAIZ,IAAMC,aAAA,GAAN,cAAqCJ,QAAA,CAAS;EAQnDC,YAAsBI,MAAA,EAAW;IAC/B,MAAM;IADc,KAAAA,MAAA,GAAAA,MAAA;IAPtB,KAAAC,IAAA,GAAO;IAKP,KAAAC,gBAAA,GAAmB;IAIjB,IAAIJ,EAAA,CAAGK,GAAA,CAAI,KAAKH,MAAM,GAAG;MACvB,KAAKI,YAAA,GAAe,KAAKJ,MAAA;IAC3B;EACF;EAAA;EAGA,OAAOK,OAAOhB,KAAA,EAAY;IACxB,OAAO,IAAIU,aAAA,CAAcV,KAAK;EAChC;EAEAK,WAAA,EAAsB;IACpB,OAAO,CAAC,IAAI;EACd;EAEAY,SAAA,EAAW;IACT,OAAO,KAAKN,MAAA;EACd;EAEAO,SAASlB,KAAA,EAAUmB,IAAA,EAAe;IAChC,IAAIV,EAAA,CAAGK,GAAA,CAAId,KAAK,GAAG;MACjB,KAAKe,YAAA,GAAef,KAAA;MACpB,IAAImB,IAAA,EAAM;QACRnB,KAAA,GAASoB,IAAA,CAAKC,KAAA,CAAMrB,KAAA,GAAQmB,IAAI,IAAIA,IAAA;QACpC,IAAI,KAAKP,IAAA,EAAM;UACb,KAAKG,YAAA,GAAef,KAAA;QACtB;MACF;IACF;IACA,IAAI,KAAKW,MAAA,KAAWX,KAAA,EAAO;MACzB,OAAO;IACT;IACA,KAAKW,MAAA,GAASX,KAAA;IACd,OAAO;EACT;EAEAsB,MAAA,EAAQ;IACN,MAAM;MAAEV;IAAK,IAAI;IACjB,KAAKA,IAAA,GAAO;IACZ,IAAIH,EAAA,CAAGK,GAAA,CAAI,KAAKH,MAAM,GAAG;MACvB,KAAKY,WAAA,GAAc;MACnB,KAAKV,gBAAA,GAAmB;MACxB,KAAKE,YAAA,GAAe,KAAKJ,MAAA;MACzB,IAAIC,IAAA,EAAM,KAAKY,YAAA,GAAe;MAC9B,KAAKC,EAAA,GAAK;IACZ;EACF;AACF;;;AC3DA,SAAShB,EAAA,IAAAiB,GAAA,EAAIC,kBAAA,QAA0B;AAIhC,IAAMC,cAAA,GAAN,cAA6BlB,aAAA,CAAqB;EAKvDH,YAAYP,KAAA,EAAe;IACzB,MAAM,CAAC;IAJT,KAAU6B,OAAA,GAAyB;IAKjC,KAAKC,SAAA,GAAYH,kBAAA,CAAmB;MAClCI,MAAA,EAAQ,CAAC/B,KAAA,EAAOA,KAAK;IACvB,CAAC;EACH;EAAA;EAGA,OAAOgB,OAAOhB,KAAA,EAAe;IAC3B,OAAO,IAAI4B,cAAA,CAAe5B,KAAK;EACjC;EAEAiB,SAAA,EAAW;IACT,MAAMjB,KAAA,GAAQ,KAAK6B,OAAA;IACnB,OAAO7B,KAAA,IAAS,OAAQ,KAAK6B,OAAA,GAAU,KAAKC,SAAA,CAAU,KAAKnB,MAAM,IAAKX,KAAA;EACxE;EAEAkB,SAASlB,KAAA,EAAc;IACrB,IAAI0B,GAAA,CAAGM,GAAA,CAAIhC,KAAK,GAAG;MACjB,IAAIA,KAAA,IAAS,KAAK6B,OAAA,EAAS;QACzB,OAAO;MACT;MACA,KAAKA,OAAA,GAAU7B,KAAA;MACf,KAAKW,MAAA,GAAS;IAChB,WAAW,MAAMO,QAAA,CAASlB,KAAK,GAAG;MAChC,KAAK6B,OAAA,GAAU;IACjB,OAAO;MACL,OAAO;IACT;IACA,OAAO;EACT;EAEAP,MAAMW,IAAA,EAAe;IACnB,IAAIA,IAAA,EAAM;MACR,KAAKH,SAAA,GAAYH,kBAAA,CAAmB;QAClCI,MAAA,EAAQ,CAAC,KAAKd,QAAA,CAAS,GAAGgB,IAAI;MAChC,CAAC;IACH;IACA,KAAKtB,MAAA,GAAS;IACd,MAAMW,KAAA,CAAM;EACd;AACF;;;ACnDA,SAASY,gBAAA,QAAwB;;;ACCjC,SACEC,IAAA,EACAC,QAAA,EACAC,aAAA,EACAC,aAAA,QACK;;;ACIA,IAAMC,WAAA,GAA2B;EAAEC,YAAA,EAAc;AAAK;;;ADEtD,IAAMC,cAAA,GAAN,cAA6BnC,QAAA,CAAS;EAC3CC,YAAsBmC,MAAA,EAAgB;IACpC,MAAM;IADc,KAAAA,MAAA,GAAAA,MAAA;IAEpB,KAAKxB,QAAA,CAASwB,MAAM;EACtB;EAEAzB,SAAS0B,QAAA,EAAoB;IAC3B,MAAMC,MAAA,GAAiB,CAAC;IACxBR,QAAA,CAAS,KAAKM,MAAA,EAAQ,CAACA,MAAA,EAAQG,GAAA,KAAQ;MACrC,IAAI9C,UAAA,CAAW2C,MAAM,GAAG;QACtBE,MAAA,CAAOC,GAAG,IAAIH,MAAA,CAAOzB,QAAA,CAAS0B,QAAQ;MACxC,WAAWL,aAAA,CAAcI,MAAM,GAAG;QAChCE,MAAA,CAAOC,GAAG,IAAIR,aAAA,CAAcK,MAAM;MACpC,WAAW,CAACC,QAAA,EAAU;QACpBC,MAAA,CAAOC,GAAG,IAAIH,MAAA;MAChB;IACF,CAAC;IACD,OAAOE,MAAA;EACT;EAAA;EAGA1B,SAASwB,MAAA,EAAgB;IACvB,KAAKA,MAAA,GAASA,MAAA;IACd,KAAKlC,OAAA,GAAU,KAAKsC,YAAA,CAAaJ,MAAM;EACzC;EAEApB,MAAA,EAAQ;IACN,IAAI,KAAKd,OAAA,EAAS;MAChB2B,IAAA,CAAK,KAAK3B,OAAA,EAASJ,IAAA,IAAQA,IAAA,CAAKkB,KAAA,CAAM,CAAC;IACzC;EACF;EAAA;EAGUwB,aAAaJ,MAAA,EAAgB;IACrC,IAAIA,MAAA,EAAQ;MACV,MAAMlC,OAAA,GAAU,mBAAIuC,GAAA,CAAmB;MACvCX,QAAA,CAASM,MAAA,EAAQ,KAAKM,aAAA,EAAexC,OAAO;MAC5C,OAAOyC,KAAA,CAAMC,IAAA,CAAK1C,OAAO;IAC3B;EACF;EAAA;EAGUwC,cAAwCN,MAAA,EAAa;IAC7D,IAAIH,WAAA,CAAYC,YAAA,IAAgBF,aAAA,CAAcI,MAAM,GAAG;MACrDH,WAAA,CAAYC,YAAA,CAAaW,GAAA,CAAIT,MAAM;IACrC;IACA,MAAMlC,OAAA,GAAUH,UAAA,CAAWqC,MAAM;IACjC,IAAIlC,OAAA,EAAS;MACX2B,IAAA,CAAK3B,OAAA,EAASJ,IAAA,IAAQ,KAAK+C,GAAA,CAAI/C,IAAI,CAAC;IACtC;EACF;AACF;;;ADtDO,IAAMgD,aAAA,GAAN,cAEGX,cAAA,CAAe;EAEvBlC,YAAYmC,MAAA,EAAW;IACrB,MAAMA,MAAM;EACd;EAAA;EAGA,OAAO1B,OAAuC0B,MAAA,EAAW;IACvD,OAAO,IAAIU,aAAA,CAAcV,MAAM;EACjC;EAEAzB,SAAA,EAAc;IACZ,OAAO,KAAKyB,MAAA,CAAOW,GAAA,CAAIjD,IAAA,IAAQA,IAAA,CAAKa,QAAA,CAAS,CAAC;EAChD;EAEAC,SAASwB,MAAA,EAAW;IAClB,MAAMlC,OAAA,GAAU,KAAKH,UAAA,CAAW;IAEhC,IAAIqC,MAAA,CAAOY,MAAA,IAAU9C,OAAA,CAAQ8C,MAAA,EAAQ;MACnC,OAAO9C,OAAA,CAAQ6C,GAAA,CAAI,CAACjD,IAAA,EAAMmD,CAAA,KAAMnD,IAAA,CAAKc,QAAA,CAASwB,MAAA,CAAOa,CAAC,CAAC,CAAC,EAAEC,IAAA,CAAKC,OAAO;IACxE;IAEA,MAAMvC,QAAA,CAASwB,MAAA,CAAOW,GAAA,CAAIK,YAAY,CAAC;IACvC,OAAO;EACT;AACF;AAEA,SAASA,aAAa1D,KAAA,EAAY;EAChC,MAAM2D,QAAA,GAAWzB,gBAAA,CAAiBlC,KAAK,IAAI4B,cAAA,GAAiBlB,aAAA;EAC5D,OAAOiD,QAAA,CAAS3C,MAAA,CAAOhB,KAAK;AAC9B;;;AGzCA,SAASS,EAAA,IAAAmD,GAAA,EAAI1B,gBAAA,IAAA2B,iBAAA,QAAwB;AAQ9B,SAASC,gBAAgB9D,KAAA,EAA0B;EACxD,MAAM+D,UAAA,GAAa9D,WAAA,CAAYD,KAAK;EACpC,OAAO+D,UAAA,GACFA,UAAA,CAAWxD,WAAA,GACZqD,GAAA,CAAGI,GAAA,CAAIhE,KAAK,IACVoD,aAAA,GACAS,iBAAA,CAAiB7D,KAAK,IACpB4B,cAAA,GACAlB,aAAA;AACV;;;AChBA,SAASD,EAAA,IAAAwD,GAAA,EAAI7B,QAAA,IAAA8B,SAAA,QAAgB;;;ACD7B,YAAYC,KAAA,MAAW;AACvB,SAASC,UAAA,EAAYC,MAAA,EAAaC,WAAA,EAAaC,SAAA,QAAiB;AAChE,SACE9D,EAAA,IAAA+D,GAAA,EACArC,IAAA,IAAAsC,KAAA,EACAC,GAAA,EACAC,cAAA,EACAC,OAAA,EAGAC,gBAAA,EACAC,mBAAA,EACAC,yBAAA,QACK;AASA,IAAMC,YAAA,GAAeA,CAACC,SAAA,EAAgBC,IAAA,KAAqB;EAChE,MAAMC,WAAA;EAAA;EAAA;EAGJ,CAACX,GAAA,CAAGY,GAAA,CAAIH,SAAS,KAChBA,SAAA,CAAUI,SAAA,IAAaJ,SAAA,CAAUI,SAAA,CAAUC,gBAAA;EAE9C,OAAOlB,UAAA,CAAW,CAACmB,UAAA,EAAiBC,QAAA,KAAuB;IACzD,MAAMC,WAAA,GAAcpB,MAAA,CAAY,IAAI;IAIpC,MAAMqB,GAAA,GACJP,WAAA;IAAA;IAEAb,WAAA,CACGtE,KAAA,IAAe;MACdyF,WAAA,CAAYE,OAAA,GAAUC,SAAA,CAAUJ,QAAA,EAAUxF,KAAK;IACjD,GACA,CAACwF,QAAQ,CACX;IAEF,MAAM,CAACK,KAAA,EAAOC,IAAI,IAAIC,gBAAA,CAAiBR,UAAA,EAAYL,IAAI;IAEvD,MAAMc,WAAA,GAAcrB,cAAA,CAAe;IAEnC,MAAMsB,QAAA,GAAWA,CAAA,KAAM;MACrB,MAAMC,QAAA,GAAWT,WAAA,CAAYE,OAAA;MAC7B,IAAIR,WAAA,IAAe,CAACe,QAAA,EAAU;QAG5B;MACF;MAEA,MAAMC,SAAA,GAAYD,QAAA,GACdhB,IAAA,CAAKkB,mBAAA,CAAoBF,QAAA,EAAUL,KAAA,CAAM5E,QAAA,CAAS,IAAI,CAAC,IACvD;MAGJ,IAAIkF,SAAA,KAAc,OAAO;QACvBH,WAAA,CAAY;MACd;IACF;IAEA,MAAMK,QAAA,GAAW,IAAIC,aAAA,CAAcL,QAAA,EAAUH,IAAI;IAEjD,MAAMS,WAAA,GAAclC,MAAA,CAAsB;IAC1CU,yBAAA,CAA0B,MAAM;MAC9BwB,WAAA,CAAYZ,OAAA,GAAUU,QAAA;MAGtB5B,KAAA,CAAKqB,IAAA,EAAMU,GAAA,IAAO3B,gBAAA,CAAiB2B,GAAA,EAAKH,QAAQ,CAAC;MAEjD,OAAO,MAAM;QAEX,IAAIE,WAAA,CAAYZ,OAAA,EAAS;UACvBlB,KAAA,CAAK8B,WAAA,CAAYZ,OAAA,CAAQG,IAAA,EAAMU,GAAA,IAC7B1B,mBAAA,CAAoB0B,GAAA,EAAKD,WAAA,CAAYZ,OAAQ,CAC/C;UACAjB,GAAA,CAAI+B,MAAA,CAAOF,WAAA,CAAYZ,OAAA,CAAQe,MAAM;QACvC;MACF;IACF,CAAC;IAGDnC,SAAA,CAAU0B,QAAA,EAAU,EAAE;IAEtBrB,OAAA,CAAQ,MAAM,MAAM;MAClB,MAAM+B,SAAA,GAAWJ,WAAA,CAAYZ,OAAA;MAC7BlB,KAAA,CAAKkC,SAAA,CAASb,IAAA,EAAMU,GAAA,IAAO1B,mBAAA,CAAoB0B,GAAA,EAAKG,SAAQ,CAAC;IAC/D,CAAC;IAED,MAAMC,SAAA,GAAY1B,IAAA,CAAK2B,iBAAA,CAAkBhB,KAAA,CAAM5E,QAAA,CAAS,CAAC;IACzD,OAAO,eAAAkD,KAAA,CAAA2C,aAAA,CAAC7B,SAAA;MAAW,GAAG2B,SAAA;MAAWlB;IAAA,CAAU;EAC7C,CAAC;AACH;AAEA,IAAMY,aAAA,GAAN,MAAoB;EAClB/F,YACWmG,MAAA,EACAZ,IAAA,EACT;IAFS,KAAAY,MAAA,GAAAA,MAAA;IACA,KAAAZ,IAAA,GAAAA,IAAA;EACR;EACHiB,cAAcC,KAAA,EAAmB;IAC/B,IAAIA,KAAA,CAAMC,IAAA,IAAQ,UAAU;MAC1BvC,GAAA,CAAIwC,KAAA,CAAM,KAAKR,MAAM;IACvB;EACF;AACF;AAIA,SAASX,iBAAiBF,KAAA,EAAYX,IAAA,EAAiC;EACrE,MAAM1C,YAAA,GAAe,mBAAIO,GAAA,CAAgB;EACzCR,WAAA,CAAYC,YAAA,GAAeA,YAAA;EAG3B,IAAIqD,KAAA,CAAMsB,KAAA,EACRtB,KAAA,GAAQ;IACN,GAAGA,KAAA;IACHsB,KAAA,EAAOjC,IAAA,CAAKkC,mBAAA,CAAoBvB,KAAA,CAAMsB,KAAK;EAC7C;EAGFtB,KAAA,GAAQ,IAAIpD,cAAA,CAAeoD,KAAK;EAEhCtD,WAAA,CAAYC,YAAA,GAAe;EAC3B,OAAO,CAACqD,KAAA,EAAOrD,YAAY;AAC7B;AAEA,SAASoD,UAAaF,GAAA,EAAa1F,KAAA,EAAU;EAC3C,IAAI0F,GAAA,EAAK;IACP,IAAIlB,GAAA,CAAGY,GAAA,CAAIM,GAAG,GAAGA,GAAA,CAAI1F,KAAK,OACpB0F,GAAA,CAAYC,OAAA,GAAU3F,KAAA;EAC9B;EACA,OAAOA,KAAA;AACT;;;ADnHA,IAAMqH,QAAA,GAAWxH,MAAA,CAAOC,GAAA,CAAI,mBAAmB;AAExC,IAAMwH,UAAA,GAAaA,CACxBC,UAAA,EACA;EACEnB,mBAAA,GAAsBA,CAAA,KAAM;EAC5BgB,mBAAA,GAAsBD,KAAA,IAAS,IAAI1E,cAAA,CAAe0E,KAAK;EACvDN,iBAAA,GAAoBhB,KAAA,IAASA;AAC/B,IAAyB,CAAC,MACvB;EACH,MAAM2B,UAAA,GAAyB;IAC7BpB,mBAAA;IACAgB,mBAAA;IACAP;EACF;EAEA,MAAMlE,QAAA,GAA0BsC,SAAA,IAAmB;IACjD,MAAMwC,WAAA,GAAcC,cAAA,CAAezC,SAAS,KAAK;IAEjD,IAAIhB,GAAA,CAAGjC,GAAA,CAAIiD,SAAS,GAAG;MACrBA,SAAA,GACEtC,QAAA,CAASsC,SAAS,MACjBtC,QAAA,CAASsC,SAAS,IAAID,YAAA,CAAaC,SAAA,EAAWuC,UAAU;IAC7D,OAAO;MACLvC,SAAA,GACEA,SAAA,CAAUoC,QAAQ,MACjBpC,SAAA,CAAUoC,QAAQ,IAAIrC,YAAA,CAAaC,SAAA,EAAWuC,UAAU;IAC7D;IAEAvC,SAAA,CAAUwC,WAAA,GAAc,YAAYA,WAAA;IACpC,OAAOxC,SAAA;EACT;EAEAf,SAAA,CAASqD,UAAA,EAAY,CAACtC,SAAA,EAAWpC,GAAA,KAAQ;IACvC,IAAIoB,GAAA,CAAGD,GAAA,CAAIuD,UAAU,GAAG;MACtB1E,GAAA,GAAM6E,cAAA,CAAezC,SAAS;IAChC;IACAtC,QAAA,CAASE,GAAG,IAAIF,QAAA,CAASsC,SAAS;EACpC,CAAC;EAED,OAAO;IACLtC;EACF;AACF;AAEA,IAAM+E,cAAA,GAAkBC,GAAA,IACtB1D,GAAA,CAAGjC,GAAA,CAAI2F,GAAG,IACNA,GAAA,GACAA,GAAA,IAAO1D,GAAA,CAAGjC,GAAA,CAAI2F,GAAA,CAAIF,WAAW,IAC3BE,GAAA,CAAIF,WAAA,GACHxD,GAAA,CAAGmB,GAAA,CAAIuC,GAAG,KAAKA,GAAA,CAAIC,IAAA,IAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}