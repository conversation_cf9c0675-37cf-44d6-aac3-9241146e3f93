{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  IframeTransport = require('../iframe'),\n  objectUtils = require('../../utils/object');\nmodule.exports = function (transport) {\n  function IframeWrapTransport(transUrl, baseUrl) {\n    IframeTransport.call(this, transport.transportName, transUrl, baseUrl);\n  }\n  inherits(IframeWrapTransport, IframeTransport);\n  IframeWrapTransport.enabled = function (url, info) {\n    if (!global.document) {\n      return false;\n    }\n    var iframeInfo = objectUtils.extend({}, info);\n    iframeInfo.sameOrigin = true;\n    return transport.enabled(iframeInfo) && IframeTransport.enabled();\n  };\n  IframeWrapTransport.transportName = 'iframe-' + transport.transportName;\n  IframeWrapTransport.needBody = true;\n  IframeWrapTransport.roundTrips = IframeTransport.roundTrips + transport.roundTrips - 1; // html, javascript (2) + transport - no CORS (1)\n\n  IframeWrapTransport.facadeTransport = transport;\n  return IframeWrapTransport;\n};", "map": {"version": 3, "names": ["inherits", "require", "IframeTransport", "objectUtils", "module", "exports", "transport", "IframeWrapTransport", "transUrl", "baseUrl", "call", "transportName", "enabled", "url", "info", "global", "document", "iframeInfo", "extend", "<PERSON><PERSON><PERSON><PERSON>", "needBody", "roundTrips", "facadeTransport"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/lib/iframe-wrap.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , IframeTransport = require('../iframe')\n  , objectUtils = require('../../utils/object')\n  ;\n\nmodule.exports = function(transport) {\n\n  function IframeWrapTransport(transUrl, baseUrl) {\n    IframeTransport.call(this, transport.transportName, transUrl, baseUrl);\n  }\n\n  inherits(IframeWrapTransport, IframeTransport);\n\n  IframeWrapTransport.enabled = function(url, info) {\n    if (!global.document) {\n      return false;\n    }\n\n    var iframeInfo = objectUtils.extend({}, info);\n    iframeInfo.sameOrigin = true;\n    return transport.enabled(iframeInfo) && IframeTransport.enabled();\n  };\n\n  IframeWrapTransport.transportName = 'iframe-' + transport.transportName;\n  IframeWrapTransport.needBody = true;\n  IframeWrapTransport.roundTrips = IframeTransport.roundTrips + transport.roundTrips - 1; // html, javascript (2) + transport - no CORS (1)\n\n  IframeWrapTransport.facadeTransport = transport;\n\n  return IframeWrapTransport;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,eAAe,GAAGD,OAAO,CAAC,WAAW,CAAC;EACtCE,WAAW,GAAGF,OAAO,CAAC,oBAAoB,CAAC;AAG/CG,MAAM,CAACC,OAAO,GAAG,UAASC,SAAS,EAAE;EAEnC,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;IAC9CP,eAAe,CAACQ,IAAI,CAAC,IAAI,EAAEJ,SAAS,CAACK,aAAa,EAAEH,QAAQ,EAAEC,OAAO,CAAC;EACxE;EAEAT,QAAQ,CAACO,mBAAmB,EAAEL,eAAe,CAAC;EAE9CK,mBAAmB,CAACK,OAAO,GAAG,UAASC,GAAG,EAAEC,IAAI,EAAE;IAChD,IAAI,CAACC,MAAM,CAACC,QAAQ,EAAE;MACpB,OAAO,KAAK;IACd;IAEA,IAAIC,UAAU,GAAGd,WAAW,CAACe,MAAM,CAAC,CAAC,CAAC,EAAEJ,IAAI,CAAC;IAC7CG,UAAU,CAACE,UAAU,GAAG,IAAI;IAC5B,OAAOb,SAAS,CAACM,OAAO,CAACK,UAAU,CAAC,IAAIf,eAAe,CAACU,OAAO,CAAC,CAAC;EACnE,CAAC;EAEDL,mBAAmB,CAACI,aAAa,GAAG,SAAS,GAAGL,SAAS,CAACK,aAAa;EACvEJ,mBAAmB,CAACa,QAAQ,GAAG,IAAI;EACnCb,mBAAmB,CAACc,UAAU,GAAGnB,eAAe,CAACmB,UAAU,GAAGf,SAAS,CAACe,UAAU,GAAG,CAAC,CAAC,CAAC;;EAExFd,mBAAmB,CAACe,eAAe,GAAGhB,SAAS;EAE/C,OAAOC,mBAAmB;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}