{"ast": null, "code": "// src/index.ts\nvar updateQueue = makeQueue();\nvar raf = fn => schedule(fn, updateQueue);\nvar writeQueue = makeQueue();\nraf.write = fn => schedule(fn, writeQueue);\nvar onStartQueue = makeQueue();\nraf.onStart = fn => schedule(fn, onStartQueue);\nvar onFrameQueue = makeQueue();\nraf.onFrame = fn => schedule(fn, onFrameQueue);\nvar onFinishQueue = makeQueue();\nraf.onFinish = fn => schedule(fn, onFinishQueue);\nvar timeouts = [];\nraf.setTimeout = (handler, ms) => {\n  const time = raf.now() + ms;\n  const cancel = () => {\n    const i = timeouts.findIndex(t => t.cancel == cancel);\n    if (~i) timeouts.splice(i, 1);\n    pendingCount -= ~i ? 1 : 0;\n  };\n  const timeout = {\n    time,\n    handler,\n    cancel\n  };\n  timeouts.splice(findTimeout(time), 0, timeout);\n  pendingCount += 1;\n  start();\n  return timeout;\n};\nvar findTimeout = time => ~(~timeouts.findIndex(t => t.time > time) || ~timeouts.length);\nraf.cancel = fn => {\n  onStartQueue.delete(fn);\n  onFrameQueue.delete(fn);\n  onFinishQueue.delete(fn);\n  updateQueue.delete(fn);\n  writeQueue.delete(fn);\n};\nraf.sync = fn => {\n  sync = true;\n  raf.batchedUpdates(fn);\n  sync = false;\n};\nraf.throttle = fn => {\n  let lastArgs;\n  function queuedFn() {\n    try {\n      fn(...lastArgs);\n    } finally {\n      lastArgs = null;\n    }\n  }\n  function throttled(...args) {\n    lastArgs = args;\n    raf.onStart(queuedFn);\n  }\n  throttled.handler = fn;\n  throttled.cancel = () => {\n    onStartQueue.delete(queuedFn);\n    lastArgs = null;\n  };\n  return throttled;\n};\nvar nativeRaf = typeof window != \"undefined\" ? window.requestAnimationFrame :\n// eslint-disable-next-line @typescript-eslint/no-empty-function\n() => {};\nraf.use = impl => nativeRaf = impl;\nraf.now = typeof performance != \"undefined\" ? () => performance.now() : Date.now;\nraf.batchedUpdates = fn => fn();\nraf.catch = console.error;\nraf.frameLoop = \"always\";\nraf.advance = () => {\n  if (raf.frameLoop !== \"demand\") {\n    console.warn(\"Cannot call the manual advancement of rafz whilst frameLoop is not set as demand\");\n  } else {\n    update();\n  }\n};\nvar ts = -1;\nvar pendingCount = 0;\nvar sync = false;\nfunction schedule(fn, queue) {\n  if (sync) {\n    queue.delete(fn);\n    fn(0);\n  } else {\n    queue.add(fn);\n    start();\n  }\n}\nfunction start() {\n  if (ts < 0) {\n    ts = 0;\n    if (raf.frameLoop !== \"demand\") {\n      nativeRaf(loop);\n    }\n  }\n}\nfunction stop() {\n  ts = -1;\n}\nfunction loop() {\n  if (~ts) {\n    nativeRaf(loop);\n    raf.batchedUpdates(update);\n  }\n}\nfunction update() {\n  const prevTs = ts;\n  ts = raf.now();\n  const count = findTimeout(ts);\n  if (count) {\n    eachSafely(timeouts.splice(0, count), t => t.handler());\n    pendingCount -= count;\n  }\n  if (!pendingCount) {\n    stop();\n    return;\n  }\n  onStartQueue.flush();\n  updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667);\n  onFrameQueue.flush();\n  writeQueue.flush();\n  onFinishQueue.flush();\n}\nfunction makeQueue() {\n  let next = /* @__PURE__ */new Set();\n  let current = next;\n  return {\n    add(fn) {\n      pendingCount += current == next && !next.has(fn) ? 1 : 0;\n      next.add(fn);\n    },\n    delete(fn) {\n      pendingCount -= current == next && next.has(fn) ? 1 : 0;\n      return next.delete(fn);\n    },\n    flush(arg) {\n      if (current.size) {\n        next = /* @__PURE__ */new Set();\n        pendingCount -= current.size;\n        eachSafely(current, fn => fn(arg) && next.add(fn));\n        pendingCount += next.size;\n        current = next;\n      }\n    }\n  };\n}\nfunction eachSafely(values, each) {\n  values.forEach(value => {\n    try {\n      each(value);\n    } catch (e) {\n      raf.catch(e);\n    }\n  });\n}\nvar __raf = {\n  /** The number of pending tasks */\n  count() {\n    return pendingCount;\n  },\n  /** Whether there's a raf update loop running */\n  isRunning() {\n    return ts >= 0;\n  },\n  /** Clear internal state. Never call from update loop! */\n  clear() {\n    ts = -1;\n    timeouts = [];\n    onStartQueue = makeQueue();\n    updateQueue = makeQueue();\n    onFrameQueue = makeQueue();\n    writeQueue = makeQueue();\n    onFinishQueue = makeQueue();\n    pendingCount = 0;\n  }\n};\nexport { __raf, raf };", "map": {"version": 3, "names": ["updateQueue", "makeQueue", "raf", "fn", "schedule", "writeQueue", "write", "onStartQueue", "onStart", "onFrameQueue", "onFrame", "onFinishQueue", "onFinish", "timeouts", "setTimeout", "handler", "ms", "time", "now", "cancel", "i", "findIndex", "t", "splice", "pendingCount", "timeout", "findTimeout", "start", "length", "delete", "sync", "batchedUpdates", "throttle", "lastArgs", "queuedFn", "throttled", "args", "nativeRaf", "window", "requestAnimationFrame", "use", "impl", "performance", "Date", "catch", "console", "error", "frameLoop", "advance", "warn", "update", "ts", "queue", "add", "loop", "stop", "prevTs", "count", "eachSafely", "flush", "Math", "min", "next", "Set", "current", "has", "arg", "size", "values", "each", "for<PERSON>ach", "value", "e", "__raf", "isRunning", "clear"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/rafz/src/index.ts"], "sourcesContent": ["import type {\n  FrameFn,\n  FrameUpdateFn,\n  NativeRaf,\n  <PERSON>fz,\n  Timeout,\n  Throttled,\n} from './types'\n\nexport type { FrameFn, FrameUpdateFn, Timeout, Throttled, Rafz }\n\nlet updateQueue = makeQueue<FrameUpdateFn>()\n\n/**\n * Schedule an update for next frame.\n * Your function can return `true` to repeat next frame.\n */\nexport const raf: Rafz = fn => schedule(fn, updateQueue)\n\nlet writeQueue = makeQueue<FrameFn>()\nraf.write = fn => schedule(fn, writeQueue)\n\nlet onStartQueue = makeQueue<FrameFn>()\nraf.onStart = fn => schedule(fn, onStartQueue)\n\nlet onFrameQueue = makeQueue<FrameFn>()\nraf.onFrame = fn => schedule(fn, onFrameQueue)\n\nlet onFinishQueue = makeQueue<FrameFn>()\nraf.onFinish = fn => schedule(fn, onFinishQueue)\n\nlet timeouts: Timeout[] = []\nraf.setTimeout = (handler, ms) => {\n  const time = raf.now() + ms\n  const cancel = () => {\n    const i = timeouts.findIndex(t => t.cancel == cancel)\n    if (~i) timeouts.splice(i, 1)\n    pendingCount -= ~i ? 1 : 0\n  }\n\n  const timeout: Timeout = { time, handler, cancel }\n  timeouts.splice(findTimeout(time), 0, timeout)\n  pendingCount += 1\n\n  start()\n  return timeout\n}\n\n/** Find the index where the given time is not greater. */\nconst findTimeout = (time: number) =>\n  ~(~timeouts.findIndex(t => t.time > time) || ~timeouts.length)\n\nraf.cancel = fn => {\n  onStartQueue.delete(fn)\n  onFrameQueue.delete(fn)\n  onFinishQueue.delete(fn)\n  updateQueue.delete(fn)\n  writeQueue.delete(fn)\n}\n\nraf.sync = fn => {\n  sync = true\n  raf.batchedUpdates(fn)\n  sync = false\n}\n\nraf.throttle = fn => {\n  let lastArgs: any\n  function queuedFn() {\n    try {\n      fn(...lastArgs)\n    } finally {\n      lastArgs = null\n    }\n  }\n  function throttled(...args: any) {\n    lastArgs = args\n    raf.onStart(queuedFn)\n  }\n  throttled.handler = fn\n  throttled.cancel = () => {\n    onStartQueue.delete(queuedFn)\n    lastArgs = null\n  }\n  return throttled as any\n}\n\nlet nativeRaf =\n  typeof window != 'undefined'\n    ? (window.requestAnimationFrame as NativeRaf)\n    : // eslint-disable-next-line @typescript-eslint/no-empty-function\n      () => {}\n\nraf.use = impl => (nativeRaf = impl)\nraf.now = typeof performance != 'undefined' ? () => performance.now() : Date.now\nraf.batchedUpdates = fn => fn()\nraf.catch = console.error\n\nraf.frameLoop = 'always'\n\nraf.advance = () => {\n  if (raf.frameLoop !== 'demand') {\n    console.warn(\n      'Cannot call the manual advancement of rafz whilst frameLoop is not set as demand'\n    )\n  } else {\n    update()\n  }\n}\n\n/** The most recent timestamp. */\nlet ts = -1\n\n/** The number of pending tasks  */\nlet pendingCount = 0\n\n/** When true, scheduling is disabled. */\nlet sync = false\n\nfunction schedule<T extends Function>(fn: T, queue: Queue<T>) {\n  if (sync) {\n    queue.delete(fn)\n    fn(0)\n  } else {\n    queue.add(fn)\n    start()\n  }\n}\n\nfunction start() {\n  if (ts < 0) {\n    ts = 0\n    if (raf.frameLoop !== 'demand') {\n      nativeRaf(loop)\n    }\n  }\n}\n\nfunction stop() {\n  ts = -1\n}\n\nfunction loop() {\n  if (~ts) {\n    nativeRaf(loop)\n    raf.batchedUpdates(update)\n  }\n}\n\nfunction update() {\n  const prevTs = ts\n  ts = raf.now()\n\n  // Flush timeouts whose time is up.\n  const count = findTimeout(ts)\n  if (count) {\n    eachSafely(timeouts.splice(0, count), t => t.handler())\n    pendingCount -= count\n  }\n\n  if (!pendingCount) {\n    stop()\n\n    return\n  }\n\n  onStartQueue.flush()\n  updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667)\n  onFrameQueue.flush()\n  writeQueue.flush()\n  onFinishQueue.flush()\n}\n\ninterface Queue<T extends Function = any> {\n  add: (fn: T) => void\n  delete: (fn: T) => boolean\n  flush: (arg?: any) => void\n}\n\nfunction makeQueue<T extends Function>(): Queue<T> {\n  let next = new Set<T>()\n  let current = next\n  return {\n    add(fn) {\n      pendingCount += current == next && !next.has(fn) ? 1 : 0\n      next.add(fn)\n    },\n    delete(fn) {\n      pendingCount -= current == next && next.has(fn) ? 1 : 0\n      return next.delete(fn)\n    },\n    flush(arg) {\n      if (current.size) {\n        next = new Set()\n        pendingCount -= current.size\n        eachSafely(current, fn => fn(arg) && next.add(fn))\n        pendingCount += next.size\n        current = next\n      }\n    },\n  }\n}\n\ninterface Eachable<T> {\n  forEach(cb: (value: T) => void): void\n}\n\nfunction eachSafely<T>(values: Eachable<T>, each: (value: T) => void) {\n  values.forEach(value => {\n    try {\n      each(value)\n    } catch (e) {\n      raf.catch(e as Error)\n    }\n  })\n}\n\n/** Tree-shakable state for testing purposes */\nexport const __raf = {\n  /** The number of pending tasks */\n  count(): number {\n    return pendingCount\n  },\n  /** Whether there's a raf update loop running */\n  isRunning(): boolean {\n    return ts >= 0\n  },\n  /** Clear internal state. Never call from update loop! */\n  clear() {\n    ts = -1\n    timeouts = []\n    onStartQueue = makeQueue()\n    updateQueue = makeQueue()\n    onFrameQueue = makeQueue()\n    writeQueue = makeQueue()\n    onFinishQueue = makeQueue()\n    pendingCount = 0\n  },\n}\n"], "mappings": ";AAWA,IAAIA,WAAA,GAAcC,SAAA,CAAyB;AAMpC,IAAMC,GAAA,GAAYC,EAAA,IAAMC,QAAA,CAASD,EAAA,EAAIH,WAAW;AAEvD,IAAIK,UAAA,GAAaJ,SAAA,CAAmB;AACpCC,GAAA,CAAII,KAAA,GAAQH,EAAA,IAAMC,QAAA,CAASD,EAAA,EAAIE,UAAU;AAEzC,IAAIE,YAAA,GAAeN,SAAA,CAAmB;AACtCC,GAAA,CAAIM,OAAA,GAAUL,EAAA,IAAMC,QAAA,CAASD,EAAA,EAAII,YAAY;AAE7C,IAAIE,YAAA,GAAeR,SAAA,CAAmB;AACtCC,GAAA,CAAIQ,OAAA,GAAUP,EAAA,IAAMC,QAAA,CAASD,EAAA,EAAIM,YAAY;AAE7C,IAAIE,aAAA,GAAgBV,SAAA,CAAmB;AACvCC,GAAA,CAAIU,QAAA,GAAWT,EAAA,IAAMC,QAAA,CAASD,EAAA,EAAIQ,aAAa;AAE/C,IAAIE,QAAA,GAAsB,EAAC;AAC3BX,GAAA,CAAIY,UAAA,GAAa,CAACC,OAAA,EAASC,EAAA,KAAO;EAChC,MAAMC,IAAA,GAAOf,GAAA,CAAIgB,GAAA,CAAI,IAAIF,EAAA;EACzB,MAAMG,MAAA,GAASA,CAAA,KAAM;IACnB,MAAMC,CAAA,GAAIP,QAAA,CAASQ,SAAA,CAAUC,CAAA,IAAKA,CAAA,CAAEH,MAAA,IAAUA,MAAM;IACpD,IAAI,CAACC,CAAA,EAAGP,QAAA,CAASU,MAAA,CAAOH,CAAA,EAAG,CAAC;IAC5BI,YAAA,IAAgB,CAACJ,CAAA,GAAI,IAAI;EAC3B;EAEA,MAAMK,OAAA,GAAmB;IAAER,IAAA;IAAMF,OAAA;IAASI;EAAO;EACjDN,QAAA,CAASU,MAAA,CAAOG,WAAA,CAAYT,IAAI,GAAG,GAAGQ,OAAO;EAC7CD,YAAA,IAAgB;EAEhBG,KAAA,CAAM;EACN,OAAOF,OAAA;AACT;AAGA,IAAMC,WAAA,GAAeT,IAAA,IACnB,EAAE,CAACJ,QAAA,CAASQ,SAAA,CAAUC,CAAA,IAAKA,CAAA,CAAEL,IAAA,GAAOA,IAAI,KAAK,CAACJ,QAAA,CAASe,MAAA;AAEzD1B,GAAA,CAAIiB,MAAA,GAAShB,EAAA,IAAM;EACjBI,YAAA,CAAasB,MAAA,CAAO1B,EAAE;EACtBM,YAAA,CAAaoB,MAAA,CAAO1B,EAAE;EACtBQ,aAAA,CAAckB,MAAA,CAAO1B,EAAE;EACvBH,WAAA,CAAY6B,MAAA,CAAO1B,EAAE;EACrBE,UAAA,CAAWwB,MAAA,CAAO1B,EAAE;AACtB;AAEAD,GAAA,CAAI4B,IAAA,GAAO3B,EAAA,IAAM;EACf2B,IAAA,GAAO;EACP5B,GAAA,CAAI6B,cAAA,CAAe5B,EAAE;EACrB2B,IAAA,GAAO;AACT;AAEA5B,GAAA,CAAI8B,QAAA,GAAW7B,EAAA,IAAM;EACnB,IAAI8B,QAAA;EACJ,SAASC,SAAA,EAAW;IAClB,IAAI;MACF/B,EAAA,CAAG,GAAG8B,QAAQ;IAChB,UAAE;MACAA,QAAA,GAAW;IACb;EACF;EACA,SAASE,UAAA,GAAaC,IAAA,EAAW;IAC/BH,QAAA,GAAWG,IAAA;IACXlC,GAAA,CAAIM,OAAA,CAAQ0B,QAAQ;EACtB;EACAC,SAAA,CAAUpB,OAAA,GAAUZ,EAAA;EACpBgC,SAAA,CAAUhB,MAAA,GAAS,MAAM;IACvBZ,YAAA,CAAasB,MAAA,CAAOK,QAAQ;IAC5BD,QAAA,GAAW;EACb;EACA,OAAOE,SAAA;AACT;AAEA,IAAIE,SAAA,GACF,OAAOC,MAAA,IAAU,cACZA,MAAA,CAAOC,qBAAA;AAAA;AAER,MAAM,CAAC;AAEbrC,GAAA,CAAIsC,GAAA,GAAMC,IAAA,IAASJ,SAAA,GAAYI,IAAA;AAC/BvC,GAAA,CAAIgB,GAAA,GAAM,OAAOwB,WAAA,IAAe,cAAc,MAAMA,WAAA,CAAYxB,GAAA,CAAI,IAAIyB,IAAA,CAAKzB,GAAA;AAC7EhB,GAAA,CAAI6B,cAAA,GAAiB5B,EAAA,IAAMA,EAAA,CAAG;AAC9BD,GAAA,CAAI0C,KAAA,GAAQC,OAAA,CAAQC,KAAA;AAEpB5C,GAAA,CAAI6C,SAAA,GAAY;AAEhB7C,GAAA,CAAI8C,OAAA,GAAU,MAAM;EAClB,IAAI9C,GAAA,CAAI6C,SAAA,KAAc,UAAU;IAC9BF,OAAA,CAAQI,IAAA,CACN,kFACF;EACF,OAAO;IACLC,MAAA,CAAO;EACT;AACF;AAGA,IAAIC,EAAA,GAAK;AAGT,IAAI3B,YAAA,GAAe;AAGnB,IAAIM,IAAA,GAAO;AAEX,SAAS1B,SAA6BD,EAAA,EAAOiD,KAAA,EAAiB;EAC5D,IAAItB,IAAA,EAAM;IACRsB,KAAA,CAAMvB,MAAA,CAAO1B,EAAE;IACfA,EAAA,CAAG,CAAC;EACN,OAAO;IACLiD,KAAA,CAAMC,GAAA,CAAIlD,EAAE;IACZwB,KAAA,CAAM;EACR;AACF;AAEA,SAASA,MAAA,EAAQ;EACf,IAAIwB,EAAA,GAAK,GAAG;IACVA,EAAA,GAAK;IACL,IAAIjD,GAAA,CAAI6C,SAAA,KAAc,UAAU;MAC9BV,SAAA,CAAUiB,IAAI;IAChB;EACF;AACF;AAEA,SAASC,KAAA,EAAO;EACdJ,EAAA,GAAK;AACP;AAEA,SAASG,KAAA,EAAO;EACd,IAAI,CAACH,EAAA,EAAI;IACPd,SAAA,CAAUiB,IAAI;IACdpD,GAAA,CAAI6B,cAAA,CAAemB,MAAM;EAC3B;AACF;AAEA,SAASA,OAAA,EAAS;EAChB,MAAMM,MAAA,GAASL,EAAA;EACfA,EAAA,GAAKjD,GAAA,CAAIgB,GAAA,CAAI;EAGb,MAAMuC,KAAA,GAAQ/B,WAAA,CAAYyB,EAAE;EAC5B,IAAIM,KAAA,EAAO;IACTC,UAAA,CAAW7C,QAAA,CAASU,MAAA,CAAO,GAAGkC,KAAK,GAAGnC,CAAA,IAAKA,CAAA,CAAEP,OAAA,CAAQ,CAAC;IACtDS,YAAA,IAAgBiC,KAAA;EAClB;EAEA,IAAI,CAACjC,YAAA,EAAc;IACjB+B,IAAA,CAAK;IAEL;EACF;EAEAhD,YAAA,CAAaoD,KAAA,CAAM;EACnB3D,WAAA,CAAY2D,KAAA,CAAMH,MAAA,GAASI,IAAA,CAAKC,GAAA,CAAI,IAAIV,EAAA,GAAKK,MAAM,IAAI,MAAM;EAC7D/C,YAAA,CAAakD,KAAA,CAAM;EACnBtD,UAAA,CAAWsD,KAAA,CAAM;EACjBhD,aAAA,CAAcgD,KAAA,CAAM;AACtB;AAQA,SAAS1D,UAAA,EAA0C;EACjD,IAAI6D,IAAA,GAAO,mBAAIC,GAAA,CAAO;EACtB,IAAIC,OAAA,GAAUF,IAAA;EACd,OAAO;IACLT,IAAIlD,EAAA,EAAI;MACNqB,YAAA,IAAgBwC,OAAA,IAAWF,IAAA,IAAQ,CAACA,IAAA,CAAKG,GAAA,CAAI9D,EAAE,IAAI,IAAI;MACvD2D,IAAA,CAAKT,GAAA,CAAIlD,EAAE;IACb;IACA0B,OAAO1B,EAAA,EAAI;MACTqB,YAAA,IAAgBwC,OAAA,IAAWF,IAAA,IAAQA,IAAA,CAAKG,GAAA,CAAI9D,EAAE,IAAI,IAAI;MACtD,OAAO2D,IAAA,CAAKjC,MAAA,CAAO1B,EAAE;IACvB;IACAwD,MAAMO,GAAA,EAAK;MACT,IAAIF,OAAA,CAAQG,IAAA,EAAM;QAChBL,IAAA,GAAO,mBAAIC,GAAA,CAAI;QACfvC,YAAA,IAAgBwC,OAAA,CAAQG,IAAA;QACxBT,UAAA,CAAWM,OAAA,EAAS7D,EAAA,IAAMA,EAAA,CAAG+D,GAAG,KAAKJ,IAAA,CAAKT,GAAA,CAAIlD,EAAE,CAAC;QACjDqB,YAAA,IAAgBsC,IAAA,CAAKK,IAAA;QACrBH,OAAA,GAAUF,IAAA;MACZ;IACF;EACF;AACF;AAMA,SAASJ,WAAcU,MAAA,EAAqBC,IAAA,EAA0B;EACpED,MAAA,CAAOE,OAAA,CAAQC,KAAA,IAAS;IACtB,IAAI;MACFF,IAAA,CAAKE,KAAK;IACZ,SAASC,CAAA,EAAP;MACAtE,GAAA,CAAI0C,KAAA,CAAM4B,CAAU;IACtB;EACF,CAAC;AACH;AAGO,IAAMC,KAAA,GAAQ;EAAA;EAEnBhB,MAAA,EAAgB;IACd,OAAOjC,YAAA;EACT;EAAA;EAEAkD,UAAA,EAAqB;IACnB,OAAOvB,EAAA,IAAM;EACf;EAAA;EAEAwB,MAAA,EAAQ;IACNxB,EAAA,GAAK;IACLtC,QAAA,GAAW,EAAC;IACZN,YAAA,GAAeN,SAAA,CAAU;IACzBD,WAAA,GAAcC,SAAA,CAAU;IACxBQ,YAAA,GAAeR,SAAA,CAAU;IACzBI,UAAA,GAAaJ,SAAA,CAAU;IACvBU,aAAA,GAAgBV,SAAA,CAAU;IAC1BuB,YAAA,GAAe;EACjB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}