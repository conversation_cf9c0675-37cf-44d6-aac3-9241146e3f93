{"ast": null, "code": "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n  return (\n    // this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot ||\n    // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || (\n    // DOM Element detected\n    isShadowRoot(element) ? element.host : null) ||\n    // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n  );\n}", "map": {"version": 3, "names": ["getNodeName", "getDocumentElement", "isShadowRoot", "getParentNode", "element", "assignedSlot", "parentNode", "host"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@popperjs/core/lib/dom-utils/getParentNode.js"], "sourcesContent": ["import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,eAAe,SAASC,aAAaA,CAACC,OAAO,EAAE;EAC7C,IAAIJ,WAAW,CAACI,OAAO,CAAC,KAAK,MAAM,EAAE;IACnC,OAAOA,OAAO;EAChB;EAEA;IAAQ;IACN;IACA;IACAA,OAAO,CAACC,YAAY;IAAI;IACxBD,OAAO,CAACE,UAAU;IAAM;IACxBJ,YAAY,CAACE,OAAO,CAAC,GAAGA,OAAO,CAACG,IAAI,GAAG,IAAI,CAAC;IAAI;IAChD;IACAN,kBAAkB,CAACG,OAAO,CAAC,CAAC;EAAA;AAGhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}