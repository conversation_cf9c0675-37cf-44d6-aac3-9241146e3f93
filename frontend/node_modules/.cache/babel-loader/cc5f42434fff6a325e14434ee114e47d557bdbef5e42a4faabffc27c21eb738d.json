{"ast": null, "code": "/**\n * Floodguard AI-Powered Risk Mapping System\n * Comprehensive Theme Configuration\n * \n * This theme is specifically designed for flood management applications with:\n * - WCAG 2.1 AA accessibility compliance\n * - Colorblind-friendly color choices\n * - Water, safety, and environmental color semantics\n * - Professional appearance for government/municipal use\n */\n\nimport { createTheme } from '@mui/material/styles';\nimport { getFloodguardPalette, getNeuromorphicShadow } from './neuromorphicUtils';\n\n// Get the Floodguard color palette\nconst palette = getFloodguardPalette();\n\n// Create the comprehensive Floodguard theme\nexport const createFloodguardTheme = () => {\n  return createTheme({\n    palette: {\n      mode: 'light',\n      // Core palette\n      primary: palette.primary,\n      secondary: palette.secondary,\n      error: palette.error,\n      warning: palette.warning,\n      success: palette.success,\n      info: palette.info,\n      // Background and surface colors\n      background: {\n        default: palette.background.default,\n        paper: palette.background.paper,\n        elevated: palette.background.elevated\n      },\n      // Text colors\n      text: {\n        primary: palette.text.primary,\n        secondary: palette.text.secondary,\n        disabled: palette.text.disabled,\n        contrast: palette.text.contrast\n      },\n      // Divider colors\n      divider: palette.divider,\n      // Custom color extensions for flood management\n      floodRisk: palette.floodRisk,\n      water: palette.water,\n      environment: palette.environment,\n      surface: palette.surface,\n      border: palette.border\n    },\n    typography: {\n      fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n      h1: {\n        fontSize: 'clamp(2.5rem, 5vw, 3.5rem)',\n        fontWeight: 800,\n        letterSpacing: '-0.02em',\n        lineHeight: 1.1,\n        color: palette.text.primary\n      },\n      h2: {\n        fontSize: 'clamp(2rem, 4vw, 2.75rem)',\n        fontWeight: 700,\n        letterSpacing: '-0.01em',\n        lineHeight: 1.2,\n        color: palette.text.primary\n      },\n      h3: {\n        fontSize: 'clamp(1.5rem, 3vw, 2rem)',\n        fontWeight: 600,\n        letterSpacing: '-0.01em',\n        lineHeight: 1.3,\n        color: palette.text.primary\n      },\n      h4: {\n        fontSize: 'clamp(1.25rem, 2.5vw, 1.75rem)',\n        fontWeight: 600,\n        letterSpacing: '0em',\n        lineHeight: 1.4,\n        color: palette.text.primary\n      },\n      h5: {\n        fontSize: 'clamp(1.1rem, 2vw, 1.5rem)',\n        fontWeight: 500,\n        letterSpacing: '0em',\n        lineHeight: 1.4,\n        color: palette.text.primary\n      },\n      h6: {\n        fontSize: 'clamp(1rem, 1.5vw, 1.25rem)',\n        fontWeight: 500,\n        letterSpacing: '0.01em',\n        lineHeight: 1.5,\n        color: palette.text.primary\n      },\n      body1: {\n        fontSize: 'clamp(0.875rem, 1.5vw, 1rem)',\n        lineHeight: 1.6,\n        color: palette.text.secondary\n      },\n      body2: {\n        fontSize: 'clamp(0.8125rem, 1.25vw, 0.875rem)',\n        lineHeight: 1.6,\n        color: palette.text.secondary\n      },\n      button: {\n        fontWeight: 600,\n        textTransform: 'none',\n        letterSpacing: '0.02em',\n        fontSize: 'clamp(0.875rem, 1.5vw, 1rem)'\n      },\n      subtitle1: {\n        fontSize: 'clamp(0.9375rem, 1.75vw, 1.125rem)',\n        fontWeight: 500,\n        lineHeight: 1.5,\n        color: palette.text.secondary\n      },\n      subtitle2: {\n        fontSize: 'clamp(0.8125rem, 1.5vw, 0.9375rem)',\n        fontWeight: 500,\n        lineHeight: 1.5,\n        color: palette.text.secondary\n      }\n    },\n    shape: {\n      borderRadius: 12\n    },\n    // Enhanced shadow system for depth and hierarchy\n    shadows: ['none', '0px 1px 2px rgba(15, 23, 42, 0.05)', '0px 2px 4px rgba(15, 23, 42, 0.06), 0px 1px 2px rgba(15, 23, 42, 0.04)', '0px 4px 6px rgba(15, 23, 42, 0.07), 0px 2px 4px rgba(15, 23, 42, 0.05)', '0px 6px 8px rgba(15, 23, 42, 0.08), 0px 3px 6px rgba(15, 23, 42, 0.06)', '0px 8px 12px rgba(15, 23, 42, 0.09), 0px 4px 8px rgba(15, 23, 42, 0.07)', '0px 10px 15px rgba(15, 23, 42, 0.10), 0px 6px 10px rgba(15, 23, 42, 0.08)', '0px 12px 18px rgba(15, 23, 42, 0.11), 0px 7px 12px rgba(15, 23, 42, 0.09)', '0px 14px 21px rgba(15, 23, 42, 0.12), 0px 8px 14px rgba(15, 23, 42, 0.10)', '0px 16px 24px rgba(15, 23, 42, 0.13), 0px 9px 16px rgba(15, 23, 42, 0.11)', '0px 18px 27px rgba(15, 23, 42, 0.14), 0px 10px 18px rgba(15, 23, 42, 0.12)', '0px 20px 30px rgba(15, 23, 42, 0.15), 0px 11px 20px rgba(15, 23, 42, 0.13)', '0px 22px 33px rgba(15, 23, 42, 0.16), 0px 12px 22px rgba(15, 23, 42, 0.14)', '0px 24px 36px rgba(15, 23, 42, 0.17), 0px 13px 24px rgba(15, 23, 42, 0.15)', '0px 26px 39px rgba(15, 23, 42, 0.18), 0px 14px 26px rgba(15, 23, 42, 0.16)', '0px 28px 42px rgba(15, 23, 42, 0.19), 0px 15px 28px rgba(15, 23, 42, 0.17)', '0px 30px 45px rgba(15, 23, 42, 0.20), 0px 16px 30px rgba(15, 23, 42, 0.18)', '0px 32px 48px rgba(15, 23, 42, 0.21), 0px 17px 32px rgba(15, 23, 42, 0.19)', '0px 34px 51px rgba(15, 23, 42, 0.22), 0px 18px 34px rgba(15, 23, 42, 0.20)', '0px 36px 54px rgba(15, 23, 42, 0.23), 0px 19px 36px rgba(15, 23, 42, 0.21)', '0px 38px 57px rgba(15, 23, 42, 0.24), 0px 20px 38px rgba(15, 23, 42, 0.22)', '0px 40px 60px rgba(15, 23, 42, 0.25), 0px 21px 40px rgba(15, 23, 42, 0.23)', '0px 42px 63px rgba(15, 23, 42, 0.26), 0px 22px 42px rgba(15, 23, 42, 0.24)', '0px 44px 66px rgba(15, 23, 42, 0.27), 0px 23px 44px rgba(15, 23, 42, 0.25)', '0px 46px 69px rgba(15, 23, 42, 0.28), 0px 24px 46px rgba(15, 23, 42, 0.26)'],\n    breakpoints: {\n      values: {\n        xs: 0,\n        sm: 600,\n        md: 960,\n        lg: 1280,\n        xl: 1920\n      }\n    },\n    // Component style overrides with Floodguard theme\n    components: {\n      // Container component\n      MuiContainer: {\n        styleOverrides: {\n          root: {\n            paddingLeft: 16,\n            paddingRight: 16,\n            '@media (min-width:600px)': {\n              paddingLeft: 24,\n              paddingRight: 24\n            },\n            '@media (min-width:960px)': {\n              paddingLeft: 32,\n              paddingRight: 32\n            },\n            '@media (min-width:1200px)': {\n              paddingLeft: 48,\n              paddingRight: 48\n            },\n            maxWidth: '100%',\n            '@media (min-width:1280px)': {\n              maxWidth: '1280px'\n            },\n            '@media (min-width:1920px)': {\n              maxWidth: '1920px'\n            }\n          }\n        }\n      },\n      // Grid component\n      MuiGrid: {\n        styleOverrides: {\n          container: {\n            marginTop: 0,\n            marginLeft: 0,\n            width: '100%'\n          },\n          item: {\n            paddingTop: 12,\n            paddingLeft: 12,\n            '@media (min-width:600px)': {\n              paddingTop: 16,\n              paddingLeft: 16\n            },\n            '@media (min-width:960px)': {\n              paddingTop: 20,\n              paddingLeft: 20\n            },\n            '@media (min-width:1200px)': {\n              paddingTop: 24,\n              paddingLeft: 24\n            }\n          }\n        }\n      },\n      // Paper component with neuromorphic styling\n      MuiPaper: {\n        styleOverrides: {\n          root: {\n            backgroundColor: palette.background.paper,\n            borderRadius: 16,\n            boxShadow: getNeuromorphicShadow(palette.background.paper),\n            transition: 'all 0.3s ease-in-out',\n            border: `1px solid ${palette.border.light}`,\n            '&:hover': {\n              boxShadow: getNeuromorphicShadow(palette.background.paper, 1.2),\n              borderColor: palette.border.medium\n            }\n          },\n          elevation1: {\n            backgroundColor: palette.surface.level1\n          },\n          elevation2: {\n            backgroundColor: palette.surface.level2\n          },\n          elevation3: {\n            backgroundColor: palette.surface.level3\n          }\n        }\n      },\n      // Button component with enhanced styling\n      MuiButton: {\n        styleOverrides: {\n          root: {\n            borderRadius: 12,\n            fontWeight: 600,\n            textTransform: 'none',\n            transition: 'all 0.2s ease-in-out',\n            boxShadow: 'none',\n            '&:hover': {\n              boxShadow: '0 4px 12px rgba(15, 23, 42, 0.15)',\n              transform: 'translateY(-1px)'\n            },\n            '&:active': {\n              transform: 'translateY(0)'\n            }\n          },\n          containedPrimary: {\n            backgroundColor: palette.primary.main,\n            color: palette.primary.contrastText,\n            '&:hover': {\n              backgroundColor: palette.primary.dark,\n              boxShadow: `0 6px 16px ${palette.primary.main}40`\n            },\n            '&:active': {\n              backgroundColor: palette.primary.dark\n            }\n          },\n          containedSecondary: {\n            backgroundColor: palette.secondary.main,\n            color: palette.secondary.contrastText,\n            '&:hover': {\n              backgroundColor: palette.secondary.dark,\n              boxShadow: `0 6px 16px ${palette.secondary.main}40`\n            }\n          },\n          outlined: {\n            borderColor: palette.border.medium,\n            color: palette.text.primary,\n            '&:hover': {\n              borderColor: palette.primary.main,\n              backgroundColor: `${palette.primary.main}08`\n            }\n          },\n          text: {\n            color: palette.text.primary,\n            '&:hover': {\n              backgroundColor: `${palette.primary.main}08`\n            }\n          }\n        }\n      },\n      // TextField component\n      MuiTextField: {\n        styleOverrides: {\n          root: {\n            '& .MuiOutlinedInput-root': {\n              backgroundColor: palette.background.paper,\n              borderRadius: 12,\n              transition: 'all 0.2s ease-in-out',\n              '& fieldset': {\n                borderColor: palette.border.medium,\n                borderWidth: 1\n              },\n              '&:hover fieldset': {\n                borderColor: palette.border.strong\n              },\n              '&.Mui-focused fieldset': {\n                borderColor: palette.primary.main,\n                borderWidth: 2\n              },\n              '&.Mui-error fieldset': {\n                borderColor: palette.error.main\n              }\n            },\n            '& .MuiInputLabel-root': {\n              color: palette.text.secondary,\n              '&.Mui-focused': {\n                color: palette.primary.main\n              },\n              '&.Mui-error': {\n                color: palette.error.main\n              }\n            },\n            '& .MuiInputBase-input': {\n              color: palette.text.primary\n            }\n          }\n        }\n      },\n      // Alert component with flood-specific styling\n      MuiAlert: {\n        styleOverrides: {\n          root: {\n            borderRadius: 12,\n            fontWeight: 500,\n            border: `1px solid ${palette.border.light}`\n          },\n          standardSuccess: {\n            backgroundColor: palette.floodRisk.minimal,\n            color: palette.success.dark,\n            borderColor: palette.success.light,\n            '& .MuiAlert-icon': {\n              color: palette.success.main\n            }\n          },\n          standardError: {\n            backgroundColor: palette.floodRisk.severe,\n            color: palette.error.dark,\n            borderColor: palette.error.light,\n            '& .MuiAlert-icon': {\n              color: palette.error.main\n            }\n          },\n          standardWarning: {\n            backgroundColor: palette.floodRisk.moderate,\n            color: palette.warning.dark,\n            borderColor: palette.warning.light,\n            '& .MuiAlert-icon': {\n              color: palette.warning.main\n            }\n          },\n          standardInfo: {\n            backgroundColor: `${palette.info.main}10`,\n            color: palette.info.dark,\n            borderColor: palette.info.light,\n            '& .MuiAlert-icon': {\n              color: palette.info.main\n            }\n          }\n        }\n      },\n      // Chip component with enhanced styling\n      MuiChip: {\n        styleOverrides: {\n          root: {\n            borderRadius: 8,\n            fontWeight: 500,\n            border: `1px solid ${palette.border.light}`,\n            transition: 'all 0.2s ease-in-out',\n            '&:hover': {\n              transform: 'translateY(-1px)',\n              boxShadow: '0 4px 12px rgba(15, 23, 42, 0.15)'\n            }\n          },\n          colorPrimary: {\n            backgroundColor: palette.primary.main,\n            color: palette.primary.contrastText,\n            borderColor: palette.primary.main\n          },\n          colorSecondary: {\n            backgroundColor: palette.secondary.main,\n            color: palette.secondary.contrastText,\n            borderColor: palette.secondary.main\n          },\n          colorSuccess: {\n            backgroundColor: palette.success.main,\n            color: palette.success.contrastText,\n            borderColor: palette.success.main\n          },\n          colorError: {\n            backgroundColor: palette.error.main,\n            color: palette.error.contrastText,\n            borderColor: palette.error.main\n          },\n          colorWarning: {\n            backgroundColor: palette.warning.main,\n            color: palette.warning.contrastText,\n            borderColor: palette.warning.main\n          },\n          colorInfo: {\n            backgroundColor: palette.info.main,\n            color: palette.info.contrastText,\n            borderColor: palette.info.main\n          }\n        }\n      },\n      // Card component\n      MuiCard: {\n        styleOverrides: {\n          root: {\n            backgroundColor: palette.background.paper,\n            borderRadius: 16,\n            border: `1px solid ${palette.border.light}`,\n            transition: 'all 0.3s ease-in-out',\n            '&:hover': {\n              borderColor: palette.border.medium,\n              transform: 'translateY(-2px)',\n              boxShadow: '0 8px 24px rgba(15, 23, 42, 0.12)'\n            }\n          }\n        }\n      },\n      // Typography component\n      MuiTypography: {\n        styleOverrides: {\n          root: {\n            color: palette.text.primary\n          },\n          h1: {\n            color: palette.text.primary,\n            fontWeight: 800\n          },\n          h2: {\n            color: palette.text.primary,\n            fontWeight: 700\n          },\n          h3: {\n            color: palette.text.primary,\n            fontWeight: 600\n          },\n          h4: {\n            color: palette.text.primary,\n            fontWeight: 600\n          },\n          h5: {\n            color: palette.text.primary,\n            fontWeight: 500\n          },\n          h6: {\n            color: palette.text.primary,\n            fontWeight: 500\n          },\n          subtitle1: {\n            color: palette.text.secondary\n          },\n          subtitle2: {\n            color: palette.text.secondary\n          },\n          body1: {\n            color: palette.text.secondary\n          },\n          body2: {\n            color: palette.text.secondary\n          }\n        }\n      }\n    }\n  });\n};\n\n// Export the default theme instance\nexport const floodguardTheme = createFloodguardTheme();\n\n// Export color utilities for direct use in components\nexport { palette as floodguardColors };\n\n// Export specific color sets for easy access\nexport const riskColors = palette.floodRisk;\nexport const waterColors = palette.water;\nexport const environmentColors = palette.environment;", "map": {"version": 3, "names": ["createTheme", "getFloodguardPalette", "getNeuromorphicShadow", "palette", "createFloodguardTheme", "mode", "primary", "secondary", "error", "warning", "success", "info", "background", "default", "paper", "elevated", "text", "disabled", "contrast", "divider", "floodRisk", "water", "environment", "surface", "border", "typography", "fontFamily", "h1", "fontSize", "fontWeight", "letterSpacing", "lineHeight", "color", "h2", "h3", "h4", "h5", "h6", "body1", "body2", "button", "textTransform", "subtitle1", "subtitle2", "shape", "borderRadius", "shadows", "breakpoints", "values", "xs", "sm", "md", "lg", "xl", "components", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styleOverrides", "root", "paddingLeft", "paddingRight", "max<PERSON><PERSON><PERSON>", "MuiGrid", "container", "marginTop", "marginLeft", "width", "item", "paddingTop", "MuiPaper", "backgroundColor", "boxShadow", "transition", "light", "borderColor", "medium", "elevation1", "level1", "elevation2", "level2", "elevation3", "level3", "MuiB<PERSON>on", "transform", "containedPrimary", "main", "contrastText", "dark", "containedSecondary", "outlined", "MuiTextField", "borderWidth", "strong", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "standardSuccess", "minimal", "standardError", "severe", "standardWarning", "moderate", "standardInfo", "MuiChip", "colorPrimary", "colorSecondary", "colorSuccess", "colorError", "colorWarning", "colorInfo", "MuiCard", "MuiTypography", "floodguardTheme", "floodguardColors", "riskColors", "waterColors", "environmentColors"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/src/theme/floodguardTheme.js"], "sourcesContent": ["/**\n * Floodguard AI-Powered Risk Mapping System\n * Comprehensive Theme Configuration\n * \n * This theme is specifically designed for flood management applications with:\n * - WCAG 2.1 AA accessibility compliance\n * - Colorblind-friendly color choices\n * - Water, safety, and environmental color semantics\n * - Professional appearance for government/municipal use\n */\n\nimport { createTheme } from '@mui/material/styles';\nimport { getFloodguardPalette, getNeuromorphicShadow } from './neuromorphicUtils';\n\n// Get the Floodguard color palette\nconst palette = getFloodguardPalette();\n\n// Create the comprehensive Floodguard theme\nexport const createFloodguardTheme = () => {\n  return createTheme({\n    palette: {\n      mode: 'light',\n      \n      // Core palette\n      primary: palette.primary,\n      secondary: palette.secondary,\n      error: palette.error,\n      warning: palette.warning,\n      success: palette.success,\n      info: palette.info,\n      \n      // Background and surface colors\n      background: {\n        default: palette.background.default,\n        paper: palette.background.paper,\n        elevated: palette.background.elevated\n      },\n      \n      // Text colors\n      text: {\n        primary: palette.text.primary,\n        secondary: palette.text.secondary,\n        disabled: palette.text.disabled,\n        contrast: palette.text.contrast\n      },\n      \n      // Divider colors\n      divider: palette.divider,\n      \n      // Custom color extensions for flood management\n      floodRisk: palette.floodRisk,\n      water: palette.water,\n      environment: palette.environment,\n      surface: palette.surface,\n      border: palette.border\n    },\n    \n    typography: {\n      fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n      \n      h1: {\n        fontSize: 'clamp(2.5rem, 5vw, 3.5rem)',\n        fontWeight: 800,\n        letterSpacing: '-0.02em',\n        lineHeight: 1.1,\n        color: palette.text.primary\n      },\n      \n      h2: {\n        fontSize: 'clamp(2rem, 4vw, 2.75rem)',\n        fontWeight: 700,\n        letterSpacing: '-0.01em',\n        lineHeight: 1.2,\n        color: palette.text.primary\n      },\n      \n      h3: {\n        fontSize: 'clamp(1.5rem, 3vw, 2rem)',\n        fontWeight: 600,\n        letterSpacing: '-0.01em',\n        lineHeight: 1.3,\n        color: palette.text.primary\n      },\n      \n      h4: {\n        fontSize: 'clamp(1.25rem, 2.5vw, 1.75rem)',\n        fontWeight: 600,\n        letterSpacing: '0em',\n        lineHeight: 1.4,\n        color: palette.text.primary\n      },\n      \n      h5: {\n        fontSize: 'clamp(1.1rem, 2vw, 1.5rem)',\n        fontWeight: 500,\n        letterSpacing: '0em',\n        lineHeight: 1.4,\n        color: palette.text.primary\n      },\n      \n      h6: {\n        fontSize: 'clamp(1rem, 1.5vw, 1.25rem)',\n        fontWeight: 500,\n        letterSpacing: '0.01em',\n        lineHeight: 1.5,\n        color: palette.text.primary\n      },\n      \n      body1: {\n        fontSize: 'clamp(0.875rem, 1.5vw, 1rem)',\n        lineHeight: 1.6,\n        color: palette.text.secondary\n      },\n      \n      body2: {\n        fontSize: 'clamp(0.8125rem, 1.25vw, 0.875rem)',\n        lineHeight: 1.6,\n        color: palette.text.secondary\n      },\n      \n      button: {\n        fontWeight: 600,\n        textTransform: 'none',\n        letterSpacing: '0.02em',\n        fontSize: 'clamp(0.875rem, 1.5vw, 1rem)'\n      },\n      \n      subtitle1: {\n        fontSize: 'clamp(0.9375rem, 1.75vw, 1.125rem)',\n        fontWeight: 500,\n        lineHeight: 1.5,\n        color: palette.text.secondary\n      },\n      \n      subtitle2: {\n        fontSize: 'clamp(0.8125rem, 1.5vw, 0.9375rem)',\n        fontWeight: 500,\n        lineHeight: 1.5,\n        color: palette.text.secondary\n      }\n    },\n    \n    shape: {\n      borderRadius: 12\n    },\n    \n    // Enhanced shadow system for depth and hierarchy\n    shadows: [\n      'none',\n      '0px 1px 2px rgba(15, 23, 42, 0.05)',\n      '0px 2px 4px rgba(15, 23, 42, 0.06), 0px 1px 2px rgba(15, 23, 42, 0.04)',\n      '0px 4px 6px rgba(15, 23, 42, 0.07), 0px 2px 4px rgba(15, 23, 42, 0.05)',\n      '0px 6px 8px rgba(15, 23, 42, 0.08), 0px 3px 6px rgba(15, 23, 42, 0.06)',\n      '0px 8px 12px rgba(15, 23, 42, 0.09), 0px 4px 8px rgba(15, 23, 42, 0.07)',\n      '0px 10px 15px rgba(15, 23, 42, 0.10), 0px 6px 10px rgba(15, 23, 42, 0.08)',\n      '0px 12px 18px rgba(15, 23, 42, 0.11), 0px 7px 12px rgba(15, 23, 42, 0.09)',\n      '0px 14px 21px rgba(15, 23, 42, 0.12), 0px 8px 14px rgba(15, 23, 42, 0.10)',\n      '0px 16px 24px rgba(15, 23, 42, 0.13), 0px 9px 16px rgba(15, 23, 42, 0.11)',\n      '0px 18px 27px rgba(15, 23, 42, 0.14), 0px 10px 18px rgba(15, 23, 42, 0.12)',\n      '0px 20px 30px rgba(15, 23, 42, 0.15), 0px 11px 20px rgba(15, 23, 42, 0.13)',\n      '0px 22px 33px rgba(15, 23, 42, 0.16), 0px 12px 22px rgba(15, 23, 42, 0.14)',\n      '0px 24px 36px rgba(15, 23, 42, 0.17), 0px 13px 24px rgba(15, 23, 42, 0.15)',\n      '0px 26px 39px rgba(15, 23, 42, 0.18), 0px 14px 26px rgba(15, 23, 42, 0.16)',\n      '0px 28px 42px rgba(15, 23, 42, 0.19), 0px 15px 28px rgba(15, 23, 42, 0.17)',\n      '0px 30px 45px rgba(15, 23, 42, 0.20), 0px 16px 30px rgba(15, 23, 42, 0.18)',\n      '0px 32px 48px rgba(15, 23, 42, 0.21), 0px 17px 32px rgba(15, 23, 42, 0.19)',\n      '0px 34px 51px rgba(15, 23, 42, 0.22), 0px 18px 34px rgba(15, 23, 42, 0.20)',\n      '0px 36px 54px rgba(15, 23, 42, 0.23), 0px 19px 36px rgba(15, 23, 42, 0.21)',\n      '0px 38px 57px rgba(15, 23, 42, 0.24), 0px 20px 38px rgba(15, 23, 42, 0.22)',\n      '0px 40px 60px rgba(15, 23, 42, 0.25), 0px 21px 40px rgba(15, 23, 42, 0.23)',\n      '0px 42px 63px rgba(15, 23, 42, 0.26), 0px 22px 42px rgba(15, 23, 42, 0.24)',\n      '0px 44px 66px rgba(15, 23, 42, 0.27), 0px 23px 44px rgba(15, 23, 42, 0.25)',\n      '0px 46px 69px rgba(15, 23, 42, 0.28), 0px 24px 46px rgba(15, 23, 42, 0.26)'\n    ],\n    \n    breakpoints: {\n      values: {\n        xs: 0,\n        sm: 600,\n        md: 960,\n        lg: 1280,\n        xl: 1920\n      }\n    },\n\n    // Component style overrides with Floodguard theme\n    components: {\n      // Container component\n      MuiContainer: {\n        styleOverrides: {\n          root: {\n            paddingLeft: 16,\n            paddingRight: 16,\n            '@media (min-width:600px)': {\n              paddingLeft: 24,\n              paddingRight: 24,\n            },\n            '@media (min-width:960px)': {\n              paddingLeft: 32,\n              paddingRight: 32,\n            },\n            '@media (min-width:1200px)': {\n              paddingLeft: 48,\n              paddingRight: 48,\n            },\n            maxWidth: '100%',\n            '@media (min-width:1280px)': {\n              maxWidth: '1280px',\n            },\n            '@media (min-width:1920px)': {\n              maxWidth: '1920px',\n            },\n          },\n        },\n      },\n\n      // Grid component\n      MuiGrid: {\n        styleOverrides: {\n          container: {\n            marginTop: 0,\n            marginLeft: 0,\n            width: '100%',\n          },\n          item: {\n            paddingTop: 12,\n            paddingLeft: 12,\n            '@media (min-width:600px)': {\n              paddingTop: 16,\n              paddingLeft: 16,\n            },\n            '@media (min-width:960px)': {\n              paddingTop: 20,\n              paddingLeft: 20,\n            },\n            '@media (min-width:1200px)': {\n              paddingTop: 24,\n              paddingLeft: 24,\n            },\n          },\n        },\n      },\n\n      // Paper component with neuromorphic styling\n      MuiPaper: {\n        styleOverrides: {\n          root: {\n            backgroundColor: palette.background.paper,\n            borderRadius: 16,\n            boxShadow: getNeuromorphicShadow(palette.background.paper),\n            transition: 'all 0.3s ease-in-out',\n            border: `1px solid ${palette.border.light}`,\n            '&:hover': {\n              boxShadow: getNeuromorphicShadow(palette.background.paper, 1.2),\n              borderColor: palette.border.medium,\n            },\n          },\n          elevation1: {\n            backgroundColor: palette.surface.level1,\n          },\n          elevation2: {\n            backgroundColor: palette.surface.level2,\n          },\n          elevation3: {\n            backgroundColor: palette.surface.level3,\n          },\n        },\n      },\n\n      // Button component with enhanced styling\n      MuiButton: {\n        styleOverrides: {\n          root: {\n            borderRadius: 12,\n            fontWeight: 600,\n            textTransform: 'none',\n            transition: 'all 0.2s ease-in-out',\n            boxShadow: 'none',\n            '&:hover': {\n              boxShadow: '0 4px 12px rgba(15, 23, 42, 0.15)',\n              transform: 'translateY(-1px)',\n            },\n            '&:active': {\n              transform: 'translateY(0)',\n            },\n          },\n          containedPrimary: {\n            backgroundColor: palette.primary.main,\n            color: palette.primary.contrastText,\n            '&:hover': {\n              backgroundColor: palette.primary.dark,\n              boxShadow: `0 6px 16px ${palette.primary.main}40`,\n            },\n            '&:active': {\n              backgroundColor: palette.primary.dark,\n            },\n          },\n          containedSecondary: {\n            backgroundColor: palette.secondary.main,\n            color: palette.secondary.contrastText,\n            '&:hover': {\n              backgroundColor: palette.secondary.dark,\n              boxShadow: `0 6px 16px ${palette.secondary.main}40`,\n            },\n          },\n          outlined: {\n            borderColor: palette.border.medium,\n            color: palette.text.primary,\n            '&:hover': {\n              borderColor: palette.primary.main,\n              backgroundColor: `${palette.primary.main}08`,\n            },\n          },\n          text: {\n            color: palette.text.primary,\n            '&:hover': {\n              backgroundColor: `${palette.primary.main}08`,\n            },\n          },\n        },\n      },\n\n      // TextField component\n      MuiTextField: {\n        styleOverrides: {\n          root: {\n            '& .MuiOutlinedInput-root': {\n              backgroundColor: palette.background.paper,\n              borderRadius: 12,\n              transition: 'all 0.2s ease-in-out',\n              '& fieldset': {\n                borderColor: palette.border.medium,\n                borderWidth: 1,\n              },\n              '&:hover fieldset': {\n                borderColor: palette.border.strong,\n              },\n              '&.Mui-focused fieldset': {\n                borderColor: palette.primary.main,\n                borderWidth: 2,\n              },\n              '&.Mui-error fieldset': {\n                borderColor: palette.error.main,\n              },\n            },\n            '& .MuiInputLabel-root': {\n              color: palette.text.secondary,\n              '&.Mui-focused': {\n                color: palette.primary.main,\n              },\n              '&.Mui-error': {\n                color: palette.error.main,\n              },\n            },\n            '& .MuiInputBase-input': {\n              color: palette.text.primary,\n            },\n          },\n        },\n      },\n\n      // Alert component with flood-specific styling\n      MuiAlert: {\n        styleOverrides: {\n          root: {\n            borderRadius: 12,\n            fontWeight: 500,\n            border: `1px solid ${palette.border.light}`,\n          },\n          standardSuccess: {\n            backgroundColor: palette.floodRisk.minimal,\n            color: palette.success.dark,\n            borderColor: palette.success.light,\n            '& .MuiAlert-icon': {\n              color: palette.success.main,\n            },\n          },\n          standardError: {\n            backgroundColor: palette.floodRisk.severe,\n            color: palette.error.dark,\n            borderColor: palette.error.light,\n            '& .MuiAlert-icon': {\n              color: palette.error.main,\n            },\n          },\n          standardWarning: {\n            backgroundColor: palette.floodRisk.moderate,\n            color: palette.warning.dark,\n            borderColor: palette.warning.light,\n            '& .MuiAlert-icon': {\n              color: palette.warning.main,\n            },\n          },\n          standardInfo: {\n            backgroundColor: `${palette.info.main}10`,\n            color: palette.info.dark,\n            borderColor: palette.info.light,\n            '& .MuiAlert-icon': {\n              color: palette.info.main,\n            },\n          },\n        },\n      },\n\n      // Chip component with enhanced styling\n      MuiChip: {\n        styleOverrides: {\n          root: {\n            borderRadius: 8,\n            fontWeight: 500,\n            border: `1px solid ${palette.border.light}`,\n            transition: 'all 0.2s ease-in-out',\n            '&:hover': {\n              transform: 'translateY(-1px)',\n              boxShadow: '0 4px 12px rgba(15, 23, 42, 0.15)',\n            },\n          },\n          colorPrimary: {\n            backgroundColor: palette.primary.main,\n            color: palette.primary.contrastText,\n            borderColor: palette.primary.main,\n          },\n          colorSecondary: {\n            backgroundColor: palette.secondary.main,\n            color: palette.secondary.contrastText,\n            borderColor: palette.secondary.main,\n          },\n          colorSuccess: {\n            backgroundColor: palette.success.main,\n            color: palette.success.contrastText,\n            borderColor: palette.success.main,\n          },\n          colorError: {\n            backgroundColor: palette.error.main,\n            color: palette.error.contrastText,\n            borderColor: palette.error.main,\n          },\n          colorWarning: {\n            backgroundColor: palette.warning.main,\n            color: palette.warning.contrastText,\n            borderColor: palette.warning.main,\n          },\n          colorInfo: {\n            backgroundColor: palette.info.main,\n            color: palette.info.contrastText,\n            borderColor: palette.info.main,\n          },\n        },\n      },\n\n      // Card component\n      MuiCard: {\n        styleOverrides: {\n          root: {\n            backgroundColor: palette.background.paper,\n            borderRadius: 16,\n            border: `1px solid ${palette.border.light}`,\n            transition: 'all 0.3s ease-in-out',\n            '&:hover': {\n              borderColor: palette.border.medium,\n              transform: 'translateY(-2px)',\n              boxShadow: '0 8px 24px rgba(15, 23, 42, 0.12)',\n            },\n          },\n        },\n      },\n\n      // Typography component\n      MuiTypography: {\n        styleOverrides: {\n          root: {\n            color: palette.text.primary,\n          },\n          h1: {\n            color: palette.text.primary,\n            fontWeight: 800,\n          },\n          h2: {\n            color: palette.text.primary,\n            fontWeight: 700,\n          },\n          h3: {\n            color: palette.text.primary,\n            fontWeight: 600,\n          },\n          h4: {\n            color: palette.text.primary,\n            fontWeight: 600,\n          },\n          h5: {\n            color: palette.text.primary,\n            fontWeight: 500,\n          },\n          h6: {\n            color: palette.text.primary,\n            fontWeight: 500,\n          },\n          subtitle1: {\n            color: palette.text.secondary,\n          },\n          subtitle2: {\n            color: palette.text.secondary,\n          },\n          body1: {\n            color: palette.text.secondary,\n          },\n          body2: {\n            color: palette.text.secondary,\n          },\n        },\n      }\n    }\n  });\n};\n\n// Export the default theme instance\nexport const floodguardTheme = createFloodguardTheme();\n\n// Export color utilities for direct use in components\nexport { palette as floodguardColors };\n\n// Export specific color sets for easy access\nexport const riskColors = palette.floodRisk;\nexport const waterColors = palette.water;\nexport const environmentColors = palette.environment;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,WAAW,QAAQ,sBAAsB;AAClD,SAASC,oBAAoB,EAAEC,qBAAqB,QAAQ,qBAAqB;;AAEjF;AACA,MAAMC,OAAO,GAAGF,oBAAoB,CAAC,CAAC;;AAEtC;AACA,OAAO,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;EACzC,OAAOJ,WAAW,CAAC;IACjBG,OAAO,EAAE;MACPE,IAAI,EAAE,OAAO;MAEb;MACAC,OAAO,EAAEH,OAAO,CAACG,OAAO;MACxBC,SAAS,EAAEJ,OAAO,CAACI,SAAS;MAC5BC,KAAK,EAAEL,OAAO,CAACK,KAAK;MACpBC,OAAO,EAAEN,OAAO,CAACM,OAAO;MACxBC,OAAO,EAAEP,OAAO,CAACO,OAAO;MACxBC,IAAI,EAAER,OAAO,CAACQ,IAAI;MAElB;MACAC,UAAU,EAAE;QACVC,OAAO,EAAEV,OAAO,CAACS,UAAU,CAACC,OAAO;QACnCC,KAAK,EAAEX,OAAO,CAACS,UAAU,CAACE,KAAK;QAC/BC,QAAQ,EAAEZ,OAAO,CAACS,UAAU,CAACG;MAC/B,CAAC;MAED;MACAC,IAAI,EAAE;QACJV,OAAO,EAAEH,OAAO,CAACa,IAAI,CAACV,OAAO;QAC7BC,SAAS,EAAEJ,OAAO,CAACa,IAAI,CAACT,SAAS;QACjCU,QAAQ,EAAEd,OAAO,CAACa,IAAI,CAACC,QAAQ;QAC/BC,QAAQ,EAAEf,OAAO,CAACa,IAAI,CAACE;MACzB,CAAC;MAED;MACAC,OAAO,EAAEhB,OAAO,CAACgB,OAAO;MAExB;MACAC,SAAS,EAAEjB,OAAO,CAACiB,SAAS;MAC5BC,KAAK,EAAElB,OAAO,CAACkB,KAAK;MACpBC,WAAW,EAAEnB,OAAO,CAACmB,WAAW;MAChCC,OAAO,EAAEpB,OAAO,CAACoB,OAAO;MACxBC,MAAM,EAAErB,OAAO,CAACqB;IAClB,CAAC;IAEDC,UAAU,EAAE;MACVC,UAAU,EAAE,qDAAqD;MAEjEC,EAAE,EAAE;QACFC,QAAQ,EAAE,4BAA4B;QACtCC,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,SAAS;QACxBC,UAAU,EAAE,GAAG;QACfC,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV;MACtB,CAAC;MAED2B,EAAE,EAAE;QACFL,QAAQ,EAAE,2BAA2B;QACrCC,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,SAAS;QACxBC,UAAU,EAAE,GAAG;QACfC,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV;MACtB,CAAC;MAED4B,EAAE,EAAE;QACFN,QAAQ,EAAE,0BAA0B;QACpCC,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,SAAS;QACxBC,UAAU,EAAE,GAAG;QACfC,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV;MACtB,CAAC;MAED6B,EAAE,EAAE;QACFP,QAAQ,EAAE,gCAAgC;QAC1CC,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,KAAK;QACpBC,UAAU,EAAE,GAAG;QACfC,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV;MACtB,CAAC;MAED8B,EAAE,EAAE;QACFR,QAAQ,EAAE,4BAA4B;QACtCC,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,KAAK;QACpBC,UAAU,EAAE,GAAG;QACfC,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV;MACtB,CAAC;MAED+B,EAAE,EAAE;QACFT,QAAQ,EAAE,6BAA6B;QACvCC,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,GAAG;QACfC,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV;MACtB,CAAC;MAEDgC,KAAK,EAAE;QACLV,QAAQ,EAAE,8BAA8B;QACxCG,UAAU,EAAE,GAAG;QACfC,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACT;MACtB,CAAC;MAEDgC,KAAK,EAAE;QACLX,QAAQ,EAAE,oCAAoC;QAC9CG,UAAU,EAAE,GAAG;QACfC,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACT;MACtB,CAAC;MAEDiC,MAAM,EAAE;QACNX,UAAU,EAAE,GAAG;QACfY,aAAa,EAAE,MAAM;QACrBX,aAAa,EAAE,QAAQ;QACvBF,QAAQ,EAAE;MACZ,CAAC;MAEDc,SAAS,EAAE;QACTd,QAAQ,EAAE,oCAAoC;QAC9CC,UAAU,EAAE,GAAG;QACfE,UAAU,EAAE,GAAG;QACfC,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACT;MACtB,CAAC;MAEDoC,SAAS,EAAE;QACTf,QAAQ,EAAE,oCAAoC;QAC9CC,UAAU,EAAE,GAAG;QACfE,UAAU,EAAE,GAAG;QACfC,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACT;MACtB;IACF,CAAC;IAEDqC,KAAK,EAAE;MACLC,YAAY,EAAE;IAChB,CAAC;IAED;IACAC,OAAO,EAAE,CACP,MAAM,EACN,oCAAoC,EACpC,wEAAwE,EACxE,wEAAwE,EACxE,wEAAwE,EACxE,yEAAyE,EACzE,2EAA2E,EAC3E,2EAA2E,EAC3E,2EAA2E,EAC3E,2EAA2E,EAC3E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,EAC5E,4EAA4E,CAC7E;IAEDC,WAAW,EAAE;MACXC,MAAM,EAAE;QACNC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,GAAG;QACPC,EAAE,EAAE,GAAG;QACPC,EAAE,EAAE,IAAI;QACRC,EAAE,EAAE;MACN;IACF,CAAC;IAED;IACAC,UAAU,EAAE;MACV;MACAC,YAAY,EAAE;QACZC,cAAc,EAAE;UACdC,IAAI,EAAE;YACJC,WAAW,EAAE,EAAE;YACfC,YAAY,EAAE,EAAE;YAChB,0BAA0B,EAAE;cAC1BD,WAAW,EAAE,EAAE;cACfC,YAAY,EAAE;YAChB,CAAC;YACD,0BAA0B,EAAE;cAC1BD,WAAW,EAAE,EAAE;cACfC,YAAY,EAAE;YAChB,CAAC;YACD,2BAA2B,EAAE;cAC3BD,WAAW,EAAE,EAAE;cACfC,YAAY,EAAE;YAChB,CAAC;YACDC,QAAQ,EAAE,MAAM;YAChB,2BAA2B,EAAE;cAC3BA,QAAQ,EAAE;YACZ,CAAC;YACD,2BAA2B,EAAE;cAC3BA,QAAQ,EAAE;YACZ;UACF;QACF;MACF,CAAC;MAED;MACAC,OAAO,EAAE;QACPL,cAAc,EAAE;UACdM,SAAS,EAAE;YACTC,SAAS,EAAE,CAAC;YACZC,UAAU,EAAE,CAAC;YACbC,KAAK,EAAE;UACT,CAAC;UACDC,IAAI,EAAE;YACJC,UAAU,EAAE,EAAE;YACdT,WAAW,EAAE,EAAE;YACf,0BAA0B,EAAE;cAC1BS,UAAU,EAAE,EAAE;cACdT,WAAW,EAAE;YACf,CAAC;YACD,0BAA0B,EAAE;cAC1BS,UAAU,EAAE,EAAE;cACdT,WAAW,EAAE;YACf,CAAC;YACD,2BAA2B,EAAE;cAC3BS,UAAU,EAAE,EAAE;cACdT,WAAW,EAAE;YACf;UACF;QACF;MACF,CAAC;MAED;MACAU,QAAQ,EAAE;QACRZ,cAAc,EAAE;UACdC,IAAI,EAAE;YACJY,eAAe,EAAElE,OAAO,CAACS,UAAU,CAACE,KAAK;YACzC+B,YAAY,EAAE,EAAE;YAChByB,SAAS,EAAEpE,qBAAqB,CAACC,OAAO,CAACS,UAAU,CAACE,KAAK,CAAC;YAC1DyD,UAAU,EAAE,sBAAsB;YAClC/C,MAAM,EAAE,aAAarB,OAAO,CAACqB,MAAM,CAACgD,KAAK,EAAE;YAC3C,SAAS,EAAE;cACTF,SAAS,EAAEpE,qBAAqB,CAACC,OAAO,CAACS,UAAU,CAACE,KAAK,EAAE,GAAG,CAAC;cAC/D2D,WAAW,EAAEtE,OAAO,CAACqB,MAAM,CAACkD;YAC9B;UACF,CAAC;UACDC,UAAU,EAAE;YACVN,eAAe,EAAElE,OAAO,CAACoB,OAAO,CAACqD;UACnC,CAAC;UACDC,UAAU,EAAE;YACVR,eAAe,EAAElE,OAAO,CAACoB,OAAO,CAACuD;UACnC,CAAC;UACDC,UAAU,EAAE;YACVV,eAAe,EAAElE,OAAO,CAACoB,OAAO,CAACyD;UACnC;QACF;MACF,CAAC;MAED;MACAC,SAAS,EAAE;QACTzB,cAAc,EAAE;UACdC,IAAI,EAAE;YACJZ,YAAY,EAAE,EAAE;YAChBhB,UAAU,EAAE,GAAG;YACfY,aAAa,EAAE,MAAM;YACrB8B,UAAU,EAAE,sBAAsB;YAClCD,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE;cACTA,SAAS,EAAE,mCAAmC;cAC9CY,SAAS,EAAE;YACb,CAAC;YACD,UAAU,EAAE;cACVA,SAAS,EAAE;YACb;UACF,CAAC;UACDC,gBAAgB,EAAE;YAChBd,eAAe,EAAElE,OAAO,CAACG,OAAO,CAAC8E,IAAI;YACrCpD,KAAK,EAAE7B,OAAO,CAACG,OAAO,CAAC+E,YAAY;YACnC,SAAS,EAAE;cACThB,eAAe,EAAElE,OAAO,CAACG,OAAO,CAACgF,IAAI;cACrChB,SAAS,EAAE,cAAcnE,OAAO,CAACG,OAAO,CAAC8E,IAAI;YAC/C,CAAC;YACD,UAAU,EAAE;cACVf,eAAe,EAAElE,OAAO,CAACG,OAAO,CAACgF;YACnC;UACF,CAAC;UACDC,kBAAkB,EAAE;YAClBlB,eAAe,EAAElE,OAAO,CAACI,SAAS,CAAC6E,IAAI;YACvCpD,KAAK,EAAE7B,OAAO,CAACI,SAAS,CAAC8E,YAAY;YACrC,SAAS,EAAE;cACThB,eAAe,EAAElE,OAAO,CAACI,SAAS,CAAC+E,IAAI;cACvChB,SAAS,EAAE,cAAcnE,OAAO,CAACI,SAAS,CAAC6E,IAAI;YACjD;UACF,CAAC;UACDI,QAAQ,EAAE;YACRf,WAAW,EAAEtE,OAAO,CAACqB,MAAM,CAACkD,MAAM;YAClC1C,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV,OAAO;YAC3B,SAAS,EAAE;cACTmE,WAAW,EAAEtE,OAAO,CAACG,OAAO,CAAC8E,IAAI;cACjCf,eAAe,EAAE,GAAGlE,OAAO,CAACG,OAAO,CAAC8E,IAAI;YAC1C;UACF,CAAC;UACDpE,IAAI,EAAE;YACJgB,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV,OAAO;YAC3B,SAAS,EAAE;cACT+D,eAAe,EAAE,GAAGlE,OAAO,CAACG,OAAO,CAAC8E,IAAI;YAC1C;UACF;QACF;MACF,CAAC;MAED;MACAK,YAAY,EAAE;QACZjC,cAAc,EAAE;UACdC,IAAI,EAAE;YACJ,0BAA0B,EAAE;cAC1BY,eAAe,EAAElE,OAAO,CAACS,UAAU,CAACE,KAAK;cACzC+B,YAAY,EAAE,EAAE;cAChB0B,UAAU,EAAE,sBAAsB;cAClC,YAAY,EAAE;gBACZE,WAAW,EAAEtE,OAAO,CAACqB,MAAM,CAACkD,MAAM;gBAClCgB,WAAW,EAAE;cACf,CAAC;cACD,kBAAkB,EAAE;gBAClBjB,WAAW,EAAEtE,OAAO,CAACqB,MAAM,CAACmE;cAC9B,CAAC;cACD,wBAAwB,EAAE;gBACxBlB,WAAW,EAAEtE,OAAO,CAACG,OAAO,CAAC8E,IAAI;gBACjCM,WAAW,EAAE;cACf,CAAC;cACD,sBAAsB,EAAE;gBACtBjB,WAAW,EAAEtE,OAAO,CAACK,KAAK,CAAC4E;cAC7B;YACF,CAAC;YACD,uBAAuB,EAAE;cACvBpD,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACT,SAAS;cAC7B,eAAe,EAAE;gBACfyB,KAAK,EAAE7B,OAAO,CAACG,OAAO,CAAC8E;cACzB,CAAC;cACD,aAAa,EAAE;gBACbpD,KAAK,EAAE7B,OAAO,CAACK,KAAK,CAAC4E;cACvB;YACF,CAAC;YACD,uBAAuB,EAAE;cACvBpD,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV;YACtB;UACF;QACF;MACF,CAAC;MAED;MACAsF,QAAQ,EAAE;QACRpC,cAAc,EAAE;UACdC,IAAI,EAAE;YACJZ,YAAY,EAAE,EAAE;YAChBhB,UAAU,EAAE,GAAG;YACfL,MAAM,EAAE,aAAarB,OAAO,CAACqB,MAAM,CAACgD,KAAK;UAC3C,CAAC;UACDqB,eAAe,EAAE;YACfxB,eAAe,EAAElE,OAAO,CAACiB,SAAS,CAAC0E,OAAO;YAC1C9D,KAAK,EAAE7B,OAAO,CAACO,OAAO,CAAC4E,IAAI;YAC3Bb,WAAW,EAAEtE,OAAO,CAACO,OAAO,CAAC8D,KAAK;YAClC,kBAAkB,EAAE;cAClBxC,KAAK,EAAE7B,OAAO,CAACO,OAAO,CAAC0E;YACzB;UACF,CAAC;UACDW,aAAa,EAAE;YACb1B,eAAe,EAAElE,OAAO,CAACiB,SAAS,CAAC4E,MAAM;YACzChE,KAAK,EAAE7B,OAAO,CAACK,KAAK,CAAC8E,IAAI;YACzBb,WAAW,EAAEtE,OAAO,CAACK,KAAK,CAACgE,KAAK;YAChC,kBAAkB,EAAE;cAClBxC,KAAK,EAAE7B,OAAO,CAACK,KAAK,CAAC4E;YACvB;UACF,CAAC;UACDa,eAAe,EAAE;YACf5B,eAAe,EAAElE,OAAO,CAACiB,SAAS,CAAC8E,QAAQ;YAC3ClE,KAAK,EAAE7B,OAAO,CAACM,OAAO,CAAC6E,IAAI;YAC3Bb,WAAW,EAAEtE,OAAO,CAACM,OAAO,CAAC+D,KAAK;YAClC,kBAAkB,EAAE;cAClBxC,KAAK,EAAE7B,OAAO,CAACM,OAAO,CAAC2E;YACzB;UACF,CAAC;UACDe,YAAY,EAAE;YACZ9B,eAAe,EAAE,GAAGlE,OAAO,CAACQ,IAAI,CAACyE,IAAI,IAAI;YACzCpD,KAAK,EAAE7B,OAAO,CAACQ,IAAI,CAAC2E,IAAI;YACxBb,WAAW,EAAEtE,OAAO,CAACQ,IAAI,CAAC6D,KAAK;YAC/B,kBAAkB,EAAE;cAClBxC,KAAK,EAAE7B,OAAO,CAACQ,IAAI,CAACyE;YACtB;UACF;QACF;MACF,CAAC;MAED;MACAgB,OAAO,EAAE;QACP5C,cAAc,EAAE;UACdC,IAAI,EAAE;YACJZ,YAAY,EAAE,CAAC;YACfhB,UAAU,EAAE,GAAG;YACfL,MAAM,EAAE,aAAarB,OAAO,CAACqB,MAAM,CAACgD,KAAK,EAAE;YAC3CD,UAAU,EAAE,sBAAsB;YAClC,SAAS,EAAE;cACTW,SAAS,EAAE,kBAAkB;cAC7BZ,SAAS,EAAE;YACb;UACF,CAAC;UACD+B,YAAY,EAAE;YACZhC,eAAe,EAAElE,OAAO,CAACG,OAAO,CAAC8E,IAAI;YACrCpD,KAAK,EAAE7B,OAAO,CAACG,OAAO,CAAC+E,YAAY;YACnCZ,WAAW,EAAEtE,OAAO,CAACG,OAAO,CAAC8E;UAC/B,CAAC;UACDkB,cAAc,EAAE;YACdjC,eAAe,EAAElE,OAAO,CAACI,SAAS,CAAC6E,IAAI;YACvCpD,KAAK,EAAE7B,OAAO,CAACI,SAAS,CAAC8E,YAAY;YACrCZ,WAAW,EAAEtE,OAAO,CAACI,SAAS,CAAC6E;UACjC,CAAC;UACDmB,YAAY,EAAE;YACZlC,eAAe,EAAElE,OAAO,CAACO,OAAO,CAAC0E,IAAI;YACrCpD,KAAK,EAAE7B,OAAO,CAACO,OAAO,CAAC2E,YAAY;YACnCZ,WAAW,EAAEtE,OAAO,CAACO,OAAO,CAAC0E;UAC/B,CAAC;UACDoB,UAAU,EAAE;YACVnC,eAAe,EAAElE,OAAO,CAACK,KAAK,CAAC4E,IAAI;YACnCpD,KAAK,EAAE7B,OAAO,CAACK,KAAK,CAAC6E,YAAY;YACjCZ,WAAW,EAAEtE,OAAO,CAACK,KAAK,CAAC4E;UAC7B,CAAC;UACDqB,YAAY,EAAE;YACZpC,eAAe,EAAElE,OAAO,CAACM,OAAO,CAAC2E,IAAI;YACrCpD,KAAK,EAAE7B,OAAO,CAACM,OAAO,CAAC4E,YAAY;YACnCZ,WAAW,EAAEtE,OAAO,CAACM,OAAO,CAAC2E;UAC/B,CAAC;UACDsB,SAAS,EAAE;YACTrC,eAAe,EAAElE,OAAO,CAACQ,IAAI,CAACyE,IAAI;YAClCpD,KAAK,EAAE7B,OAAO,CAACQ,IAAI,CAAC0E,YAAY;YAChCZ,WAAW,EAAEtE,OAAO,CAACQ,IAAI,CAACyE;UAC5B;QACF;MACF,CAAC;MAED;MACAuB,OAAO,EAAE;QACPnD,cAAc,EAAE;UACdC,IAAI,EAAE;YACJY,eAAe,EAAElE,OAAO,CAACS,UAAU,CAACE,KAAK;YACzC+B,YAAY,EAAE,EAAE;YAChBrB,MAAM,EAAE,aAAarB,OAAO,CAACqB,MAAM,CAACgD,KAAK,EAAE;YAC3CD,UAAU,EAAE,sBAAsB;YAClC,SAAS,EAAE;cACTE,WAAW,EAAEtE,OAAO,CAACqB,MAAM,CAACkD,MAAM;cAClCQ,SAAS,EAAE,kBAAkB;cAC7BZ,SAAS,EAAE;YACb;UACF;QACF;MACF,CAAC;MAED;MACAsC,aAAa,EAAE;QACbpD,cAAc,EAAE;UACdC,IAAI,EAAE;YACJzB,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV;UACtB,CAAC;UACDqB,EAAE,EAAE;YACFK,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV,OAAO;YAC3BuB,UAAU,EAAE;UACd,CAAC;UACDI,EAAE,EAAE;YACFD,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV,OAAO;YAC3BuB,UAAU,EAAE;UACd,CAAC;UACDK,EAAE,EAAE;YACFF,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV,OAAO;YAC3BuB,UAAU,EAAE;UACd,CAAC;UACDM,EAAE,EAAE;YACFH,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV,OAAO;YAC3BuB,UAAU,EAAE;UACd,CAAC;UACDO,EAAE,EAAE;YACFJ,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV,OAAO;YAC3BuB,UAAU,EAAE;UACd,CAAC;UACDQ,EAAE,EAAE;YACFL,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACV,OAAO;YAC3BuB,UAAU,EAAE;UACd,CAAC;UACDa,SAAS,EAAE;YACTV,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACT;UACtB,CAAC;UACDoC,SAAS,EAAE;YACTX,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACT;UACtB,CAAC;UACD+B,KAAK,EAAE;YACLN,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACT;UACtB,CAAC;UACDgC,KAAK,EAAE;YACLP,KAAK,EAAE7B,OAAO,CAACa,IAAI,CAACT;UACtB;QACF;MACF;IACF;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMsG,eAAe,GAAGzG,qBAAqB,CAAC,CAAC;;AAEtD;AACA,SAASD,OAAO,IAAI2G,gBAAgB;;AAEpC;AACA,OAAO,MAAMC,UAAU,GAAG5G,OAAO,CAACiB,SAAS;AAC3C,OAAO,MAAM4F,WAAW,GAAG7G,OAAO,CAACkB,KAAK;AACxC,OAAO,MAAM4F,iBAAiB,GAAG9G,OAAO,CAACmB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}