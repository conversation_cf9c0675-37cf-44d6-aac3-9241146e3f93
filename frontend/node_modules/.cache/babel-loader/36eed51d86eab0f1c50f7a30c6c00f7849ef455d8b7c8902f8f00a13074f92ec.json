{"ast": null, "code": "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function (val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error('val is not a non-empty string or a valid number. val=' + JSON.stringify(val));\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(str);\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}", "map": {"version": 3, "names": ["s", "m", "h", "d", "w", "y", "module", "exports", "val", "options", "type", "length", "parse", "isFinite", "long", "fmtLong", "fmtShort", "Error", "JSON", "stringify", "str", "String", "match", "exec", "n", "parseFloat", "toLowerCase", "undefined", "ms", "msAbs", "Math", "abs", "round", "plural", "name", "isPlural"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/ms/index.js"], "sourcesContent": ["/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function (val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,IAAIA,CAAC,GAAG,IAAI;AACZ,IAAIC,CAAC,GAAGD,CAAC,GAAG,EAAE;AACd,IAAIE,CAAC,GAAGD,CAAC,GAAG,EAAE;AACd,IAAIE,CAAC,GAAGD,CAAC,GAAG,EAAE;AACd,IAAIE,CAAC,GAAGD,CAAC,GAAG,CAAC;AACb,IAAIE,CAAC,GAAGF,CAAC,GAAG,MAAM;;AAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAG,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAEC,OAAO,EAAE;EACvCA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIC,IAAI,GAAG,OAAOF,GAAG;EACrB,IAAIE,IAAI,KAAK,QAAQ,IAAIF,GAAG,CAACG,MAAM,GAAG,CAAC,EAAE;IACvC,OAAOC,KAAK,CAACJ,GAAG,CAAC;EACnB,CAAC,MAAM,IAAIE,IAAI,KAAK,QAAQ,IAAIG,QAAQ,CAACL,GAAG,CAAC,EAAE;IAC7C,OAAOC,OAAO,CAACK,IAAI,GAAGC,OAAO,CAACP,GAAG,CAAC,GAAGQ,QAAQ,CAACR,GAAG,CAAC;EACpD;EACA,MAAM,IAAIS,KAAK,CACb,uDAAuD,GACrDC,IAAI,CAACC,SAAS,CAACX,GAAG,CACtB,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASI,KAAKA,CAACQ,GAAG,EAAE;EAClBA,GAAG,GAAGC,MAAM,CAACD,GAAG,CAAC;EACjB,IAAIA,GAAG,CAACT,MAAM,GAAG,GAAG,EAAE;IACpB;EACF;EACA,IAAIW,KAAK,GAAG,kIAAkI,CAACC,IAAI,CACjJH,GACF,CAAC;EACD,IAAI,CAACE,KAAK,EAAE;IACV;EACF;EACA,IAAIE,CAAC,GAAGC,UAAU,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;EAC5B,IAAIZ,IAAI,GAAG,CAACY,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,EAAEI,WAAW,CAAC,CAAC;EAC3C,QAAQhB,IAAI;IACV,KAAK,OAAO;IACZ,KAAK,MAAM;IACX,KAAK,KAAK;IACV,KAAK,IAAI;IACT,KAAK,GAAG;MACN,OAAOc,CAAC,GAAGnB,CAAC;IACd,KAAK,OAAO;IACZ,KAAK,MAAM;IACX,KAAK,GAAG;MACN,OAAOmB,CAAC,GAAGpB,CAAC;IACd,KAAK,MAAM;IACX,KAAK,KAAK;IACV,KAAK,GAAG;MACN,OAAOoB,CAAC,GAAGrB,CAAC;IACd,KAAK,OAAO;IACZ,KAAK,MAAM;IACX,KAAK,KAAK;IACV,KAAK,IAAI;IACT,KAAK,GAAG;MACN,OAAOqB,CAAC,GAAGtB,CAAC;IACd,KAAK,SAAS;IACd,KAAK,QAAQ;IACb,KAAK,MAAM;IACX,KAAK,KAAK;IACV,KAAK,GAAG;MACN,OAAOsB,CAAC,GAAGvB,CAAC;IACd,KAAK,SAAS;IACd,KAAK,QAAQ;IACb,KAAK,MAAM;IACX,KAAK,KAAK;IACV,KAAK,GAAG;MACN,OAAOuB,CAAC,GAAGxB,CAAC;IACd,KAAK,cAAc;IACnB,KAAK,aAAa;IAClB,KAAK,OAAO;IACZ,KAAK,MAAM;IACX,KAAK,IAAI;MACP,OAAOwB,CAAC;IACV;MACE,OAAOG,SAAS;EACpB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASX,QAAQA,CAACY,EAAE,EAAE;EACpB,IAAIC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACH,EAAE,CAAC;EACxB,IAAIC,KAAK,IAAI1B,CAAC,EAAE;IACd,OAAO2B,IAAI,CAACE,KAAK,CAACJ,EAAE,GAAGzB,CAAC,CAAC,GAAG,GAAG;EACjC;EACA,IAAI0B,KAAK,IAAI3B,CAAC,EAAE;IACd,OAAO4B,IAAI,CAACE,KAAK,CAACJ,EAAE,GAAG1B,CAAC,CAAC,GAAG,GAAG;EACjC;EACA,IAAI2B,KAAK,IAAI5B,CAAC,EAAE;IACd,OAAO6B,IAAI,CAACE,KAAK,CAACJ,EAAE,GAAG3B,CAAC,CAAC,GAAG,GAAG;EACjC;EACA,IAAI4B,KAAK,IAAI7B,CAAC,EAAE;IACd,OAAO8B,IAAI,CAACE,KAAK,CAACJ,EAAE,GAAG5B,CAAC,CAAC,GAAG,GAAG;EACjC;EACA,OAAO4B,EAAE,GAAG,IAAI;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASb,OAAOA,CAACa,EAAE,EAAE;EACnB,IAAIC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACH,EAAE,CAAC;EACxB,IAAIC,KAAK,IAAI1B,CAAC,EAAE;IACd,OAAO8B,MAAM,CAACL,EAAE,EAAEC,KAAK,EAAE1B,CAAC,EAAE,KAAK,CAAC;EACpC;EACA,IAAI0B,KAAK,IAAI3B,CAAC,EAAE;IACd,OAAO+B,MAAM,CAACL,EAAE,EAAEC,KAAK,EAAE3B,CAAC,EAAE,MAAM,CAAC;EACrC;EACA,IAAI2B,KAAK,IAAI5B,CAAC,EAAE;IACd,OAAOgC,MAAM,CAACL,EAAE,EAAEC,KAAK,EAAE5B,CAAC,EAAE,QAAQ,CAAC;EACvC;EACA,IAAI4B,KAAK,IAAI7B,CAAC,EAAE;IACd,OAAOiC,MAAM,CAACL,EAAE,EAAEC,KAAK,EAAE7B,CAAC,EAAE,QAAQ,CAAC;EACvC;EACA,OAAO4B,EAAE,GAAG,KAAK;AACnB;;AAEA;AACA;AACA;;AAEA,SAASK,MAAMA,CAACL,EAAE,EAAEC,KAAK,EAAEL,CAAC,EAAEU,IAAI,EAAE;EAClC,IAAIC,QAAQ,GAAGN,KAAK,IAAIL,CAAC,GAAG,GAAG;EAC/B,OAAOM,IAAI,CAACE,KAAK,CAACJ,EAAE,GAAGJ,CAAC,CAAC,GAAG,GAAG,GAAGU,IAAI,IAAIC,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}