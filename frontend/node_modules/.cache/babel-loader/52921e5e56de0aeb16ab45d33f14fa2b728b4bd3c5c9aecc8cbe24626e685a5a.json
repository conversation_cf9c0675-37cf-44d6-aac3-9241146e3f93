{"ast": null, "code": "export function withPane(props, context) {\n  const pane = props.pane ?? context.pane;\n  return pane ? {\n    ...props,\n    pane\n  } : props;\n}", "map": {"version": 3, "names": ["with<PERSON>ane", "props", "context", "pane"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-leaflet/core/lib/pane.js"], "sourcesContent": ["export function withPane(props, context) {\n    const pane = props.pane ?? context.pane;\n    return pane ? {\n        ...props,\n        pane\n    } : props;\n}\n"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACrC,MAAMC,IAAI,GAAGF,KAAK,CAACE,IAAI,IAAID,OAAO,CAACC,IAAI;EACvC,OAAOA,IAAI,GAAG;IACV,GAAGF,KAAK;IACRE;EACJ,CAAC,GAAGF,KAAK;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}