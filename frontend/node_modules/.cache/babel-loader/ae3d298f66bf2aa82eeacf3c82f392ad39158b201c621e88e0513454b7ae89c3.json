{"ast": null, "code": "'use strict';\n\nmodule.exports = {\n  isObject: function (obj) {\n    var type = typeof obj;\n    return type === 'function' || type === 'object' && !!obj;\n  },\n  extend: function (obj) {\n    if (!this.isObject(obj)) {\n      return obj;\n    }\n    var source, prop;\n    for (var i = 1, length = arguments.length; i < length; i++) {\n      source = arguments[i];\n      for (prop in source) {\n        if (Object.prototype.hasOwnProperty.call(source, prop)) {\n          obj[prop] = source[prop];\n        }\n      }\n    }\n    return obj;\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "isObject", "obj", "type", "extend", "source", "prop", "i", "length", "arguments", "Object", "prototype", "hasOwnProperty", "call"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/utils/object.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = {\n  isObject: function(obj) {\n    var type = typeof obj;\n    return type === 'function' || type === 'object' && !!obj;\n  }\n\n, extend: function(obj) {\n    if (!this.isObject(obj)) {\n      return obj;\n    }\n    var source, prop;\n    for (var i = 1, length = arguments.length; i < length; i++) {\n      source = arguments[i];\n      for (prop in source) {\n        if (Object.prototype.hasOwnProperty.call(source, prop)) {\n          obj[prop] = source[prop];\n        }\n      }\n    }\n    return obj;\n  }\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG;EACfC,QAAQ,EAAE,SAAAA,CAASC,GAAG,EAAE;IACtB,IAAIC,IAAI,GAAG,OAAOD,GAAG;IACrB,OAAOC,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,QAAQ,IAAI,CAAC,CAACD,GAAG;EAC1D,CAAC;EAEDE,MAAM,EAAE,SAAAA,CAASF,GAAG,EAAE;IACpB,IAAI,CAAC,IAAI,CAACD,QAAQ,CAACC,GAAG,CAAC,EAAE;MACvB,OAAOA,GAAG;IACZ;IACA,IAAIG,MAAM,EAAEC,IAAI;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,MAAM,GAAGC,SAAS,CAACD,MAAM,EAAED,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1DF,MAAM,GAAGI,SAAS,CAACF,CAAC,CAAC;MACrB,KAAKD,IAAI,IAAID,MAAM,EAAE;QACnB,IAAIK,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,MAAM,EAAEC,IAAI,CAAC,EAAE;UACtDJ,GAAG,CAACI,IAAI,CAAC,GAAGD,MAAM,CAACC,IAAI,CAAC;QAC1B;MACF;IACF;IACA,OAAOJ,GAAG;EACZ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}