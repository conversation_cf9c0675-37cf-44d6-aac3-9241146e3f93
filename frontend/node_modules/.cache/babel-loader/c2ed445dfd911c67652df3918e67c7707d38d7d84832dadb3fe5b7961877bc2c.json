{"ast": null, "code": "import { TickerStrategy } from './types.js';\nexport class Ticker {\n  constructor(_interval, _strategy = TickerStrategy.Interval, _debug) {\n    this._interval = _interval;\n    this._strategy = _strategy;\n    this._debug = _debug;\n    this._workerScript = `\n    var startTime = Date.now();\n    setInterval(function() {\n        self.postMessage(Date.now() - startTime);\n    }, ${this._interval});\n  `;\n  }\n  start(tick) {\n    this.stop();\n    if (this.shouldUseWorker()) {\n      this.runWorker(tick);\n    } else {\n      this.runInterval(tick);\n    }\n  }\n  stop() {\n    this.disposeWorker();\n    this.disposeInterval();\n  }\n  shouldUseWorker() {\n    return typeof Worker !== 'undefined' && this._strategy === TickerStrategy.Worker;\n  }\n  runWorker(tick) {\n    this._debug('Using runWorker for outgoing pings');\n    if (!this._worker) {\n      this._worker = new Worker(URL.createObjectURL(new Blob([this._workerScript], {\n        type: 'text/javascript'\n      })));\n      this._worker.onmessage = message => tick(message.data);\n    }\n  }\n  runInterval(tick) {\n    this._debug('Using runInterval for outgoing pings');\n    if (!this._timer) {\n      const startTime = Date.now();\n      this._timer = setInterval(() => {\n        tick(Date.now() - startTime);\n      }, this._interval);\n    }\n  }\n  disposeWorker() {\n    if (this._worker) {\n      this._worker.terminate();\n      delete this._worker;\n      this._debug('Outgoing ping disposeWorker');\n    }\n  }\n  disposeInterval() {\n    if (this._timer) {\n      clearInterval(this._timer);\n      delete this._timer;\n      this._debug('Outgoing ping disposeInterval');\n    }\n  }\n}", "map": {"version": 3, "names": ["TickerStrategy", "Ticker", "constructor", "_interval", "_strategy", "Interval", "_debug", "_workerScript", "start", "tick", "stop", "shouldUseWorker", "runWorker", "runInterval", "disposeWorker", "disposeInterval", "Worker", "_worker", "URL", "createObjectURL", "Blob", "type", "onmessage", "message", "data", "_timer", "startTime", "Date", "now", "setInterval", "terminate", "clearInterval"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@stomp/stompjs/src/ticker.ts"], "sourcesContent": ["import { debugFnType, TickerStrategy } from './types.js';\n\nexport class Ticker {\n  private readonly _workerScript = `\n    var startTime = Date.now();\n    setInterval(function() {\n        self.postMessage(Date.now() - startTime);\n    }, ${this._interval});\n  `;\n\n  private _worker?: Worker;\n  private _timer?: any;\n\n  constructor(\n    private readonly _interval: number,\n    private readonly _strategy = TickerStrategy.Interval,\n    private readonly _debug: debugFnType) {\n  }\n\n  public start(tick: (elapsedTime: number) => void): void {\n    this.stop();\n\n    if (this.shouldUseWorker()) {\n      this.runWorker(tick);\n    } else {\n      this.runInterval(tick);\n    }\n  }\n\n  public stop(): void {\n    this.disposeWorker();\n    this.disposeInterval();\n  }\n\n  private shouldUseWorker(): boolean {\n    return typeof(Worker) !== 'undefined' && this._strategy === TickerStrategy.Worker\n  }\n\n  private runWorker(tick: (elapsedTime: number) => void): void {\n    this._debug('Using runWorker for outgoing pings');\n    if (!this._worker) {\n      this._worker = new Worker(\n        URL.createObjectURL(\n          new Blob([this._workerScript], { type: 'text/javascript' })\n        )\n      );\n      this._worker.onmessage = (message) => tick(message.data);\n    }\n  }\n\n  private runInterval(tick: (elapsedTime: number) => void): void {\n    this._debug('Using runInterval for outgoing pings');\n    if (!this._timer) {\n      const startTime = Date.now();\n      this._timer = setInterval(() => {\n        tick(Date.now() - startTime);\n      }, this._interval);\n    }\n  }\n\n  private disposeWorker(): void {\n    if (this._worker) {\n      this._worker.terminate();\n      delete this._worker;\n      this._debug('Outgoing ping disposeWorker');\n    }\n  }\n\n  private disposeInterval(): void {\n    if (this._timer) {\n      clearInterval(this._timer);\n      delete this._timer;\n      this._debug('Outgoing ping disposeInterval');\n    }\n  }\n}\n"], "mappings": "AAAA,SAAsBA,cAAc,QAAQ,YAAY;AAExD,OAAM,MAAOC,MAAM;EAWjBC,YACmBC,SAAiB,EACjBC,SAAA,GAAYJ,cAAc,CAACK,QAAQ,EACnCC,MAAmB;IAFnB,KAAAH,SAAS,GAATA,SAAS;IACT,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAE,MAAM,GAANA,MAAM;IAbR,KAAAC,aAAa,GAAG;;;;SAI1B,IAAI,CAACJ,SAAS;GACpB;EASD;EAEOK,KAAKA,CAACC,IAAmC;IAC9C,IAAI,CAACC,IAAI,EAAE;IAEX,IAAI,IAAI,CAACC,eAAe,EAAE,EAAE;MAC1B,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC;IACtB,CAAC,MAAM;MACL,IAAI,CAACI,WAAW,CAACJ,IAAI,CAAC;IACxB;EACF;EAEOC,IAAIA,CAAA;IACT,IAAI,CAACI,aAAa,EAAE;IACpB,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQJ,eAAeA,CAAA;IACrB,OAAO,OAAOK,MAAO,KAAK,WAAW,IAAI,IAAI,CAACZ,SAAS,KAAKJ,cAAc,CAACgB,MAAM;EACnF;EAEQJ,SAASA,CAACH,IAAmC;IACnD,IAAI,CAACH,MAAM,CAAC,oCAAoC,CAAC;IACjD,IAAI,CAAC,IAAI,CAACW,OAAO,EAAE;MACjB,IAAI,CAACA,OAAO,GAAG,IAAID,MAAM,CACvBE,GAAG,CAACC,eAAe,CACjB,IAAIC,IAAI,CAAC,CAAC,IAAI,CAACb,aAAa,CAAC,EAAE;QAAEc,IAAI,EAAE;MAAiB,CAAE,CAAC,CAC5D,CACF;MACD,IAAI,CAACJ,OAAO,CAACK,SAAS,GAAIC,OAAO,IAAKd,IAAI,CAACc,OAAO,CAACC,IAAI,CAAC;IAC1D;EACF;EAEQX,WAAWA,CAACJ,IAAmC;IACrD,IAAI,CAACH,MAAM,CAAC,sCAAsC,CAAC;IACnD,IAAI,CAAC,IAAI,CAACmB,MAAM,EAAE;MAChB,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE;MAC5B,IAAI,CAACH,MAAM,GAAGI,WAAW,CAAC,MAAK;QAC7BpB,IAAI,CAACkB,IAAI,CAACC,GAAG,EAAE,GAAGF,SAAS,CAAC;MAC9B,CAAC,EAAE,IAAI,CAACvB,SAAS,CAAC;IACpB;EACF;EAEQW,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACG,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACa,SAAS,EAAE;MACxB,OAAO,IAAI,CAACb,OAAO;MACnB,IAAI,CAACX,MAAM,CAAC,6BAA6B,CAAC;IAC5C;EACF;EAEQS,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACU,MAAM,EAAE;MACfM,aAAa,CAAC,IAAI,CAACN,MAAM,CAAC;MAC1B,OAAO,IAAI,CAACA,MAAM;MAClB,IAAI,CAACnB,MAAM,CAAC,+BAA+B,CAAC;IAC9C;EACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}