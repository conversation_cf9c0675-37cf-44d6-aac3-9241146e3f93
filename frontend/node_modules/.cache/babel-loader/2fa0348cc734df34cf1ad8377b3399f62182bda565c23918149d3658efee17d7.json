{"ast": null, "code": "'use strict';\n\nexport default typeof Blob !== 'undefined' ? Blob : null;", "map": {"version": 3, "names": ["Blob"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/axios/lib/platform/browser/classes/Blob.js"], "sourcesContent": ["'use strict'\n\nexport default typeof Blob !== 'undefined' ? Blob : null\n"], "mappings": "AAAA,YAAY;;AAEZ,eAAe,OAAOA,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}