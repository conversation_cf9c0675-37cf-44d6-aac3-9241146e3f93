{"ast": null, "code": "import computeOffsets from \"../utils/computeOffsets.js\";\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n    name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "map": {"version": 3, "names": ["computeOffsets", "popperOffsets", "_ref", "state", "name", "modifiersData", "reference", "rects", "element", "popper", "strategy", "placement", "enabled", "phase", "fn", "data"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@popperjs/core/lib/modifiers/popperOffsets.js"], "sourcesContent": ["import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};"], "mappings": "AAAA,OAAOA,cAAc,MAAM,4BAA4B;AAEvD,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,IAAI,GAAGF,IAAI,CAACE,IAAI;EACpB;EACA;EACA;EACA;EACAD,KAAK,CAACE,aAAa,CAACD,IAAI,CAAC,GAAGJ,cAAc,CAAC;IACzCM,SAAS,EAAEH,KAAK,CAACI,KAAK,CAACD,SAAS;IAChCE,OAAO,EAAEL,KAAK,CAACI,KAAK,CAACE,MAAM;IAC3BC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAER,KAAK,CAACQ;EACnB,CAAC,CAAC;AACJ,CAAC,CAAC;;AAGF,eAAe;EACbP,IAAI,EAAE,eAAe;EACrBQ,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,MAAM;EACbC,EAAE,EAAEb,aAAa;EACjBc,IAAI,EAAE,CAAC;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}