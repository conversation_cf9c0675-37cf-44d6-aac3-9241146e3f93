{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Downloads/Flood copy/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { ThemeProvider } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { Container, Box, Paper, Typography, Grid, Alert, Chip } from '@mui/material';\nimport Header from './components/Header';\nimport FloodMap from './components/FloodMap';\nimport PredictionForm from './components/PredictionForm';\nimport ResultDisplay from './components/ResultDisplay';\nimport RiskFactorsChart from './components/RiskFactorsChart';\nimport TimelineRiskPredictor from './components/TimelineRiskPredictor';\nimport VoiceEmergencyAssistant from './components/VoiceEmergencyAssistant';\nimport CommunityReports from './components/CommunityReports';\nimport { SlideUp, ScaleIn } from './components/animations/AnimatedComponents';\nimport LoadingAnimation from './components/animations/LoadingAnimation';\nimport axios from 'axios';\nimport { floodguardTheme } from './theme/floodguardTheme';\nimport './components/ResponsiveLayout.css';\n\n// Use the comprehensive Floodguard theme\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = floodguardTheme;\nfunction App() {\n  _s();\n  const [mapData, setMapData] = useState([]);\n  const [options, setOptions] = useState({\n    land_cover: [],\n    soil_type: []\n  });\n  const [prediction, setPrediction] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [initialLoading, setInitialLoading] = useState(true);\n  const [showPredictionResult, setShowPredictionResult] = useState(false);\n  const [forecastSummary, setForecastSummary] = useState(null);\n  const [showForecastAlert, setShowForecastAlert] = useState(false);\n  useEffect(() => {\n    // Fetch map data and options when component mounts\n    const fetchData = async () => {\n      setInitialLoading(true);\n      let loadingTimer;\n      try {\n        const [mapResponse, optionsResponse] = await Promise.all([axios.get('/api/map-data'), axios.get('/api/options')]);\n        setMapData(mapResponse.data);\n        setOptions(optionsResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n      } finally {\n        // Add a slight delay to make the loading animation visible\n        // but ensure it gets cleared if component unmounts\n        loadingTimer = setTimeout(() => {\n          setInitialLoading(false);\n        }, 1500);\n      }\n\n      // Cleanup function to ensure loading state is reset if component unmounts\n      return () => {\n        if (loadingTimer) clearTimeout(loadingTimer);\n        setInitialLoading(false);\n      };\n    };\n    fetchData();\n  }, []);\n  const handleSubmit = async formData => {\n    setLoading(true);\n    setShowPredictionResult(false);\n    setShowForecastAlert(false);\n    try {\n      // Add a slight delay to make the loading animation visible\n      await new Promise(resolve => setTimeout(resolve, 1200));\n      const response = await axios.post('/api/predict', formData);\n      setPrediction(response.data);\n\n      // Add a slight delay before showing the result for better animation\n      setTimeout(() => {\n        setShowPredictionResult(true);\n      }, 300);\n    } catch (error) {\n      console.error('Error making prediction:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleForecastGenerated = summary => {\n    setForecastSummary(summary);\n    setShowForecastAlert(true);\n\n    // Hide the alert after 10 seconds\n    setTimeout(() => {\n      setShowForecastAlert(false);\n    }, 10000);\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(VoiceEmergencyAssistant, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), initialLoading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        minHeight: '80vh',\n        background: theme.palette.background.default\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'relative',\n          width: 200,\n          height: 200\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '100%',\n            borderRadius: '50%',\n            border: '4px solid transparent',\n            borderTopColor: theme.palette.primary.main,\n            animation: 'spin 1.5s linear infinite',\n            '@keyframes spin': {\n              '0%': {\n                transform: 'rotate(0deg)'\n              },\n              '100%': {\n                transform: 'rotate(360deg)'\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'absolute',\n            top: 15,\n            left: 15,\n            width: 'calc(100% - 30px)',\n            height: 'calc(100% - 30px)',\n            borderRadius: '50%',\n            border: '4px solid transparent',\n            borderTopColor: theme.palette.secondary.main,\n            animation: 'spin 2s linear infinite',\n            '@keyframes spin': {\n              '0%': {\n                transform: 'rotate(0deg)'\n              },\n              '100%': {\n                transform: 'rotate(360deg)'\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'absolute',\n            top: 30,\n            left: 30,\n            width: 'calc(100% - 60px)',\n            height: 'calc(100% - 60px)',\n            borderRadius: '50%',\n            border: '4px solid transparent',\n            borderTopColor: theme.palette.info.main,\n            animation: 'spin 2.5s linear infinite',\n            '@keyframes spin': {\n              '0%': {\n                transform: 'rotate(0deg)'\n              },\n              '100%': {\n                transform: 'rotate(360deg)'\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          mt: 4,\n          fontWeight: 600,\n          background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,\n          backgroundClip: 'text',\n          textFillColor: 'transparent',\n          animation: 'pulse 2s infinite',\n          '@keyframes pulse': {\n            '0%': {\n              opacity: 0.6\n            },\n            '50%': {\n              opacity: 1\n            },\n            '100%': {\n              opacity: 0.6\n            }\n          }\n        },\n        children: \"Loading Flood Prediction System...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      sx: {\n        mt: {\n          xs: 3,\n          sm: 4,\n          md: 5\n        },\n        mb: {\n          xs: 5,\n          sm: 7,\n          md: 10\n        },\n        px: {\n          xs: 2,\n          sm: 3,\n          md: 4\n        },\n        py: {\n          xs: 4,\n          sm: 5,\n          md: 6\n        },\n        overflow: 'hidden',\n        backgroundColor: theme.palette.background.elevated,\n        borderRadius: '24px',\n        boxShadow: `\n              inset 1px 1px 2px rgba(255, 255, 255, 0.5),\n              inset -1px -1px 2px rgba(174, 174, 192, 0.3)\n            `,\n        position: 'relative',\n        zIndex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: {\n          xs: 3,\n          sm: 4,\n          md: 5\n        },\n        alignItems: \"stretch\",\n        sx: {\n          '& .MuiGrid-item': {\n            display: 'flex',\n            flexDirection: 'column'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 3,\n            sx: {\n              p: {\n                xs: 4,\n                sm: 5,\n                md: 6\n              },\n              mb: {\n                xs: 3,\n                sm: 4,\n                md: 5\n              },\n              backgroundColor: theme.palette.background.paper,\n              position: 'relative',\n              overflow: 'hidden',\n              borderRadius: 3,\n              transition: 'all 0.3s ease-in-out',\n              boxShadow: `\n                    6px 6px 12px rgba(174, 174, 192, 0.3),\n                    -6px -6px 12px rgba(255, 255, 255, 0.5)\n                  `,\n              '&:hover': {\n                boxShadow: `\n                      8px 8px 16px rgba(174, 174, 192, 0.35),\n                      -8px -8px 16px rgba(255, 255, 255, 0.6)\n                    `\n              },\n              minHeight: {\n                xs: 'auto',\n                sm: '200px'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'absolute',\n                top: 0,\n                right: 0,\n                width: '180px',\n                height: '180px',\n                background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',\n                borderRadius: '0 0 0 100%',\n                zIndex: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'absolute',\n                bottom: 0,\n                left: 0,\n                width: '120px',\n                height: '120px',\n                background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',\n                borderRadius: '0 100% 0 0',\n                zIndex: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'relative',\n                zIndex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: {\n                    xs: 'flex-start',\n                    sm: 'center'\n                  },\n                  flexDirection: {\n                    xs: 'column',\n                    sm: 'row'\n                  },\n                  mb: {\n                    xs: 3,\n                    sm: 4\n                  },\n                  gap: {\n                    xs: 2,\n                    sm: 0\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mr: {\n                      xs: 0,\n                      sm: 2\n                    },\n                    p: {\n                      xs: 1.5,\n                      sm: 2\n                    },\n                    borderRadius: '50%',\n                    background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    boxShadow: '0 4px 12px rgba(76, 201, 240, 0.15)',\n                    alignSelf: {\n                      xs: 'center',\n                      sm: 'flex-start'\n                    }\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    component: \"span\",\n                    children: \"\\uD83C\\uDF0A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h2\",\n                  component: \"h1\",\n                  sx: {\n                    background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,\n                    backgroundClip: 'text',\n                    textFillColor: 'transparent',\n                    fontWeight: 800,\n                    letterSpacing: '-0.5px',\n                    fontSize: {\n                      xs: '2rem',\n                      sm: '2.5rem',\n                      md: '3rem'\n                    },\n                    textAlign: {\n                      xs: 'center',\n                      sm: 'left'\n                    },\n                    lineHeight: 1.2\n                  },\n                  children: \"Flood Risk Prediction System\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                paragraph: true,\n                sx: {\n                  fontSize: {\n                    xs: '1rem',\n                    sm: '1.1rem'\n                  },\n                  maxWidth: {\n                    xs: '100%',\n                    sm: '90%'\n                  },\n                  color: 'text.secondary',\n                  lineHeight: 1.6,\n                  mb: {\n                    xs: 3,\n                    sm: 4\n                  },\n                  textAlign: {\n                    xs: 'center',\n                    sm: 'left'\n                  }\n                },\n                children: \"This interactive tool helps predict flood risk based on various environmental and geographical factors. For Indian cities, we provide accurate predictions using historical flood data and real-time weather conditions.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  gap: {\n                    xs: 1.5,\n                    sm: 2\n                  },\n                  flexWrap: 'wrap',\n                  mt: {\n                    xs: 3,\n                    sm: 4\n                  },\n                  justifyContent: {\n                    xs: 'center',\n                    sm: 'flex-start'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Real-time Weather Data\",\n                  color: \"primary\",\n                  size: \"medium\",\n                  icon: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u26C8\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 29\n                  }, this),\n                  sx: {\n                    fontWeight: 500,\n                    px: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Historical Flood Analysis\",\n                  color: \"info\",\n                  size: \"medium\",\n                  icon: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\uD83D\\uDCCA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 29\n                  }, this),\n                  sx: {\n                    fontWeight: 500,\n                    px: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Indian Cities Database\",\n                  color: \"success\",\n                  size: \"medium\",\n                  icon: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\uD83C\\uDFD9\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 29\n                  }, this),\n                  sx: {\n                    fontWeight: 500,\n                    px: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 3,\n            sx: {\n              p: {\n                xs: 3,\n                sm: 4,\n                md: 5\n              },\n              height: '100%',\n              minHeight: {\n                xs: 'auto',\n                lg: '600px'\n              },\n              position: 'relative',\n              overflow: 'hidden',\n              borderRadius: 3,\n              transition: 'all 0.3s ease-in-out',\n              '&:hover': {\n                boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n              },\n              display: 'flex',\n              flexDirection: 'column'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: '100px',\n                height: '100px',\n                background: 'radial-gradient(circle, rgba(58,134,255,0.08) 0%, rgba(0,0,0,0) 70%)',\n                borderRadius: '0 0 100% 0',\n                zIndex: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'absolute',\n                bottom: 0,\n                right: 0,\n                width: '80px',\n                height: '80px',\n                background: 'radial-gradient(circle, rgba(76,201,240,0.05) 0%, rgba(0,0,0,0) 70%)',\n                borderRadius: '100% 0 0 0',\n                zIndex: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'relative',\n                zIndex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: {\n                    xs: 'flex-start',\n                    sm: 'center'\n                  },\n                  flexDirection: {\n                    xs: 'column',\n                    sm: 'row'\n                  },\n                  mb: {\n                    xs: 3,\n                    sm: 4\n                  },\n                  gap: {\n                    xs: 2,\n                    sm: 0\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mr: {\n                      xs: 0,\n                      sm: 2\n                    },\n                    p: {\n                      xs: 1,\n                      sm: 1.5\n                    },\n                    borderRadius: '12px',\n                    background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    alignSelf: {\n                      xs: 'center',\n                      sm: 'flex-start'\n                    }\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h5\",\n                    component: \"span\",\n                    children: \"\\uD83D\\uDCDD\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 700,\n                    color: theme.palette.primary.main,\n                    letterSpacing: '-0.5px',\n                    fontSize: {\n                      xs: '1.5rem',\n                      sm: '2rem',\n                      md: '2.125rem'\n                    },\n                    textAlign: {\n                      xs: 'center',\n                      sm: 'left'\n                    }\n                  },\n                  children: \"Predict Flood Risk\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                sx: {\n                  mb: {\n                    xs: 3,\n                    sm: 4\n                  },\n                  color: 'text.secondary',\n                  maxWidth: '100%',\n                  textAlign: {\n                    xs: 'center',\n                    sm: 'left'\n                  }\n                },\n                children: \"Enter location and environmental factors to get a precise flood risk assessment. For Indian cities, we provide enhanced accuracy using historical data.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(PredictionForm, {\n                options: options,\n                onSubmit: handleSubmit,\n                loading: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 3,\n            sx: {\n              p: {\n                xs: 3,\n                sm: 4,\n                md: 5\n              },\n              height: '100%',\n              minHeight: {\n                xs: 'auto',\n                lg: '600px'\n              },\n              position: 'relative',\n              overflow: 'hidden',\n              borderRadius: 3,\n              transition: 'all 0.3s ease-in-out',\n              '&:hover': {\n                boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n              },\n              display: 'flex',\n              flexDirection: 'column'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'absolute',\n                top: 0,\n                right: 0,\n                width: '100px',\n                height: '100px',\n                background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',\n                borderRadius: '0 0 0 100%',\n                zIndex: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'absolute',\n                bottom: 0,\n                left: 0,\n                width: '80px',\n                height: '80px',\n                background: 'radial-gradient(circle, rgba(255,89,94,0.05) 0%, rgba(0,0,0,0) 70%)',\n                borderRadius: '0 100% 0 0',\n                zIndex: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'relative',\n                zIndex: 1\n              },\n              children: loading ? /*#__PURE__*/_jsxDEV(LoadingAnimation, {\n                theme: theme\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 21\n              }, this) : prediction && showPredictionResult ? /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  opacity: showPredictionResult ? 1 : 0,\n                  transform: showPredictionResult ? 'translateY(0)' : 'translateY(20px)',\n                  transition: 'all 0.5s ease-in-out'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mr: 2,\n                      p: 1,\n                      borderRadius: '12px',\n                      background: 'linear-gradient(135deg, rgba(76, 201, 240, 0.1) 0%, rgba(58, 134, 255, 0.1) 100%)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      component: \"span\",\n                      children: \"\\uD83D\\uDCCA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: theme.palette.primary.main,\n                      letterSpacing: '-0.5px'\n                    },\n                    children: \"Risk Assessment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ResultDisplay, {\n                  prediction: prediction\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  py: 8,\n                  opacity: loading ? 0 : 1,\n                  transition: 'opacity 0.3s ease-in-out'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 100,\n                    height: 100,\n                    borderRadius: '50%',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                    boxShadow: '0 8px 32px rgba(76, 201, 240, 0.12)',\n                    mb: 3,\n                    animation: 'float 3s ease-in-out infinite',\n                    '@keyframes float': {\n                      '0%': {\n                        transform: 'translateY(0px)'\n                      },\n                      '50%': {\n                        transform: 'translateY(-10px)'\n                      },\n                      '100%': {\n                        transform: 'translateY(0px)'\n                      }\n                    }\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h2\",\n                    component: \"span\",\n                    children: \"\\uD83D\\uDCCA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  sx: {\n                    fontWeight: 600,\n                    color: theme.palette.primary.main,\n                    textAlign: 'center',\n                    mb: 1\n                  },\n                  children: \"Results will appear here\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  color: \"text.secondary\",\n                  sx: {\n                    textAlign: 'center',\n                    maxWidth: '80%',\n                    mx: 'auto'\n                  },\n                  children: \"Fill out the form on the left to generate a detailed flood risk assessment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this), prediction && prediction.risk_assessment && showPredictionResult && /*#__PURE__*/_jsxDEV(SlideUp, {\n          delay: 200,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 3,\n              sx: {\n                p: {\n                  xs: 3,\n                  md: 4\n                },\n                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',\n                borderRadius: 2,\n                position: 'relative',\n                overflow: 'hidden',\n                transition: 'all 0.3s ease-in-out',\n                '&:hover': {\n                  boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: 'absolute',\n                  top: 0,\n                  right: 0,\n                  width: '150px',\n                  height: '150px',\n                  background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',\n                  borderRadius: '0 0 0 100%',\n                  zIndex: 0\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: 'absolute',\n                  bottom: 0,\n                  left: 0,\n                  width: '120px',\n                  height: '120px',\n                  background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',\n                  borderRadius: '0 100% 0 0',\n                  zIndex: 0\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: 'relative',\n                  zIndex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mr: 2,\n                      p: 1,\n                      borderRadius: '12px',\n                      background: 'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      component: \"span\",\n                      children: \"\\uD83D\\uDCC8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 628,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: theme.palette.primary.dark,\n                      letterSpacing: '-0.5px'\n                    },\n                    children: \"Risk Factor Analysis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    mb: 3,\n                    color: 'text.secondary',\n                    maxWidth: '800px'\n                  },\n                  children: [\"This chart shows the contribution of different environmental and geographical factors to the overall flood risk assessment.\", prediction.accurate_data && \" For Indian cities, this includes historical flood data and real-time weather conditions.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(RiskFactorsChart, {\n                  riskAssessment: prediction.risk_assessment\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 15\n        }, this), showForecastAlert && forecastSummary && /*#__PURE__*/_jsxDEV(ScaleIn, {\n          delay: 100,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              severity: forecastSummary.maxRiskScore > 70 ? \"error\" : forecastSummary.maxRiskScore > 40 ? \"warning\" : \"info\",\n              variant: \"filled\",\n              sx: {\n                mb: 3,\n                borderRadius: 2,\n                boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',\n                '& .MuiAlert-icon': {\n                  fontSize: '1.8rem'\n                },\n                p: 2,\n                animation: 'pulse 2s infinite',\n                '@keyframes pulse': {\n                  '0%': {\n                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)'\n                  },\n                  '50%': {\n                    boxShadow: '0 8px 36px rgba(0, 0, 0, 0.25)'\n                  },\n                  '100%': {\n                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)'\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'flex-start'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flexGrow: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      fontWeight: 'bold',\n                      mb: 0.5\n                    },\n                    children: forecastSummary.maxRiskScore > 70 ? \"⚠️ High flood risk detected in the forecast!\" : forecastSummary.maxRiskScore > 40 ? \"⚠️ Medium flood risk detected in the forecast\" : \"ℹ️ Flood risk forecast generated\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: [\"Location: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: forecastSummary.location || 'Selected area'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 690,\n                      columnNumber: 37\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 689,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    children: [\"Peak risk score of \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: forecastSummary.maxRiskScore.toFixed(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 693,\n                      columnNumber: 46\n                    }, this), \" expected around \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: forecastSummary.maxRiskTime\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 693,\n                      columnNumber: 121\n                    }, this), \". Risk trend is \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: forecastSummary.riskTrend\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 694,\n                      columnNumber: 41\n                    }, this), \".\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 692,\n                    columnNumber: 25\n                  }, this), forecastSummary.maxRiskScore > 60 && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      mt: 1,\n                      fontStyle: 'italic'\n                    },\n                    children: \"Please monitor local weather updates and follow emergency guidelines.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 15\n        }, this), prediction && showPredictionResult && /*#__PURE__*/_jsxDEV(SlideUp, {\n          delay: 300,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 3,\n              sx: {\n                borderRadius: 2,\n                overflow: 'hidden',\n                transition: 'all 0.3s ease-in-out',\n                '&:hover': {\n                  boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(TimelineRiskPredictor, {\n                formData: prediction.input_data,\n                onForecastGenerated: handleForecastGenerated\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(SlideUp, {\n          delay: 200,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 3,\n              sx: {\n                p: {\n                  xs: 3,\n                  sm: 4,\n                  md: 5\n                },\n                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',\n                borderRadius: 3,\n                position: 'relative',\n                overflow: 'hidden',\n                transition: 'all 0.3s ease-in-out',\n                '&:hover': {\n                  boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: 'absolute',\n                  top: 0,\n                  right: 0,\n                  width: '150px',\n                  height: '150px',\n                  background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',\n                  borderRadius: '0 0 0 100%',\n                  zIndex: 0\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: 'relative',\n                  zIndex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mr: 2,\n                      p: 1,\n                      borderRadius: '12px',\n                      background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      component: \"span\",\n                      children: \"\\uD83D\\uDDFA\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 773,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: theme.palette.primary.dark,\n                      letterSpacing: '-0.5px'\n                    },\n                    children: \"Flood Risk Map\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 775,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  paragraph: true,\n                  sx: {\n                    mb: 3,\n                    color: 'text.secondary',\n                    maxWidth: '800px'\n                  },\n                  children: \"This interactive map shows areas with predicted flood risk based on our analysis. Green markers indicate low risk areas, while red markers indicate high risk zones. Click on markers to see detailed information.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    borderRadius: 3,\n                    overflow: 'hidden',\n                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                    height: {\n                      xs: '400px',\n                      sm: '500px',\n                      md: '600px'\n                    },\n                    mt: {\n                      xs: 2,\n                      sm: 3\n                    }\n                  },\n                  children: /*#__PURE__*/_jsxDEV(FloodMap, {\n                    mapData: mapData\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 806,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 799,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(SlideUp, {\n          delay: 300,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 3,\n              sx: {\n                p: {\n                  xs: 3,\n                  sm: 4,\n                  md: 5\n                },\n                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',\n                borderRadius: 3,\n                position: 'relative',\n                overflow: 'hidden',\n                transition: 'all 0.3s ease-in-out',\n                '&:hover': {\n                  boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: 'absolute',\n                  top: 0,\n                  right: 0,\n                  width: '150px',\n                  height: '150px',\n                  background: 'radial-gradient(circle, rgba(255,89,94,0.08) 0%, rgba(0,0,0,0) 70%)',\n                  borderRadius: '0 0 0 100%',\n                  zIndex: 0\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  position: 'relative',\n                  zIndex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mr: 2,\n                      p: 1,\n                      borderRadius: '12px',\n                      background: 'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h5\",\n                      component: \"span\",\n                      children: \"\\uD83D\\uDC65\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 855,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 844,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: theme.palette.primary.dark,\n                      letterSpacing: '-0.5px'\n                    },\n                    children: \"Community Reports\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 857,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  paragraph: true,\n                  sx: {\n                    mb: 3,\n                    color: 'text.secondary',\n                    maxWidth: '800px'\n                  },\n                  children: \"View and submit real-time flood reports from community members. These reports help validate our predictions and provide valuable on-the-ground information during flood events.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 868,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(CommunityReports, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 880,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"8UHPkuCVwEdzWw2zFuHvq2u31VU=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useState", "useEffect", "ThemeProvider", "CssBaseline", "Container", "Box", "Paper", "Typography", "Grid", "<PERSON><PERSON>", "Chip", "Header", "FloodMap", "PredictionForm", "ResultDisplay", "RiskFactorsChart", "TimelineRiskPredictor", "VoiceEmergencyAssistant", "CommunityReports", "SlideUp", "ScaleIn", "LoadingAnimation", "axios", "floodguardTheme", "jsxDEV", "_jsxDEV", "theme", "App", "_s", "mapData", "setMapData", "options", "setOptions", "land_cover", "soil_type", "prediction", "setPrediction", "loading", "setLoading", "initialLoading", "setInitialLoading", "showPredictionResult", "setShowPredictionResult", "forecastSummary", "setForecastSummary", "showForecastAlert", "setShowForecastAlert", "fetchData", "loadingTimer", "mapResponse", "optionsResponse", "Promise", "all", "get", "data", "error", "console", "setTimeout", "clearTimeout", "handleSubmit", "formData", "resolve", "response", "post", "handleForecastGenerated", "summary", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "background", "palette", "default", "position", "width", "height", "top", "left", "borderRadius", "border", "borderTopColor", "primary", "main", "animation", "transform", "secondary", "info", "variant", "mt", "fontWeight", "backgroundClip", "textFillColor", "opacity", "max<PERSON><PERSON><PERSON>", "xs", "sm", "md", "mb", "px", "py", "overflow", "backgroundColor", "elevated", "boxShadow", "zIndex", "container", "spacing", "item", "elevation", "p", "paper", "transition", "right", "bottom", "gap", "mr", "alignSelf", "component", "letterSpacing", "fontSize", "textAlign", "lineHeight", "paragraph", "color", "flexWrap", "label", "size", "icon", "lg", "onSubmit", "mx", "risk_assessment", "delay", "dark", "accurate_data", "riskAssessment", "severity", "maxRiskScore", "flexGrow", "location", "toFixed", "maxRiskTime", "riskTrend", "fontStyle", "input_data", "onForecastGenerated", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/src/App.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { ThemeProvider } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { Container, Box, Paper, Typography, Grid, Alert, Chip } from '@mui/material';\nimport Header from './components/Header';\nimport FloodMap from './components/FloodMap';\nimport PredictionForm from './components/PredictionForm';\nimport ResultDisplay from './components/ResultDisplay';\nimport RiskFactorsChart from './components/RiskFactorsChart';\nimport TimelineRiskPredictor from './components/TimelineRiskPredictor';\nimport VoiceEmergencyAssistant from './components/VoiceEmergencyAssistant';\nimport CommunityReports from './components/CommunityReports';\nimport { SlideUp, ScaleIn } from './components/animations/AnimatedComponents';\nimport LoadingAnimation from './components/animations/LoadingAnimation';\nimport axios from 'axios';\nimport { floodguardTheme } from './theme/floodguardTheme';\nimport './components/ResponsiveLayout.css';\n\n// Use the comprehensive Floodguard theme\nconst theme = floodguardTheme;\n\nfunction App() {\n  const [mapData, setMapData] = useState([]);\n  const [options, setOptions] = useState({ land_cover: [], soil_type: [] });\n  const [prediction, setPrediction] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [initialLoading, setInitialLoading] = useState(true);\n  const [showPredictionResult, setShowPredictionResult] = useState(false);\n  const [forecastSummary, setForecastSummary] = useState(null);\n  const [showForecastAlert, setShowForecastAlert] = useState(false);\n\n  useEffect(() => {\n    // Fetch map data and options when component mounts\n    const fetchData = async () => {\n      setInitialLoading(true);\n      let loadingTimer;\n\n      try {\n        const [mapResponse, optionsResponse] = await Promise.all([\n          axios.get('/api/map-data'),\n          axios.get('/api/options')\n        ]);\n        setMapData(mapResponse.data);\n        setOptions(optionsResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n      } finally {\n        // Add a slight delay to make the loading animation visible\n        // but ensure it gets cleared if component unmounts\n        loadingTimer = setTimeout(() => {\n          setInitialLoading(false);\n        }, 1500);\n      }\n\n      // Cleanup function to ensure loading state is reset if component unmounts\n      return () => {\n        if (loadingTimer) clearTimeout(loadingTimer);\n        setInitialLoading(false);\n      };\n    };\n\n    fetchData();\n  }, []);\n\n  const handleSubmit = async (formData) => {\n    setLoading(true);\n    setShowPredictionResult(false);\n    setShowForecastAlert(false);\n\n    try {\n      // Add a slight delay to make the loading animation visible\n      await new Promise(resolve => setTimeout(resolve, 1200));\n\n      const response = await axios.post('/api/predict', formData);\n      setPrediction(response.data);\n\n      // Add a slight delay before showing the result for better animation\n      setTimeout(() => {\n        setShowPredictionResult(true);\n      }, 300);\n    } catch (error) {\n      console.error('Error making prediction:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleForecastGenerated = (summary) => {\n    setForecastSummary(summary);\n    setShowForecastAlert(true);\n\n    // Hide the alert after 10 seconds\n    setTimeout(() => {\n      setShowForecastAlert(false);\n    }, 10000);\n  };\n\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Header />\n      <VoiceEmergencyAssistant />\n\n      {initialLoading ? (\n        <Box\n          sx={{\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            justifyContent: 'center',\n            minHeight: '80vh',\n            background: theme.palette.background.default\n          }}\n        >\n          <Box sx={{ position: 'relative', width: 200, height: 200 }}>\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: '100%',\n                height: '100%',\n                borderRadius: '50%',\n                border: '4px solid transparent',\n                borderTopColor: theme.palette.primary.main,\n                animation: 'spin 1.5s linear infinite',\n                '@keyframes spin': {\n                  '0%': { transform: 'rotate(0deg)' },\n                  '100%': { transform: 'rotate(360deg)' }\n                }\n              }}\n            />\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 15,\n                left: 15,\n                width: 'calc(100% - 30px)',\n                height: 'calc(100% - 30px)',\n                borderRadius: '50%',\n                border: '4px solid transparent',\n                borderTopColor: theme.palette.secondary.main,\n                animation: 'spin 2s linear infinite',\n                '@keyframes spin': {\n                  '0%': { transform: 'rotate(0deg)' },\n                  '100%': { transform: 'rotate(360deg)' }\n                }\n              }}\n            />\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 30,\n                left: 30,\n                width: 'calc(100% - 60px)',\n                height: 'calc(100% - 60px)',\n                borderRadius: '50%',\n                border: '4px solid transparent',\n                borderTopColor: theme.palette.info.main,\n                animation: 'spin 2.5s linear infinite',\n                '@keyframes spin': {\n                  '0%': { transform: 'rotate(0deg)' },\n                  '100%': { transform: 'rotate(360deg)' }\n                }\n              }}\n            />\n          </Box>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              mt: 4,\n              fontWeight: 600,\n              background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,\n              backgroundClip: 'text',\n              textFillColor: 'transparent',\n              animation: 'pulse 2s infinite',\n              '@keyframes pulse': {\n                '0%': { opacity: 0.6 },\n                '50%': { opacity: 1 },\n                '100%': { opacity: 0.6 }\n              }\n            }}\n          >\n            Loading Flood Prediction System...\n          </Typography>\n        </Box>\n      ) : (\n        <Container\n          maxWidth=\"xl\"\n          sx={{\n            mt: { xs: 3, sm: 4, md: 5 },\n            mb: { xs: 5, sm: 7, md: 10 },\n            px: { xs: 2, sm: 3, md: 4 },\n            py: { xs: 4, sm: 5, md: 6 },\n            overflow: 'hidden',\n            backgroundColor: theme.palette.background.elevated,\n            borderRadius: '24px',\n            boxShadow: `\n              inset 1px 1px 2px rgba(255, 255, 255, 0.5),\n              inset -1px -1px 2px rgba(174, 174, 192, 0.3)\n            `,\n            position: 'relative',\n            zIndex: 1\n          }}\n        >\n          <Grid\n            container\n            spacing={{ xs: 3, sm: 4, md: 5 }}\n            alignItems=\"stretch\"\n            sx={{\n              '& .MuiGrid-item': {\n                display: 'flex',\n                flexDirection: 'column'\n              }\n            }}\n          >\n            <Grid item xs={12}>\n              <Paper\n                elevation={3}\n                sx={{\n                  p: { xs: 4, sm: 5, md: 6 },\n                  mb: { xs: 3, sm: 4, md: 5 },\n                  backgroundColor: theme.palette.background.paper,\n                  position: 'relative',\n                  overflow: 'hidden',\n                  borderRadius: 3,\n                  transition: 'all 0.3s ease-in-out',\n                  boxShadow: `\n                    6px 6px 12px rgba(174, 174, 192, 0.3),\n                    -6px -6px 12px rgba(255, 255, 255, 0.5)\n                  `,\n                  '&:hover': {\n                    boxShadow: `\n                      8px 8px 16px rgba(174, 174, 192, 0.35),\n                      -8px -8px 16px rgba(255, 255, 255, 0.6)\n                    `\n                  },\n                  minHeight: { xs: 'auto', sm: '200px' }\n                }}\n              >\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    top: 0,\n                    right: 0,\n                    width: '180px',\n                    height: '180px',\n                    background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 0 0 100%',\n                    zIndex: 0\n                  }}\n                />\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    bottom: 0,\n                    left: 0,\n                    width: '120px',\n                    height: '120px',\n                    background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 100% 0 0',\n                    zIndex: 0\n                  }}\n                />\n                <Box sx={{ position: 'relative', zIndex: 1 }}>\n                  <Box sx={{\n                    display: 'flex',\n                    alignItems: { xs: 'flex-start', sm: 'center' },\n                    flexDirection: { xs: 'column', sm: 'row' },\n                    mb: { xs: 3, sm: 4 },\n                    gap: { xs: 2, sm: 0 }\n                  }}>\n                    <Box\n                      sx={{\n                        mr: { xs: 0, sm: 2 },\n                        p: { xs: 1.5, sm: 2 },\n                        borderRadius: '50%',\n                        background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        boxShadow: '0 4px 12px rgba(76, 201, 240, 0.15)',\n                        alignSelf: { xs: 'center', sm: 'flex-start' }\n                      }}\n                    >\n                      <Typography variant=\"h4\" component=\"span\">🌊</Typography>\n                    </Box>\n                    <Typography\n                      variant=\"h2\"\n                      component=\"h1\"\n                      sx={{\n                        background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,\n                        backgroundClip: 'text',\n                        textFillColor: 'transparent',\n                        fontWeight: 800,\n                        letterSpacing: '-0.5px',\n                        fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },\n                        textAlign: { xs: 'center', sm: 'left' },\n                        lineHeight: 1.2\n                      }}\n                    >\n                      Flood Risk Prediction System\n                    </Typography>\n                  </Box>\n                  <Typography\n                    variant=\"body1\"\n                    paragraph\n                    sx={{\n                      fontSize: { xs: '1rem', sm: '1.1rem' },\n                      maxWidth: { xs: '100%', sm: '90%' },\n                      color: 'text.secondary',\n                      lineHeight: 1.6,\n                      mb: { xs: 3, sm: 4 },\n                      textAlign: { xs: 'center', sm: 'left' }\n                    }}\n                  >\n                    This interactive tool helps predict flood risk based on various environmental and geographical factors.\n                    For Indian cities, we provide accurate predictions using historical flood data and real-time weather conditions.\n                  </Typography>\n                  <Box\n                    sx={{\n                      display: 'flex',\n                      gap: { xs: 1.5, sm: 2 },\n                      flexWrap: 'wrap',\n                      mt: { xs: 3, sm: 4 },\n                      justifyContent: { xs: 'center', sm: 'flex-start' }\n                    }}\n                  >\n                    <Chip\n                      label=\"Real-time Weather Data\"\n                      color=\"primary\"\n                      size=\"medium\"\n                      icon={<span>⛈️</span>}\n                      sx={{ fontWeight: 500, px: 1 }}\n                    />\n                    <Chip\n                      label=\"Historical Flood Analysis\"\n                      color=\"info\"\n                      size=\"medium\"\n                      icon={<span>📊</span>}\n                      sx={{ fontWeight: 500, px: 1 }}\n                    />\n                    <Chip\n                      label=\"Indian Cities Database\"\n                      color=\"success\"\n                      size=\"medium\"\n                      icon={<span>🏙️</span>}\n                      sx={{ fontWeight: 500, px: 1 }}\n                    />\n                  </Box>\n                </Box>\n              </Paper>\n            </Grid>\n\n            <Grid item xs={12} lg={6}>\n              <Paper\n                elevation={3}\n                sx={{\n                  p: { xs: 3, sm: 4, md: 5 },\n                  height: '100%',\n                  minHeight: { xs: 'auto', lg: '600px' },\n                  position: 'relative',\n                  overflow: 'hidden',\n                  borderRadius: 3,\n                  transition: 'all 0.3s ease-in-out',\n                  '&:hover': {\n                    boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                  },\n                  display: 'flex',\n                  flexDirection: 'column'\n                }}\n              >\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    width: '100px',\n                    height: '100px',\n                    background: 'radial-gradient(circle, rgba(58,134,255,0.08) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 0 100% 0',\n                    zIndex: 0\n                  }}\n                />\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    bottom: 0,\n                    right: 0,\n                    width: '80px',\n                    height: '80px',\n                    background: 'radial-gradient(circle, rgba(76,201,240,0.05) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '100% 0 0 0',\n                    zIndex: 0\n                  }}\n                />\n                <Box sx={{ position: 'relative', zIndex: 1 }}>\n                  <Box sx={{\n                    display: 'flex',\n                    alignItems: { xs: 'flex-start', sm: 'center' },\n                    flexDirection: { xs: 'column', sm: 'row' },\n                    mb: { xs: 3, sm: 4 },\n                    gap: { xs: 2, sm: 0 }\n                  }}>\n                    <Box\n                      sx={{\n                        mr: { xs: 0, sm: 2 },\n                        p: { xs: 1, sm: 1.5 },\n                        borderRadius: '12px',\n                        background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        alignSelf: { xs: 'center', sm: 'flex-start' }\n                      }}\n                    >\n                      <Typography variant=\"h5\" component=\"span\">📝</Typography>\n                    </Box>\n                    <Typography\n                      variant=\"h4\"\n                      sx={{\n                        fontWeight: 700,\n                        color: theme.palette.primary.main,\n                        letterSpacing: '-0.5px',\n                        fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' },\n                        textAlign: { xs: 'center', sm: 'left' }\n                      }}\n                    >\n                      Predict Flood Risk\n                    </Typography>\n                  </Box>\n                  <Typography\n                    variant=\"body1\"\n                    sx={{\n                      mb: { xs: 3, sm: 4 },\n                      color: 'text.secondary',\n                      maxWidth: '100%',\n                      textAlign: { xs: 'center', sm: 'left' }\n                    }}\n                  >\n                    Enter location and environmental factors to get a precise flood risk assessment.\n                    For Indian cities, we provide enhanced accuracy using historical data.\n                  </Typography>\n                  <PredictionForm\n                    options={options}\n                    onSubmit={handleSubmit}\n                    loading={loading}\n                  />\n                </Box>\n              </Paper>\n            </Grid>\n\n            <Grid item xs={12} lg={6}>\n              <Paper\n                elevation={3}\n                sx={{\n                  p: { xs: 3, sm: 4, md: 5 },\n                  height: '100%',\n                  minHeight: { xs: 'auto', lg: '600px' },\n                  position: 'relative',\n                  overflow: 'hidden',\n                  borderRadius: 3,\n                  transition: 'all 0.3s ease-in-out',\n                  '&:hover': {\n                    boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                  },\n                  display: 'flex',\n                  flexDirection: 'column'\n                }}\n              >\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    top: 0,\n                    right: 0,\n                    width: '100px',\n                    height: '100px',\n                    background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 0 0 100%',\n                    zIndex: 0\n                  }}\n                />\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    bottom: 0,\n                    left: 0,\n                    width: '80px',\n                    height: '80px',\n                    background: 'radial-gradient(circle, rgba(255,89,94,0.05) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 100% 0 0',\n                    zIndex: 0\n                  }}\n                />\n                <Box sx={{ position: 'relative', zIndex: 1 }}>\n                  {loading ? (\n                    <LoadingAnimation theme={theme} />\n                  ) : prediction && showPredictionResult ? (\n                    <Box sx={{\n                      opacity: showPredictionResult ? 1 : 0,\n                      transform: showPredictionResult ? 'translateY(0)' : 'translateY(20px)',\n                      transition: 'all 0.5s ease-in-out'\n                    }}>\n                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                        <Box\n                          sx={{\n                            mr: 2,\n                            p: 1,\n                            borderRadius: '12px',\n                            background: 'linear-gradient(135deg, rgba(76, 201, 240, 0.1) 0%, rgba(58, 134, 255, 0.1) 100%)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                          }}\n                        >\n                          <Typography variant=\"h5\" component=\"span\">📊</Typography>\n                        </Box>\n                        <Typography\n                          variant=\"h4\"\n                          sx={{\n                            fontWeight: 700,\n                            color: theme.palette.primary.main,\n                            letterSpacing: '-0.5px'\n                          }}\n                        >\n                          Risk Assessment\n                        </Typography>\n                      </Box>\n                      <ResultDisplay prediction={prediction} />\n                    </Box>\n                  ) : (\n                    <Box sx={{\n                      display: 'flex',\n                      flexDirection: 'column',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      py: 8,\n                      opacity: loading ? 0 : 1,\n                      transition: 'opacity 0.3s ease-in-out'\n                    }}>\n                      <Box\n                        sx={{\n                          width: 100,\n                          height: 100,\n                          borderRadius: '50%',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                          boxShadow: '0 8px 32px rgba(76, 201, 240, 0.12)',\n                          mb: 3,\n                          animation: 'float 3s ease-in-out infinite',\n                          '@keyframes float': {\n                            '0%': { transform: 'translateY(0px)' },\n                            '50%': { transform: 'translateY(-10px)' },\n                            '100%': { transform: 'translateY(0px)' }\n                          }\n                        }}\n                      >\n                        <Typography variant=\"h2\" component=\"span\">📊</Typography>\n                      </Box>\n                      <Typography variant=\"h5\" sx={{ fontWeight: 600, color: theme.palette.primary.main, textAlign: 'center', mb: 1 }}>\n                        Results will appear here\n                      </Typography>\n                      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ textAlign: 'center', maxWidth: '80%', mx: 'auto' }}>\n                        Fill out the form on the left to generate a detailed flood risk assessment\n                      </Typography>\n                    </Box>\n                  )}\n                </Box>\n              </Paper>\n            </Grid>\n\n            {prediction && prediction.risk_assessment && showPredictionResult && (\n              <SlideUp delay={200}>\n                <Grid item xs={12}>\n                  <Paper\n                    elevation={3}\n                    sx={{\n                      p: { xs: 3, md: 4 },\n                      background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',\n                      borderRadius: 2,\n                      position: 'relative',\n                      overflow: 'hidden',\n                      transition: 'all 0.3s ease-in-out',\n                      '&:hover': {\n                        boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                      }\n                    }}\n                  >\n                    <Box\n                      sx={{\n                        position: 'absolute',\n                        top: 0,\n                        right: 0,\n                        width: '150px',\n                        height: '150px',\n                        background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',\n                        borderRadius: '0 0 0 100%',\n                        zIndex: 0\n                      }}\n                    />\n                    <Box\n                      sx={{\n                        position: 'absolute',\n                        bottom: 0,\n                        left: 0,\n                        width: '120px',\n                        height: '120px',\n                        background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',\n                        borderRadius: '0 100% 0 0',\n                        zIndex: 0\n                      }}\n                    />\n                    <Box sx={{ position: 'relative', zIndex: 1 }}>\n                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                        <Box\n                          sx={{\n                            mr: 2,\n                            p: 1,\n                            borderRadius: '12px',\n                            background: 'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                          }}\n                        >\n                          <Typography variant=\"h5\" component=\"span\">📈</Typography>\n                        </Box>\n                        <Typography\n                          variant=\"h4\"\n                          sx={{\n                            fontWeight: 700,\n                            color: theme.palette.primary.dark,\n                            letterSpacing: '-0.5px'\n                          }}\n                        >\n                          Risk Factor Analysis\n                        </Typography>\n                      </Box>\n                      <Typography\n                        variant=\"body1\"\n                        sx={{\n                          mb: 3,\n                          color: 'text.secondary',\n                          maxWidth: '800px'\n                        }}\n                      >\n                        This chart shows the contribution of different environmental and geographical factors to the overall flood risk assessment.\n                        {prediction.accurate_data && \" For Indian cities, this includes historical flood data and real-time weather conditions.\"}\n                      </Typography>\n                      <RiskFactorsChart riskAssessment={prediction.risk_assessment} />\n                    </Box>\n                  </Paper>\n                </Grid>\n              </SlideUp>\n            )}\n\n            {/* Forecast Alert */}\n            {showForecastAlert && forecastSummary && (\n              <ScaleIn delay={100}>\n                <Grid item xs={12}>\n                  <Alert\n                    severity={forecastSummary.maxRiskScore > 70 ? \"error\" : forecastSummary.maxRiskScore > 40 ? \"warning\" : \"info\"}\n                    variant=\"filled\"\n                    sx={{\n                      mb: 3,\n                      borderRadius: 2,\n                      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',\n                      '& .MuiAlert-icon': { fontSize: '1.8rem' },\n                      p: 2,\n                      animation: 'pulse 2s infinite',\n                      '@keyframes pulse': {\n                        '0%': { boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)' },\n                        '50%': { boxShadow: '0 8px 36px rgba(0, 0, 0, 0.25)' },\n                        '100%': { boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)' }\n                      }\n                    }}\n                  >\n                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>\n                      <Box sx={{ flexGrow: 1 }}>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 'bold', mb: 0.5 }}>\n                          {forecastSummary.maxRiskScore > 70\n                            ? \"⚠️ High flood risk detected in the forecast!\"\n                            : forecastSummary.maxRiskScore > 40\n                              ? \"⚠️ Medium flood risk detected in the forecast\"\n                              : \"ℹ️ Flood risk forecast generated\"}\n                        </Typography>\n                        <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                          Location: <strong>{forecastSummary.location || 'Selected area'}</strong>\n                        </Typography>\n                        <Typography variant=\"body1\">\n                          Peak risk score of <strong>{forecastSummary.maxRiskScore.toFixed(1)}</strong> expected around <strong>{forecastSummary.maxRiskTime}</strong>.\n                          Risk trend is <strong>{forecastSummary.riskTrend}</strong>.\n                        </Typography>\n                        {forecastSummary.maxRiskScore > 60 && (\n                          <Typography variant=\"body2\" sx={{ mt: 1, fontStyle: 'italic' }}>\n                            Please monitor local weather updates and follow emergency guidelines.\n                          </Typography>\n                        )}\n                      </Box>\n                    </Box>\n                  </Alert>\n                </Grid>\n              </ScaleIn>\n            )}\n\n            {/* Temporal Prediction Component */}\n            {prediction && showPredictionResult && (\n              <SlideUp delay={300}>\n                <Grid item xs={12}>\n                  <Paper\n                    elevation={3}\n                    sx={{\n                      borderRadius: 2,\n                      overflow: 'hidden',\n                      transition: 'all 0.3s ease-in-out',\n                      '&:hover': {\n                        boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                      }\n                    }}\n                  >\n                    <TimelineRiskPredictor\n                      formData={prediction.input_data}\n                      onForecastGenerated={handleForecastGenerated}\n                    />\n                  </Paper>\n                </Grid>\n              </SlideUp>\n            )}\n\n            <SlideUp delay={200}>\n              <Grid item xs={12}>\n                <Paper\n                  elevation={3}\n                  sx={{\n                    p: { xs: 3, sm: 4, md: 5 },\n                    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',\n                    borderRadius: 3,\n                    position: 'relative',\n                    overflow: 'hidden',\n                    transition: 'all 0.3s ease-in-out',\n                    '&:hover': {\n                      boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                    }\n                  }}\n                >\n                  <Box\n                    sx={{\n                      position: 'absolute',\n                      top: 0,\n                      right: 0,\n                      width: '150px',\n                      height: '150px',\n                      background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',\n                      borderRadius: '0 0 0 100%',\n                      zIndex: 0\n                    }}\n                  />\n                  <Box sx={{ position: 'relative', zIndex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                      <Box\n                        sx={{\n                          mr: 2,\n                          p: 1,\n                          borderRadius: '12px',\n                          background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center'\n                        }}\n                      >\n                        <Typography variant=\"h5\" component=\"span\">🗺️</Typography>\n                      </Box>\n                      <Typography\n                        variant=\"h4\"\n                        sx={{\n                          fontWeight: 700,\n                          color: theme.palette.primary.dark,\n                          letterSpacing: '-0.5px'\n                        }}\n                      >\n                        Flood Risk Map\n                      </Typography>\n                    </Box>\n                    <Typography\n                      variant=\"body1\"\n                      paragraph\n                      sx={{\n                        mb: 3,\n                        color: 'text.secondary',\n                        maxWidth: '800px'\n                      }}\n                    >\n                      This interactive map shows areas with predicted flood risk based on our analysis.\n                      Green markers indicate low risk areas, while red markers indicate high risk zones.\n                      Click on markers to see detailed information.\n                    </Typography>\n                    <Box sx={{\n                      borderRadius: 3,\n                      overflow: 'hidden',\n                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                      height: { xs: '400px', sm: '500px', md: '600px' },\n                      mt: { xs: 2, sm: 3 }\n                    }}>\n                      <FloodMap mapData={mapData} />\n                    </Box>\n                  </Box>\n                </Paper>\n              </Grid>\n            </SlideUp>\n\n            {/* Community Reports Section */}\n            <SlideUp delay={300}>\n              <Grid item xs={12}>\n                <Paper\n                  elevation={3}\n                  sx={{\n                    p: { xs: 3, sm: 4, md: 5 },\n                    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',\n                    borderRadius: 3,\n                    position: 'relative',\n                    overflow: 'hidden',\n                    transition: 'all 0.3s ease-in-out',\n                    '&:hover': {\n                      boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                    }\n                  }}\n                >\n                  <Box\n                    sx={{\n                      position: 'absolute',\n                      top: 0,\n                      right: 0,\n                      width: '150px',\n                      height: '150px',\n                      background: 'radial-gradient(circle, rgba(255,89,94,0.08) 0%, rgba(0,0,0,0) 70%)',\n                      borderRadius: '0 0 0 100%',\n                      zIndex: 0\n                    }}\n                  />\n                  <Box sx={{ position: 'relative', zIndex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                      <Box\n                        sx={{\n                          mr: 2,\n                          p: 1,\n                          borderRadius: '12px',\n                          background: 'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center'\n                        }}\n                      >\n                        <Typography variant=\"h5\" component=\"span\">👥</Typography>\n                      </Box>\n                      <Typography\n                        variant=\"h4\"\n                        sx={{\n                          fontWeight: 700,\n                          color: theme.palette.primary.dark,\n                          letterSpacing: '-0.5px'\n                        }}\n                      >\n                        Community Reports\n                      </Typography>\n                    </Box>\n                    <Typography\n                      variant=\"body1\"\n                      paragraph\n                      sx={{\n                        mb: 3,\n                        color: 'text.secondary',\n                        maxWidth: '800px'\n                      }}\n                    >\n                      View and submit real-time flood reports from community members. These reports help validate our predictions\n                      and provide valuable on-the-ground information during flood events.\n                    </Typography>\n                    <CommunityReports />\n                  </Box>\n                </Paper>\n              </Grid>\n            </SlideUp>\n          </Grid>\n        </Container>\n      )}\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,QAAQ,eAAe;AACpF,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,SAASC,OAAO,EAAEC,OAAO,QAAQ,4CAA4C;AAC7E,OAAOC,gBAAgB,MAAM,0CAA0C;AACvE,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAO,mCAAmC;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAGH,eAAe;AAE7B,SAASI,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC;IAAEiC,UAAU,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAG,CAAC,CAAC;EACzE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAEjEC,SAAS,CAAC,MAAM;IACd;IACA,MAAM8C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BP,iBAAiB,CAAC,IAAI,CAAC;MACvB,IAAIQ,YAAY;MAEhB,IAAI;QACF,MAAM,CAACC,WAAW,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACvD9B,KAAK,CAAC+B,GAAG,CAAC,eAAe,CAAC,EAC1B/B,KAAK,CAAC+B,GAAG,CAAC,cAAc,CAAC,CAC1B,CAAC;QACFvB,UAAU,CAACmB,WAAW,CAACK,IAAI,CAAC;QAC5BtB,UAAU,CAACkB,eAAe,CAACI,IAAI,CAAC;MAClC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C,CAAC,SAAS;QACR;QACA;QACAP,YAAY,GAAGS,UAAU,CAAC,MAAM;UAC9BjB,iBAAiB,CAAC,KAAK,CAAC;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV;;MAEA;MACA,OAAO,MAAM;QACX,IAAIQ,YAAY,EAAEU,YAAY,CAACV,YAAY,CAAC;QAC5CR,iBAAiB,CAAC,KAAK,CAAC;MAC1B,CAAC;IACH,CAAC;IAEDO,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvCtB,UAAU,CAAC,IAAI,CAAC;IAChBI,uBAAuB,CAAC,KAAK,CAAC;IAC9BI,oBAAoB,CAAC,KAAK,CAAC;IAE3B,IAAI;MACF;MACA,MAAM,IAAIK,OAAO,CAACU,OAAO,IAAIJ,UAAU,CAACI,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,MAAMC,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,IAAI,CAAC,cAAc,EAAEH,QAAQ,CAAC;MAC3DxB,aAAa,CAAC0B,QAAQ,CAACR,IAAI,CAAC;;MAE5B;MACAG,UAAU,CAAC,MAAM;QACff,uBAAuB,CAAC,IAAI,CAAC;MAC/B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,uBAAuB,GAAIC,OAAO,IAAK;IAC3CrB,kBAAkB,CAACqB,OAAO,CAAC;IAC3BnB,oBAAoB,CAAC,IAAI,CAAC;;IAE1B;IACAW,UAAU,CAAC,MAAM;MACfX,oBAAoB,CAAC,KAAK,CAAC;IAC7B,CAAC,EAAE,KAAK,CAAC;EACX,CAAC;EAED,oBACErB,OAAA,CAACvB,aAAa;IAACwB,KAAK,EAAEA,KAAM;IAAAwC,QAAA,gBAC1BzC,OAAA,CAACtB,WAAW;MAAAgE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACf7C,OAAA,CAACd,MAAM;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACV7C,OAAA,CAACR,uBAAuB;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAE1B/B,cAAc,gBACbd,OAAA,CAACpB,GAAG;MACFkE,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,MAAM;QACjBC,UAAU,EAAEnD,KAAK,CAACoD,OAAO,CAACD,UAAU,CAACE;MACvC,CAAE;MAAAb,QAAA,gBAEFzC,OAAA,CAACpB,GAAG;QAACkE,EAAE,EAAE;UAAES,QAAQ,EAAE,UAAU;UAAEC,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAI,CAAE;QAAAhB,QAAA,gBACzDzC,OAAA,CAACpB,GAAG;UACFkE,EAAE,EAAE;YACFS,QAAQ,EAAE,UAAU;YACpBG,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPH,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdG,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,uBAAuB;YAC/BC,cAAc,EAAE7D,KAAK,CAACoD,OAAO,CAACU,OAAO,CAACC,IAAI;YAC1CC,SAAS,EAAE,2BAA2B;YACtC,iBAAiB,EAAE;cACjB,IAAI,EAAE;gBAAEC,SAAS,EAAE;cAAe,CAAC;cACnC,MAAM,EAAE;gBAAEA,SAAS,EAAE;cAAiB;YACxC;UACF;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF7C,OAAA,CAACpB,GAAG;UACFkE,EAAE,EAAE;YACFS,QAAQ,EAAE,UAAU;YACpBG,GAAG,EAAE,EAAE;YACPC,IAAI,EAAE,EAAE;YACRH,KAAK,EAAE,mBAAmB;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BG,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,uBAAuB;YAC/BC,cAAc,EAAE7D,KAAK,CAACoD,OAAO,CAACc,SAAS,CAACH,IAAI;YAC5CC,SAAS,EAAE,yBAAyB;YACpC,iBAAiB,EAAE;cACjB,IAAI,EAAE;gBAAEC,SAAS,EAAE;cAAe,CAAC;cACnC,MAAM,EAAE;gBAAEA,SAAS,EAAE;cAAiB;YACxC;UACF;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF7C,OAAA,CAACpB,GAAG;UACFkE,EAAE,EAAE;YACFS,QAAQ,EAAE,UAAU;YACpBG,GAAG,EAAE,EAAE;YACPC,IAAI,EAAE,EAAE;YACRH,KAAK,EAAE,mBAAmB;YAC1BC,MAAM,EAAE,mBAAmB;YAC3BG,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,uBAAuB;YAC/BC,cAAc,EAAE7D,KAAK,CAACoD,OAAO,CAACe,IAAI,CAACJ,IAAI;YACvCC,SAAS,EAAE,2BAA2B;YACtC,iBAAiB,EAAE;cACjB,IAAI,EAAE;gBAAEC,SAAS,EAAE;cAAe,CAAC;cACnC,MAAM,EAAE;gBAAEA,SAAS,EAAE;cAAiB;YACxC;UACF;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN7C,OAAA,CAAClB,UAAU;QACTuF,OAAO,EAAC,IAAI;QACZvB,EAAE,EAAE;UACFwB,EAAE,EAAE,CAAC;UACLC,UAAU,EAAE,GAAG;UACfnB,UAAU,EAAE,0BAA0BnD,KAAK,CAACoD,OAAO,CAACU,OAAO,CAACC,IAAI,SAAS/D,KAAK,CAACoD,OAAO,CAACc,SAAS,CAACH,IAAI,OAAO;UAC5GQ,cAAc,EAAE,MAAM;UACtBC,aAAa,EAAE,aAAa;UAC5BR,SAAS,EAAE,mBAAmB;UAC9B,kBAAkB,EAAE;YAClB,IAAI,EAAE;cAAES,OAAO,EAAE;YAAI,CAAC;YACtB,KAAK,EAAE;cAAEA,OAAO,EAAE;YAAE,CAAC;YACrB,MAAM,EAAE;cAAEA,OAAO,EAAE;YAAI;UACzB;QACF,CAAE;QAAAjC,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEN7C,OAAA,CAACrB,SAAS;MACRgG,QAAQ,EAAC,IAAI;MACb7B,EAAE,EAAE;QACFwB,EAAE,EAAE;UAAEM,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QAC3BC,EAAE,EAAE;UAAEH,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAG,CAAC;QAC5BE,EAAE,EAAE;UAAEJ,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QAC3BG,EAAE,EAAE;UAAEL,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QAC3BI,QAAQ,EAAE,QAAQ;QAClBC,eAAe,EAAElF,KAAK,CAACoD,OAAO,CAACD,UAAU,CAACgC,QAAQ;QAClDxB,YAAY,EAAE,MAAM;QACpByB,SAAS,EAAE;AACvB;AACA;AACA,aAAa;QACD9B,QAAQ,EAAE,UAAU;QACpB+B,MAAM,EAAE;MACV,CAAE;MAAA7C,QAAA,eAEFzC,OAAA,CAACjB,IAAI;QACHwG,SAAS;QACTC,OAAO,EAAE;UAAEZ,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QACjC7B,UAAU,EAAC,SAAS;QACpBH,EAAE,EAAE;UACF,iBAAiB,EAAE;YACjBC,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE;UACjB;QACF,CAAE;QAAAP,QAAA,gBAEFzC,OAAA,CAACjB,IAAI;UAAC0G,IAAI;UAACb,EAAE,EAAE,EAAG;UAAAnC,QAAA,eAChBzC,OAAA,CAACnB,KAAK;YACJ6G,SAAS,EAAE,CAAE;YACb5C,EAAE,EAAE;cACF6C,CAAC,EAAE;gBAAEf,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE,CAAC;cAC1BC,EAAE,EAAE;gBAAEH,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE,CAAC;cAC3BK,eAAe,EAAElF,KAAK,CAACoD,OAAO,CAACD,UAAU,CAACwC,KAAK;cAC/CrC,QAAQ,EAAE,UAAU;cACpB2B,QAAQ,EAAE,QAAQ;cAClBtB,YAAY,EAAE,CAAC;cACfiC,UAAU,EAAE,sBAAsB;cAClCR,SAAS,EAAE;AAC7B;AACA;AACA,mBAAmB;cACD,SAAS,EAAE;gBACTA,SAAS,EAAE;AAC/B;AACA;AACA;cACkB,CAAC;cACDlC,SAAS,EAAE;gBAAEyB,EAAE,EAAE,MAAM;gBAAEC,EAAE,EAAE;cAAQ;YACvC,CAAE;YAAApC,QAAA,gBAEFzC,OAAA,CAACpB,GAAG;cACFkE,EAAE,EAAE;gBACFS,QAAQ,EAAE,UAAU;gBACpBG,GAAG,EAAE,CAAC;gBACNoC,KAAK,EAAE,CAAC;gBACRtC,KAAK,EAAE,OAAO;gBACdC,MAAM,EAAE,OAAO;gBACfL,UAAU,EAAE,qEAAqE;gBACjFQ,YAAY,EAAE,YAAY;gBAC1B0B,MAAM,EAAE;cACV;YAAE;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF7C,OAAA,CAACpB,GAAG;cACFkE,EAAE,EAAE;gBACFS,QAAQ,EAAE,UAAU;gBACpBwC,MAAM,EAAE,CAAC;gBACTpC,IAAI,EAAE,CAAC;gBACPH,KAAK,EAAE,OAAO;gBACdC,MAAM,EAAE,OAAO;gBACfL,UAAU,EAAE,sEAAsE;gBAClFQ,YAAY,EAAE,YAAY;gBAC1B0B,MAAM,EAAE;cACV;YAAE;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF7C,OAAA,CAACpB,GAAG;cAACkE,EAAE,EAAE;gBAAES,QAAQ,EAAE,UAAU;gBAAE+B,MAAM,EAAE;cAAE,CAAE;cAAA7C,QAAA,gBAC3CzC,OAAA,CAACpB,GAAG;gBAACkE,EAAE,EAAE;kBACPC,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE;oBAAE2B,EAAE,EAAE,YAAY;oBAAEC,EAAE,EAAE;kBAAS,CAAC;kBAC9C7B,aAAa,EAAE;oBAAE4B,EAAE,EAAE,QAAQ;oBAAEC,EAAE,EAAE;kBAAM,CAAC;kBAC1CE,EAAE,EAAE;oBAAEH,EAAE,EAAE,CAAC;oBAAEC,EAAE,EAAE;kBAAE,CAAC;kBACpBmB,GAAG,EAAE;oBAAEpB,EAAE,EAAE,CAAC;oBAAEC,EAAE,EAAE;kBAAE;gBACtB,CAAE;gBAAApC,QAAA,gBACAzC,OAAA,CAACpB,GAAG;kBACFkE,EAAE,EAAE;oBACFmD,EAAE,EAAE;sBAAErB,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE;oBAAE,CAAC;oBACpBc,CAAC,EAAE;sBAAEf,EAAE,EAAE,GAAG;sBAAEC,EAAE,EAAE;oBAAE,CAAC;oBACrBjB,YAAY,EAAE,KAAK;oBACnBR,UAAU,EAAE,mFAAmF;oBAC/FL,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,QAAQ;oBACxBmC,SAAS,EAAE,qCAAqC;oBAChDa,SAAS,EAAE;sBAAEtB,EAAE,EAAE,QAAQ;sBAAEC,EAAE,EAAE;oBAAa;kBAC9C,CAAE;kBAAApC,QAAA,eAEFzC,OAAA,CAAClB,UAAU;oBAACuF,OAAO,EAAC,IAAI;oBAAC8B,SAAS,EAAC,MAAM;oBAAA1D,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN7C,OAAA,CAAClB,UAAU;kBACTuF,OAAO,EAAC,IAAI;kBACZ8B,SAAS,EAAC,IAAI;kBACdrD,EAAE,EAAE;oBACFM,UAAU,EAAE,0BAA0BnD,KAAK,CAACoD,OAAO,CAACU,OAAO,CAACC,IAAI,SAAS/D,KAAK,CAACoD,OAAO,CAACc,SAAS,CAACH,IAAI,OAAO;oBAC5GQ,cAAc,EAAE,MAAM;oBACtBC,aAAa,EAAE,aAAa;oBAC5BF,UAAU,EAAE,GAAG;oBACf6B,aAAa,EAAE,QAAQ;oBACvBC,QAAQ,EAAE;sBAAEzB,EAAE,EAAE,MAAM;sBAAEC,EAAE,EAAE,QAAQ;sBAAEC,EAAE,EAAE;oBAAO,CAAC;oBAClDwB,SAAS,EAAE;sBAAE1B,EAAE,EAAE,QAAQ;sBAAEC,EAAE,EAAE;oBAAO,CAAC;oBACvC0B,UAAU,EAAE;kBACd,CAAE;kBAAA9D,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN7C,OAAA,CAAClB,UAAU;gBACTuF,OAAO,EAAC,OAAO;gBACfmC,SAAS;gBACT1D,EAAE,EAAE;kBACFuD,QAAQ,EAAE;oBAAEzB,EAAE,EAAE,MAAM;oBAAEC,EAAE,EAAE;kBAAS,CAAC;kBACtCF,QAAQ,EAAE;oBAAEC,EAAE,EAAE,MAAM;oBAAEC,EAAE,EAAE;kBAAM,CAAC;kBACnC4B,KAAK,EAAE,gBAAgB;kBACvBF,UAAU,EAAE,GAAG;kBACfxB,EAAE,EAAE;oBAAEH,EAAE,EAAE,CAAC;oBAAEC,EAAE,EAAE;kBAAE,CAAC;kBACpByB,SAAS,EAAE;oBAAE1B,EAAE,EAAE,QAAQ;oBAAEC,EAAE,EAAE;kBAAO;gBACxC,CAAE;gBAAApC,QAAA,EACH;cAGD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb7C,OAAA,CAACpB,GAAG;gBACFkE,EAAE,EAAE;kBACFC,OAAO,EAAE,MAAM;kBACfiD,GAAG,EAAE;oBAAEpB,EAAE,EAAE,GAAG;oBAAEC,EAAE,EAAE;kBAAE,CAAC;kBACvB6B,QAAQ,EAAE,MAAM;kBAChBpC,EAAE,EAAE;oBAAEM,EAAE,EAAE,CAAC;oBAAEC,EAAE,EAAE;kBAAE,CAAC;kBACpB3B,cAAc,EAAE;oBAAE0B,EAAE,EAAE,QAAQ;oBAAEC,EAAE,EAAE;kBAAa;gBACnD,CAAE;gBAAApC,QAAA,gBAEFzC,OAAA,CAACf,IAAI;kBACH0H,KAAK,EAAC,wBAAwB;kBAC9BF,KAAK,EAAC,SAAS;kBACfG,IAAI,EAAC,QAAQ;kBACbC,IAAI,eAAE7G,OAAA;oBAAAyC,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAE;kBACtBC,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAES,EAAE,EAAE;kBAAE;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACF7C,OAAA,CAACf,IAAI;kBACH0H,KAAK,EAAC,2BAA2B;kBACjCF,KAAK,EAAC,MAAM;kBACZG,IAAI,EAAC,QAAQ;kBACbC,IAAI,eAAE7G,OAAA;oBAAAyC,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAE;kBACtBC,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAES,EAAE,EAAE;kBAAE;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACF7C,OAAA,CAACf,IAAI;kBACH0H,KAAK,EAAC,wBAAwB;kBAC9BF,KAAK,EAAC,SAAS;kBACfG,IAAI,EAAC,QAAQ;kBACbC,IAAI,eAAE7G,OAAA;oBAAAyC,QAAA,EAAM;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAE;kBACvBC,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAES,EAAE,EAAE;kBAAE;gBAAE;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEP7C,OAAA,CAACjB,IAAI;UAAC0G,IAAI;UAACb,EAAE,EAAE,EAAG;UAACkC,EAAE,EAAE,CAAE;UAAArE,QAAA,eACvBzC,OAAA,CAACnB,KAAK;YACJ6G,SAAS,EAAE,CAAE;YACb5C,EAAE,EAAE;cACF6C,CAAC,EAAE;gBAAEf,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE,CAAC;cAC1BrB,MAAM,EAAE,MAAM;cACdN,SAAS,EAAE;gBAAEyB,EAAE,EAAE,MAAM;gBAAEkC,EAAE,EAAE;cAAQ,CAAC;cACtCvD,QAAQ,EAAE,UAAU;cACpB2B,QAAQ,EAAE,QAAQ;cAClBtB,YAAY,EAAE,CAAC;cACfiC,UAAU,EAAE,sBAAsB;cAClC,SAAS,EAAE;gBACTR,SAAS,EAAE;cACb,CAAC;cACDtC,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE;YACjB,CAAE;YAAAP,QAAA,gBAEFzC,OAAA,CAACpB,GAAG;cACFkE,EAAE,EAAE;gBACFS,QAAQ,EAAE,UAAU;gBACpBG,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPH,KAAK,EAAE,OAAO;gBACdC,MAAM,EAAE,OAAO;gBACfL,UAAU,EAAE,sEAAsE;gBAClFQ,YAAY,EAAE,YAAY;gBAC1B0B,MAAM,EAAE;cACV;YAAE;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF7C,OAAA,CAACpB,GAAG;cACFkE,EAAE,EAAE;gBACFS,QAAQ,EAAE,UAAU;gBACpBwC,MAAM,EAAE,CAAC;gBACTD,KAAK,EAAE,CAAC;gBACRtC,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdL,UAAU,EAAE,sEAAsE;gBAClFQ,YAAY,EAAE,YAAY;gBAC1B0B,MAAM,EAAE;cACV;YAAE;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF7C,OAAA,CAACpB,GAAG;cAACkE,EAAE,EAAE;gBAAES,QAAQ,EAAE,UAAU;gBAAE+B,MAAM,EAAE;cAAE,CAAE;cAAA7C,QAAA,gBAC3CzC,OAAA,CAACpB,GAAG;gBAACkE,EAAE,EAAE;kBACPC,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE;oBAAE2B,EAAE,EAAE,YAAY;oBAAEC,EAAE,EAAE;kBAAS,CAAC;kBAC9C7B,aAAa,EAAE;oBAAE4B,EAAE,EAAE,QAAQ;oBAAEC,EAAE,EAAE;kBAAM,CAAC;kBAC1CE,EAAE,EAAE;oBAAEH,EAAE,EAAE,CAAC;oBAAEC,EAAE,EAAE;kBAAE,CAAC;kBACpBmB,GAAG,EAAE;oBAAEpB,EAAE,EAAE,CAAC;oBAAEC,EAAE,EAAE;kBAAE;gBACtB,CAAE;gBAAApC,QAAA,gBACAzC,OAAA,CAACpB,GAAG;kBACFkE,EAAE,EAAE;oBACFmD,EAAE,EAAE;sBAAErB,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE;oBAAE,CAAC;oBACpBc,CAAC,EAAE;sBAAEf,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE;oBAAI,CAAC;oBACrBjB,YAAY,EAAE,MAAM;oBACpBR,UAAU,EAAE,mFAAmF;oBAC/FL,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,QAAQ;oBACxBgD,SAAS,EAAE;sBAAEtB,EAAE,EAAE,QAAQ;sBAAEC,EAAE,EAAE;oBAAa;kBAC9C,CAAE;kBAAApC,QAAA,eAEFzC,OAAA,CAAClB,UAAU;oBAACuF,OAAO,EAAC,IAAI;oBAAC8B,SAAS,EAAC,MAAM;oBAAA1D,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN7C,OAAA,CAAClB,UAAU;kBACTuF,OAAO,EAAC,IAAI;kBACZvB,EAAE,EAAE;oBACFyB,UAAU,EAAE,GAAG;oBACfkC,KAAK,EAAExG,KAAK,CAACoD,OAAO,CAACU,OAAO,CAACC,IAAI;oBACjCoC,aAAa,EAAE,QAAQ;oBACvBC,QAAQ,EAAE;sBAAEzB,EAAE,EAAE,QAAQ;sBAAEC,EAAE,EAAE,MAAM;sBAAEC,EAAE,EAAE;oBAAW,CAAC;oBACtDwB,SAAS,EAAE;sBAAE1B,EAAE,EAAE,QAAQ;sBAAEC,EAAE,EAAE;oBAAO;kBACxC,CAAE;kBAAApC,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN7C,OAAA,CAAClB,UAAU;gBACTuF,OAAO,EAAC,OAAO;gBACfvB,EAAE,EAAE;kBACFiC,EAAE,EAAE;oBAAEH,EAAE,EAAE,CAAC;oBAAEC,EAAE,EAAE;kBAAE,CAAC;kBACpB4B,KAAK,EAAE,gBAAgB;kBACvB9B,QAAQ,EAAE,MAAM;kBAChB2B,SAAS,EAAE;oBAAE1B,EAAE,EAAE,QAAQ;oBAAEC,EAAE,EAAE;kBAAO;gBACxC,CAAE;gBAAApC,QAAA,EACH;cAGD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb7C,OAAA,CAACZ,cAAc;gBACbkB,OAAO,EAAEA,OAAQ;gBACjByG,QAAQ,EAAE7E,YAAa;gBACvBtB,OAAO,EAAEA;cAAQ;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEP7C,OAAA,CAACjB,IAAI;UAAC0G,IAAI;UAACb,EAAE,EAAE,EAAG;UAACkC,EAAE,EAAE,CAAE;UAAArE,QAAA,eACvBzC,OAAA,CAACnB,KAAK;YACJ6G,SAAS,EAAE,CAAE;YACb5C,EAAE,EAAE;cACF6C,CAAC,EAAE;gBAAEf,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE,CAAC;cAC1BrB,MAAM,EAAE,MAAM;cACdN,SAAS,EAAE;gBAAEyB,EAAE,EAAE,MAAM;gBAAEkC,EAAE,EAAE;cAAQ,CAAC;cACtCvD,QAAQ,EAAE,UAAU;cACpB2B,QAAQ,EAAE,QAAQ;cAClBtB,YAAY,EAAE,CAAC;cACfiC,UAAU,EAAE,sBAAsB;cAClC,SAAS,EAAE;gBACTR,SAAS,EAAE;cACb,CAAC;cACDtC,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE;YACjB,CAAE;YAAAP,QAAA,gBAEFzC,OAAA,CAACpB,GAAG;cACFkE,EAAE,EAAE;gBACFS,QAAQ,EAAE,UAAU;gBACpBG,GAAG,EAAE,CAAC;gBACNoC,KAAK,EAAE,CAAC;gBACRtC,KAAK,EAAE,OAAO;gBACdC,MAAM,EAAE,OAAO;gBACfL,UAAU,EAAE,sEAAsE;gBAClFQ,YAAY,EAAE,YAAY;gBAC1B0B,MAAM,EAAE;cACV;YAAE;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF7C,OAAA,CAACpB,GAAG;cACFkE,EAAE,EAAE;gBACFS,QAAQ,EAAE,UAAU;gBACpBwC,MAAM,EAAE,CAAC;gBACTpC,IAAI,EAAE,CAAC;gBACPH,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdL,UAAU,EAAE,qEAAqE;gBACjFQ,YAAY,EAAE,YAAY;gBAC1B0B,MAAM,EAAE;cACV;YAAE;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF7C,OAAA,CAACpB,GAAG;cAACkE,EAAE,EAAE;gBAAES,QAAQ,EAAE,UAAU;gBAAE+B,MAAM,EAAE;cAAE,CAAE;cAAA7C,QAAA,EAC1C7B,OAAO,gBACNZ,OAAA,CAACJ,gBAAgB;gBAACK,KAAK,EAAEA;cAAM;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GAChCnC,UAAU,IAAIM,oBAAoB,gBACpChB,OAAA,CAACpB,GAAG;gBAACkE,EAAE,EAAE;kBACP4B,OAAO,EAAE1D,oBAAoB,GAAG,CAAC,GAAG,CAAC;kBACrCkD,SAAS,EAAElD,oBAAoB,GAAG,eAAe,GAAG,kBAAkB;kBACtE6E,UAAU,EAAE;gBACd,CAAE;gBAAApD,QAAA,gBACAzC,OAAA,CAACpB,GAAG;kBAACkE,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAE8B,EAAE,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,gBACxDzC,OAAA,CAACpB,GAAG;oBACFkE,EAAE,EAAE;sBACFmD,EAAE,EAAE,CAAC;sBACLN,CAAC,EAAE,CAAC;sBACJ/B,YAAY,EAAE,MAAM;sBACpBR,UAAU,EAAE,mFAAmF;sBAC/FL,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE;oBAClB,CAAE;oBAAAT,QAAA,eAEFzC,OAAA,CAAClB,UAAU;sBAACuF,OAAO,EAAC,IAAI;sBAAC8B,SAAS,EAAC,MAAM;sBAAA1D,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACN7C,OAAA,CAAClB,UAAU;oBACTuF,OAAO,EAAC,IAAI;oBACZvB,EAAE,EAAE;sBACFyB,UAAU,EAAE,GAAG;sBACfkC,KAAK,EAAExG,KAAK,CAACoD,OAAO,CAACU,OAAO,CAACC,IAAI;sBACjCoC,aAAa,EAAE;oBACjB,CAAE;oBAAA3D,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN7C,OAAA,CAACX,aAAa;kBAACqB,UAAU,EAAEA;gBAAW;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,gBAEN7C,OAAA,CAACpB,GAAG;gBAACkE,EAAE,EAAE;kBACPC,OAAO,EAAE,MAAM;kBACfC,aAAa,EAAE,QAAQ;kBACvBC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxB+B,EAAE,EAAE,CAAC;kBACLP,OAAO,EAAE9D,OAAO,GAAG,CAAC,GAAG,CAAC;kBACxBiF,UAAU,EAAE;gBACd,CAAE;gBAAApD,QAAA,gBACAzC,OAAA,CAACpB,GAAG;kBACFkE,EAAE,EAAE;oBACFU,KAAK,EAAE,GAAG;oBACVC,MAAM,EAAE,GAAG;oBACXG,YAAY,EAAE,KAAK;oBACnBb,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,QAAQ;oBACxBE,UAAU,EAAE,mFAAmF;oBAC/FiC,SAAS,EAAE,qCAAqC;oBAChDN,EAAE,EAAE,CAAC;oBACLd,SAAS,EAAE,+BAA+B;oBAC1C,kBAAkB,EAAE;sBAClB,IAAI,EAAE;wBAAEC,SAAS,EAAE;sBAAkB,CAAC;sBACtC,KAAK,EAAE;wBAAEA,SAAS,EAAE;sBAAoB,CAAC;sBACzC,MAAM,EAAE;wBAAEA,SAAS,EAAE;sBAAkB;oBACzC;kBACF,CAAE;kBAAAzB,QAAA,eAEFzC,OAAA,CAAClB,UAAU;oBAACuF,OAAO,EAAC,IAAI;oBAAC8B,SAAS,EAAC,MAAM;oBAAA1D,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN7C,OAAA,CAAClB,UAAU;kBAACuF,OAAO,EAAC,IAAI;kBAACvB,EAAE,EAAE;oBAAEyB,UAAU,EAAE,GAAG;oBAAEkC,KAAK,EAAExG,KAAK,CAACoD,OAAO,CAACU,OAAO,CAACC,IAAI;oBAAEsC,SAAS,EAAE,QAAQ;oBAAEvB,EAAE,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,EAAC;gBAEjH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7C,OAAA,CAAClB,UAAU;kBAACuF,OAAO,EAAC,OAAO;kBAACoC,KAAK,EAAC,gBAAgB;kBAAC3D,EAAE,EAAE;oBAAEwD,SAAS,EAAE,QAAQ;oBAAE3B,QAAQ,EAAE,KAAK;oBAAEqC,EAAE,EAAE;kBAAO,CAAE;kBAAAvE,QAAA,EAAC;gBAE7G;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAENnC,UAAU,IAAIA,UAAU,CAACuG,eAAe,IAAIjG,oBAAoB,iBAC/DhB,OAAA,CAACN,OAAO;UAACwH,KAAK,EAAE,GAAI;UAAAzE,QAAA,eAClBzC,OAAA,CAACjB,IAAI;YAAC0G,IAAI;YAACb,EAAE,EAAE,EAAG;YAAAnC,QAAA,eAChBzC,OAAA,CAACnB,KAAK;cACJ6G,SAAS,EAAE,CAAE;cACb5C,EAAE,EAAE;gBACF6C,CAAC,EAAE;kBAAEf,EAAE,EAAE,CAAC;kBAAEE,EAAE,EAAE;gBAAE,CAAC;gBACnB1B,UAAU,EAAE,mDAAmD;gBAC/DQ,YAAY,EAAE,CAAC;gBACfL,QAAQ,EAAE,UAAU;gBACpB2B,QAAQ,EAAE,QAAQ;gBAClBW,UAAU,EAAE,sBAAsB;gBAClC,SAAS,EAAE;kBACTR,SAAS,EAAE;gBACb;cACF,CAAE;cAAA5C,QAAA,gBAEFzC,OAAA,CAACpB,GAAG;gBACFkE,EAAE,EAAE;kBACFS,QAAQ,EAAE,UAAU;kBACpBG,GAAG,EAAE,CAAC;kBACNoC,KAAK,EAAE,CAAC;kBACRtC,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,OAAO;kBACfL,UAAU,EAAE,sEAAsE;kBAClFQ,YAAY,EAAE,YAAY;kBAC1B0B,MAAM,EAAE;gBACV;cAAE;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF7C,OAAA,CAACpB,GAAG;gBACFkE,EAAE,EAAE;kBACFS,QAAQ,EAAE,UAAU;kBACpBwC,MAAM,EAAE,CAAC;kBACTpC,IAAI,EAAE,CAAC;kBACPH,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,OAAO;kBACfL,UAAU,EAAE,sEAAsE;kBAClFQ,YAAY,EAAE,YAAY;kBAC1B0B,MAAM,EAAE;gBACV;cAAE;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF7C,OAAA,CAACpB,GAAG;gBAACkE,EAAE,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAE+B,MAAM,EAAE;gBAAE,CAAE;gBAAA7C,QAAA,gBAC3CzC,OAAA,CAACpB,GAAG;kBAACkE,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAE8B,EAAE,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,gBACxDzC,OAAA,CAACpB,GAAG;oBACFkE,EAAE,EAAE;sBACFmD,EAAE,EAAE,CAAC;sBACLN,CAAC,EAAE,CAAC;sBACJ/B,YAAY,EAAE,MAAM;sBACpBR,UAAU,EAAE,kFAAkF;sBAC9FL,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE;oBAClB,CAAE;oBAAAT,QAAA,eAEFzC,OAAA,CAAClB,UAAU;sBAACuF,OAAO,EAAC,IAAI;sBAAC8B,SAAS,EAAC,MAAM;sBAAA1D,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACN7C,OAAA,CAAClB,UAAU;oBACTuF,OAAO,EAAC,IAAI;oBACZvB,EAAE,EAAE;sBACFyB,UAAU,EAAE,GAAG;sBACfkC,KAAK,EAAExG,KAAK,CAACoD,OAAO,CAACU,OAAO,CAACoD,IAAI;sBACjCf,aAAa,EAAE;oBACjB,CAAE;oBAAA3D,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN7C,OAAA,CAAClB,UAAU;kBACTuF,OAAO,EAAC,OAAO;kBACfvB,EAAE,EAAE;oBACFiC,EAAE,EAAE,CAAC;oBACL0B,KAAK,EAAE,gBAAgB;oBACvB9B,QAAQ,EAAE;kBACZ,CAAE;kBAAAlC,QAAA,GACH,6HAEC,EAAC/B,UAAU,CAAC0G,aAAa,IAAI,2FAA2F;gBAAA;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9G,CAAC,eACb7C,OAAA,CAACV,gBAAgB;kBAAC+H,cAAc,EAAE3G,UAAU,CAACuG;gBAAgB;kBAAAvE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACV,EAGAzB,iBAAiB,IAAIF,eAAe,iBACnClB,OAAA,CAACL,OAAO;UAACuH,KAAK,EAAE,GAAI;UAAAzE,QAAA,eAClBzC,OAAA,CAACjB,IAAI;YAAC0G,IAAI;YAACb,EAAE,EAAE,EAAG;YAAAnC,QAAA,eAChBzC,OAAA,CAAChB,KAAK;cACJsI,QAAQ,EAAEpG,eAAe,CAACqG,YAAY,GAAG,EAAE,GAAG,OAAO,GAAGrG,eAAe,CAACqG,YAAY,GAAG,EAAE,GAAG,SAAS,GAAG,MAAO;cAC/GlD,OAAO,EAAC,QAAQ;cAChBvB,EAAE,EAAE;gBACFiC,EAAE,EAAE,CAAC;gBACLnB,YAAY,EAAE,CAAC;gBACfyB,SAAS,EAAE,gCAAgC;gBAC3C,kBAAkB,EAAE;kBAAEgB,QAAQ,EAAE;gBAAS,CAAC;gBAC1CV,CAAC,EAAE,CAAC;gBACJ1B,SAAS,EAAE,mBAAmB;gBAC9B,kBAAkB,EAAE;kBAClB,IAAI,EAAE;oBAAEoB,SAAS,EAAE;kBAAiC,CAAC;kBACrD,KAAK,EAAE;oBAAEA,SAAS,EAAE;kBAAiC,CAAC;kBACtD,MAAM,EAAE;oBAAEA,SAAS,EAAE;kBAAiC;gBACxD;cACF,CAAE;cAAA5C,QAAA,eAEFzC,OAAA,CAACpB,GAAG;gBAACkE,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAa,CAAE;gBAAAR,QAAA,eACrDzC,OAAA,CAACpB,GAAG;kBAACkE,EAAE,EAAE;oBAAE0E,QAAQ,EAAE;kBAAE,CAAE;kBAAA/E,QAAA,gBACvBzC,OAAA,CAAClB,UAAU;oBAACuF,OAAO,EAAC,IAAI;oBAACvB,EAAE,EAAE;sBAAEyB,UAAU,EAAE,MAAM;sBAAEQ,EAAE,EAAE;oBAAI,CAAE;oBAAAtC,QAAA,EAC1DvB,eAAe,CAACqG,YAAY,GAAG,EAAE,GAC9B,8CAA8C,GAC9CrG,eAAe,CAACqG,YAAY,GAAG,EAAE,GAC/B,+CAA+C,GAC/C;kBAAkC;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACb7C,OAAA,CAAClB,UAAU;oBAACuF,OAAO,EAAC,OAAO;oBAACvB,EAAE,EAAE;sBAAEyB,UAAU,EAAE;oBAAI,CAAE;oBAAA9B,QAAA,GAAC,YACzC,eAAAzC,OAAA;sBAAAyC,QAAA,EAASvB,eAAe,CAACuG,QAAQ,IAAI;oBAAe;sBAAA/E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eACb7C,OAAA,CAAClB,UAAU;oBAACuF,OAAO,EAAC,OAAO;oBAAA5B,QAAA,GAAC,qBACP,eAAAzC,OAAA;sBAAAyC,QAAA,EAASvB,eAAe,CAACqG,YAAY,CAACG,OAAO,CAAC,CAAC;oBAAC;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,qBAAiB,eAAA7C,OAAA;sBAAAyC,QAAA,EAASvB,eAAe,CAACyG;oBAAW;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,oBAC9H,eAAA7C,OAAA;sBAAAyC,QAAA,EAASvB,eAAe,CAAC0G;oBAAS;sBAAAlF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,KAC5D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EACZ3B,eAAe,CAACqG,YAAY,GAAG,EAAE,iBAChCvH,OAAA,CAAClB,UAAU;oBAACuF,OAAO,EAAC,OAAO;oBAACvB,EAAE,EAAE;sBAAEwB,EAAE,EAAE,CAAC;sBAAEuD,SAAS,EAAE;oBAAS,CAAE;oBAAApF,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACV,EAGAnC,UAAU,IAAIM,oBAAoB,iBACjChB,OAAA,CAACN,OAAO;UAACwH,KAAK,EAAE,GAAI;UAAAzE,QAAA,eAClBzC,OAAA,CAACjB,IAAI;YAAC0G,IAAI;YAACb,EAAE,EAAE,EAAG;YAAAnC,QAAA,eAChBzC,OAAA,CAACnB,KAAK;cACJ6G,SAAS,EAAE,CAAE;cACb5C,EAAE,EAAE;gBACFc,YAAY,EAAE,CAAC;gBACfsB,QAAQ,EAAE,QAAQ;gBAClBW,UAAU,EAAE,sBAAsB;gBAClC,SAAS,EAAE;kBACTR,SAAS,EAAE;gBACb;cACF,CAAE;cAAA5C,QAAA,eAEFzC,OAAA,CAACT,qBAAqB;gBACpB4C,QAAQ,EAAEzB,UAAU,CAACoH,UAAW;gBAChCC,mBAAmB,EAAExF;cAAwB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACV,eAED7C,OAAA,CAACN,OAAO;UAACwH,KAAK,EAAE,GAAI;UAAAzE,QAAA,eAClBzC,OAAA,CAACjB,IAAI;YAAC0G,IAAI;YAACb,EAAE,EAAE,EAAG;YAAAnC,QAAA,eAChBzC,OAAA,CAACnB,KAAK;cACJ6G,SAAS,EAAE,CAAE;cACb5C,EAAE,EAAE;gBACF6C,CAAC,EAAE;kBAAEf,EAAE,EAAE,CAAC;kBAAEC,EAAE,EAAE,CAAC;kBAAEC,EAAE,EAAE;gBAAE,CAAC;gBAC1B1B,UAAU,EAAE,mDAAmD;gBAC/DQ,YAAY,EAAE,CAAC;gBACfL,QAAQ,EAAE,UAAU;gBACpB2B,QAAQ,EAAE,QAAQ;gBAClBW,UAAU,EAAE,sBAAsB;gBAClC,SAAS,EAAE;kBACTR,SAAS,EAAE;gBACb;cACF,CAAE;cAAA5C,QAAA,gBAEFzC,OAAA,CAACpB,GAAG;gBACFkE,EAAE,EAAE;kBACFS,QAAQ,EAAE,UAAU;kBACpBG,GAAG,EAAE,CAAC;kBACNoC,KAAK,EAAE,CAAC;kBACRtC,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,OAAO;kBACfL,UAAU,EAAE,sEAAsE;kBAClFQ,YAAY,EAAE,YAAY;kBAC1B0B,MAAM,EAAE;gBACV;cAAE;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF7C,OAAA,CAACpB,GAAG;gBAACkE,EAAE,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAE+B,MAAM,EAAE;gBAAE,CAAE;gBAAA7C,QAAA,gBAC3CzC,OAAA,CAACpB,GAAG;kBAACkE,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAE8B,EAAE,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,gBACxDzC,OAAA,CAACpB,GAAG;oBACFkE,EAAE,EAAE;sBACFmD,EAAE,EAAE,CAAC;sBACLN,CAAC,EAAE,CAAC;sBACJ/B,YAAY,EAAE,MAAM;sBACpBR,UAAU,EAAE,mFAAmF;sBAC/FL,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE;oBAClB,CAAE;oBAAAT,QAAA,eAEFzC,OAAA,CAAClB,UAAU;sBAACuF,OAAO,EAAC,IAAI;sBAAC8B,SAAS,EAAC,MAAM;sBAAA1D,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACN7C,OAAA,CAAClB,UAAU;oBACTuF,OAAO,EAAC,IAAI;oBACZvB,EAAE,EAAE;sBACFyB,UAAU,EAAE,GAAG;sBACfkC,KAAK,EAAExG,KAAK,CAACoD,OAAO,CAACU,OAAO,CAACoD,IAAI;sBACjCf,aAAa,EAAE;oBACjB,CAAE;oBAAA3D,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN7C,OAAA,CAAClB,UAAU;kBACTuF,OAAO,EAAC,OAAO;kBACfmC,SAAS;kBACT1D,EAAE,EAAE;oBACFiC,EAAE,EAAE,CAAC;oBACL0B,KAAK,EAAE,gBAAgB;oBACvB9B,QAAQ,EAAE;kBACZ,CAAE;kBAAAlC,QAAA,EACH;gBAID;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7C,OAAA,CAACpB,GAAG;kBAACkE,EAAE,EAAE;oBACPc,YAAY,EAAE,CAAC;oBACfsB,QAAQ,EAAE,QAAQ;oBAClBG,SAAS,EAAE,gCAAgC;oBAC3C5B,MAAM,EAAE;sBAAEmB,EAAE,EAAE,OAAO;sBAAEC,EAAE,EAAE,OAAO;sBAAEC,EAAE,EAAE;oBAAQ,CAAC;oBACjDR,EAAE,EAAE;sBAAEM,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE;oBAAE;kBACrB,CAAE;kBAAApC,QAAA,eACAzC,OAAA,CAACb,QAAQ;oBAACiB,OAAO,EAAEA;kBAAQ;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGV7C,OAAA,CAACN,OAAO;UAACwH,KAAK,EAAE,GAAI;UAAAzE,QAAA,eAClBzC,OAAA,CAACjB,IAAI;YAAC0G,IAAI;YAACb,EAAE,EAAE,EAAG;YAAAnC,QAAA,eAChBzC,OAAA,CAACnB,KAAK;cACJ6G,SAAS,EAAE,CAAE;cACb5C,EAAE,EAAE;gBACF6C,CAAC,EAAE;kBAAEf,EAAE,EAAE,CAAC;kBAAEC,EAAE,EAAE,CAAC;kBAAEC,EAAE,EAAE;gBAAE,CAAC;gBAC1B1B,UAAU,EAAE,mDAAmD;gBAC/DQ,YAAY,EAAE,CAAC;gBACfL,QAAQ,EAAE,UAAU;gBACpB2B,QAAQ,EAAE,QAAQ;gBAClBW,UAAU,EAAE,sBAAsB;gBAClC,SAAS,EAAE;kBACTR,SAAS,EAAE;gBACb;cACF,CAAE;cAAA5C,QAAA,gBAEFzC,OAAA,CAACpB,GAAG;gBACFkE,EAAE,EAAE;kBACFS,QAAQ,EAAE,UAAU;kBACpBG,GAAG,EAAE,CAAC;kBACNoC,KAAK,EAAE,CAAC;kBACRtC,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,OAAO;kBACfL,UAAU,EAAE,qEAAqE;kBACjFQ,YAAY,EAAE,YAAY;kBAC1B0B,MAAM,EAAE;gBACV;cAAE;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF7C,OAAA,CAACpB,GAAG;gBAACkE,EAAE,EAAE;kBAAES,QAAQ,EAAE,UAAU;kBAAE+B,MAAM,EAAE;gBAAE,CAAE;gBAAA7C,QAAA,gBAC3CzC,OAAA,CAACpB,GAAG;kBAACkE,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAE8B,EAAE,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,gBACxDzC,OAAA,CAACpB,GAAG;oBACFkE,EAAE,EAAE;sBACFmD,EAAE,EAAE,CAAC;sBACLN,CAAC,EAAE,CAAC;sBACJ/B,YAAY,EAAE,MAAM;sBACpBR,UAAU,EAAE,kFAAkF;sBAC9FL,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE;oBAClB,CAAE;oBAAAT,QAAA,eAEFzC,OAAA,CAAClB,UAAU;sBAACuF,OAAO,EAAC,IAAI;sBAAC8B,SAAS,EAAC,MAAM;sBAAA1D,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACN7C,OAAA,CAAClB,UAAU;oBACTuF,OAAO,EAAC,IAAI;oBACZvB,EAAE,EAAE;sBACFyB,UAAU,EAAE,GAAG;sBACfkC,KAAK,EAAExG,KAAK,CAACoD,OAAO,CAACU,OAAO,CAACoD,IAAI;sBACjCf,aAAa,EAAE;oBACjB,CAAE;oBAAA3D,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN7C,OAAA,CAAClB,UAAU;kBACTuF,OAAO,EAAC,OAAO;kBACfmC,SAAS;kBACT1D,EAAE,EAAE;oBACFiC,EAAE,EAAE,CAAC;oBACL0B,KAAK,EAAE,gBAAgB;oBACvB9B,QAAQ,EAAE;kBACZ,CAAE;kBAAAlC,QAAA,EACH;gBAGD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7C,OAAA,CAACP,gBAAgB;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACZ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAEpB;AAAC1C,EAAA,CAp2BQD,GAAG;AAAA8H,EAAA,GAAH9H,GAAG;AAs2BZ,eAAeA,GAAG;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}