{"ast": null, "code": "export { default } from './useIsFocusVisible';\nexport * from './useIsFocusVisible';", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@mui/utils/esm/useIsFocusVisible/index.js"], "sourcesContent": ["export { default } from './useIsFocusVisible';\nexport * from './useIsFocusVisible';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB;AAC7C,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}