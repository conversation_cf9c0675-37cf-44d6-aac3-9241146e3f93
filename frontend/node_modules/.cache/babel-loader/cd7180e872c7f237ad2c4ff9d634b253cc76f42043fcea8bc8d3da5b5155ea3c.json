{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTabUtilityClass(slot) {\n  return generateUtilityClass('MuiTab', slot);\n}\nconst tabClasses = generateUtilityClasses('MuiTab', ['root', 'labelIcon', 'textColorInherit', 'textColorPrimary', 'textColorSecondary', 'selected', 'disabled', 'fullWidth', 'wrapped', 'iconWrapper']);\nexport default tabClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTabUtilityClass", "slot", "tabClasses"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@mui/material/Tab/tabClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTabUtilityClass(slot) {\n  return generateUtilityClass('MuiTab', slot);\n}\nconst tabClasses = generateUtilityClasses('MuiTab', ['root', 'labelIcon', 'textColorInherit', 'textColorPrimary', 'textColorSecondary', 'selected', 'disabled', 'fullWidth', 'wrapped', 'iconWrapper']);\nexport default tabClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,kBAAkBA,CAACC,IAAI,EAAE;EACvC,OAAOF,oBAAoB,CAAC,QAAQ,EAAEE,IAAI,CAAC;AAC7C;AACA,MAAMC,UAAU,GAAGJ,sBAAsB,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;AACvM,eAAeI,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}