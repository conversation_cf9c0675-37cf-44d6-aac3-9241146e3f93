{"ast": null, "code": "'use strict';\n\nvar iframeUtils = require('./utils/iframe');\nfunction FacadeJS(transport) {\n  this._transport = transport;\n  transport.on('message', this._transportMessage.bind(this));\n  transport.on('close', this._transportClose.bind(this));\n}\nFacadeJS.prototype._transportClose = function (code, reason) {\n  iframeUtils.postMessage('c', JSON.stringify([code, reason]));\n};\nFacadeJS.prototype._transportMessage = function (frame) {\n  iframeUtils.postMessage('t', frame);\n};\nFacadeJS.prototype._send = function (data) {\n  this._transport.send(data);\n};\nFacadeJS.prototype._close = function () {\n  this._transport.close();\n  this._transport.removeAllListeners();\n};\nmodule.exports = FacadeJS;", "map": {"version": 3, "names": ["iframe<PERSON><PERSON>s", "require", "FacadeJS", "transport", "_transport", "on", "_transportMessage", "bind", "_transportClose", "prototype", "code", "reason", "postMessage", "JSON", "stringify", "frame", "_send", "data", "send", "_close", "close", "removeAllListeners", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/facade.js"], "sourcesContent": ["'use strict';\n\nvar iframeUtils = require('./utils/iframe')\n  ;\n\nfunction FacadeJS(transport) {\n  this._transport = transport;\n  transport.on('message', this._transportMessage.bind(this));\n  transport.on('close', this._transportClose.bind(this));\n}\n\nFacadeJS.prototype._transportClose = function(code, reason) {\n  iframeUtils.postMessage('c', JSON.stringify([code, reason]));\n};\nFacadeJS.prototype._transportMessage = function(frame) {\n  iframeUtils.postMessage('t', frame);\n};\nFacadeJS.prototype._send = function(data) {\n  this._transport.send(data);\n};\nFacadeJS.prototype._close = function() {\n  this._transport.close();\n  this._transport.removeAllListeners();\n};\n\nmodule.exports = FacadeJS;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAG3C,SAASC,QAAQA,CAACC,SAAS,EAAE;EAC3B,IAAI,CAACC,UAAU,GAAGD,SAAS;EAC3BA,SAAS,CAACE,EAAE,CAAC,SAAS,EAAE,IAAI,CAACC,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC1DJ,SAAS,CAACE,EAAE,CAAC,OAAO,EAAE,IAAI,CAACG,eAAe,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC;AACxD;AAEAL,QAAQ,CAACO,SAAS,CAACD,eAAe,GAAG,UAASE,IAAI,EAAEC,MAAM,EAAE;EAC1DX,WAAW,CAACY,WAAW,CAAC,GAAG,EAAEC,IAAI,CAACC,SAAS,CAAC,CAACJ,IAAI,EAAEC,MAAM,CAAC,CAAC,CAAC;AAC9D,CAAC;AACDT,QAAQ,CAACO,SAAS,CAACH,iBAAiB,GAAG,UAASS,KAAK,EAAE;EACrDf,WAAW,CAACY,WAAW,CAAC,GAAG,EAAEG,KAAK,CAAC;AACrC,CAAC;AACDb,QAAQ,CAACO,SAAS,CAACO,KAAK,GAAG,UAASC,IAAI,EAAE;EACxC,IAAI,CAACb,UAAU,CAACc,IAAI,CAACD,IAAI,CAAC;AAC5B,CAAC;AACDf,QAAQ,CAACO,SAAS,CAACU,MAAM,GAAG,YAAW;EACrC,IAAI,CAACf,UAAU,CAACgB,KAAK,CAAC,CAAC;EACvB,IAAI,CAAChB,UAAU,CAACiB,kBAAkB,CAAC,CAAC;AACtC,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGrB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}