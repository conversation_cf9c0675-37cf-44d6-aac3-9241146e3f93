{"ast": null, "code": "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "map": {"version": 3, "names": ["popperGenerator", "detectOverflow", "eventListeners", "popperOffsets", "computeStyles", "applyStyles", "offset", "flip", "preventOverflow", "arrow", "hide", "defaultModifiers", "createPopper", "createPopperLite"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@popperjs/core/lib/popper.js"], "sourcesContent": ["import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";"], "mappings": "AAAA,SAASA,eAAe,EAAEC,cAAc,QAAQ,mBAAmB;AACnE,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,IAAI,MAAM,qBAAqB;AACtC,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,IAAI,MAAM,qBAAqB;AACtC,IAAIC,gBAAgB,GAAG,CAACT,cAAc,EAAEC,aAAa,EAAEC,aAAa,EAAEC,WAAW,EAAEC,MAAM,EAAEC,IAAI,EAAEC,eAAe,EAAEC,KAAK,EAAEC,IAAI,CAAC;AAC9H,IAAIE,YAAY,GAAG,aAAaZ,eAAe,CAAC;EAC9CW,gBAAgB,EAAEA;AACpB,CAAC,CAAC,CAAC,CAAC;;AAEJ,SAASC,YAAY,EAAEZ,eAAe,EAAEW,gBAAgB,EAAEV,cAAc,GAAG,CAAC;;AAE5E,SAASW,YAAY,IAAIC,gBAAgB,QAAQ,kBAAkB,CAAC,CAAC;;AAErE,cAAc,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}