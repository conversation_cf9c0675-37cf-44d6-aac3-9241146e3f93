{"ast": null, "code": "import { augmentWebsocket } from './augment-websocket.js';\nimport { BYTE } from './byte.js';\nimport { FrameImpl } from './frame-impl.js';\nimport { Parser } from './parser.js';\nimport { Ticker } from './ticker.js';\nimport { StompSocketState } from './types.js';\nimport { Versions } from './versions.js';\n/**\n * The STOMP protocol handler\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class StompHandler {\n  get connectedVersion() {\n    return this._connectedVersion;\n  }\n  get connected() {\n    return this._connected;\n  }\n  constructor(_client, _webSocket, config) {\n    this._client = _client;\n    this._webSocket = _webSocket;\n    this._connected = false;\n    this._serverFrameHandlers = {\n      // [CONNECTED Frame](https://stomp.github.com/stomp-specification-1.2.html#CONNECTED_Frame)\n      CONNECTED: frame => {\n        this.debug(`connected to server ${frame.headers.server}`);\n        this._connected = true;\n        this._connectedVersion = frame.headers.version;\n        // STOMP version 1.2 needs header values to be escaped\n        if (this._connectedVersion === Versions.V1_2) {\n          this._escapeHeaderValues = true;\n        }\n        this._setupHeartbeat(frame.headers);\n        this.onConnect(frame);\n      },\n      // [MESSAGE Frame](https://stomp.github.com/stomp-specification-1.2.html#MESSAGE)\n      MESSAGE: frame => {\n        // the callback is registered when the client calls\n        // `subscribe()`.\n        // If there is no registered subscription for the received message,\n        // the default `onUnhandledMessage` callback is used that the client can set.\n        // This is useful for subscriptions that are automatically created\n        // on the browser side (e.g. [RabbitMQ's temporary\n        // queues](https://www.rabbitmq.com/stomp.html)).\n        const subscription = frame.headers.subscription;\n        const onReceive = this._subscriptions[subscription] || this.onUnhandledMessage;\n        // bless the frame to be a Message\n        const message = frame;\n        const client = this;\n        const messageId = this._connectedVersion === Versions.V1_2 ? message.headers.ack : message.headers['message-id'];\n        // add `ack()` and `nack()` methods directly to the returned frame\n        // so that a simple call to `message.ack()` can acknowledge the message.\n        message.ack = (headers = {}) => {\n          return client.ack(messageId, subscription, headers);\n        };\n        message.nack = (headers = {}) => {\n          return client.nack(messageId, subscription, headers);\n        };\n        onReceive(message);\n      },\n      // [RECEIPT Frame](https://stomp.github.com/stomp-specification-1.2.html#RECEIPT)\n      RECEIPT: frame => {\n        const callback = this._receiptWatchers[frame.headers['receipt-id']];\n        if (callback) {\n          callback(frame);\n          // Server will acknowledge only once, remove the callback\n          delete this._receiptWatchers[frame.headers['receipt-id']];\n        } else {\n          this.onUnhandledReceipt(frame);\n        }\n      },\n      // [ERROR Frame](https://stomp.github.com/stomp-specification-1.2.html#ERROR)\n      ERROR: frame => {\n        this.onStompError(frame);\n      }\n    };\n    // used to index subscribers\n    this._counter = 0;\n    // subscription callbacks indexed by subscriber's ID\n    this._subscriptions = {};\n    // receipt-watchers indexed by receipts-ids\n    this._receiptWatchers = {};\n    this._partialData = '';\n    this._escapeHeaderValues = false;\n    this._lastServerActivityTS = Date.now();\n    this.debug = config.debug;\n    this.stompVersions = config.stompVersions;\n    this.connectHeaders = config.connectHeaders;\n    this.disconnectHeaders = config.disconnectHeaders;\n    this.heartbeatIncoming = config.heartbeatIncoming;\n    this.heartbeatOutgoing = config.heartbeatOutgoing;\n    this.splitLargeFrames = config.splitLargeFrames;\n    this.maxWebSocketChunkSize = config.maxWebSocketChunkSize;\n    this.forceBinaryWSFrames = config.forceBinaryWSFrames;\n    this.logRawCommunication = config.logRawCommunication;\n    this.appendMissingNULLonIncoming = config.appendMissingNULLonIncoming;\n    this.discardWebsocketOnCommFailure = config.discardWebsocketOnCommFailure;\n    this.onConnect = config.onConnect;\n    this.onDisconnect = config.onDisconnect;\n    this.onStompError = config.onStompError;\n    this.onWebSocketClose = config.onWebSocketClose;\n    this.onWebSocketError = config.onWebSocketError;\n    this.onUnhandledMessage = config.onUnhandledMessage;\n    this.onUnhandledReceipt = config.onUnhandledReceipt;\n    this.onUnhandledFrame = config.onUnhandledFrame;\n  }\n  start() {\n    const parser = new Parser(\n    // On Frame\n    rawFrame => {\n      const frame = FrameImpl.fromRawFrame(rawFrame, this._escapeHeaderValues);\n      // if this.logRawCommunication is set, the rawChunk is logged at this._webSocket.onmessage\n      if (!this.logRawCommunication) {\n        this.debug(`<<< ${frame}`);\n      }\n      const serverFrameHandler = this._serverFrameHandlers[frame.command] || this.onUnhandledFrame;\n      serverFrameHandler(frame);\n    },\n    // On Incoming Ping\n    () => {\n      this.debug('<<< PONG');\n    });\n    this._webSocket.onmessage = evt => {\n      this.debug('Received data');\n      this._lastServerActivityTS = Date.now();\n      if (this.logRawCommunication) {\n        const rawChunkAsString = evt.data instanceof ArrayBuffer ? new TextDecoder().decode(evt.data) : evt.data;\n        this.debug(`<<< ${rawChunkAsString}`);\n      }\n      parser.parseChunk(evt.data, this.appendMissingNULLonIncoming);\n    };\n    this._webSocket.onclose = closeEvent => {\n      this.debug(`Connection closed to ${this._webSocket.url}`);\n      this._cleanUp();\n      this.onWebSocketClose(closeEvent);\n    };\n    this._webSocket.onerror = errorEvent => {\n      this.onWebSocketError(errorEvent);\n    };\n    this._webSocket.onopen = () => {\n      // Clone before updating\n      const connectHeaders = Object.assign({}, this.connectHeaders);\n      this.debug('Web Socket Opened...');\n      connectHeaders['accept-version'] = this.stompVersions.supportedVersions();\n      connectHeaders['heart-beat'] = [this.heartbeatOutgoing, this.heartbeatIncoming].join(',');\n      this._transmit({\n        command: 'CONNECT',\n        headers: connectHeaders\n      });\n    };\n  }\n  _setupHeartbeat(headers) {\n    if (headers.version !== Versions.V1_1 && headers.version !== Versions.V1_2) {\n      return;\n    }\n    // It is valid for the server to not send this header\n    // https://stomp.github.io/stomp-specification-1.2.html#Heart-beating\n    if (!headers['heart-beat']) {\n      return;\n    }\n    // heart-beat header received from the server looks like:\n    //\n    //     heart-beat: sx, sy\n    const [serverOutgoing, serverIncoming] = headers['heart-beat'].split(',').map(v => parseInt(v, 10));\n    if (this.heartbeatOutgoing !== 0 && serverIncoming !== 0) {\n      const ttl = Math.max(this.heartbeatOutgoing, serverIncoming);\n      this.debug(`send PING every ${ttl}ms`);\n      this._pinger = new Ticker(ttl, this._client.heartbeatStrategy, this.debug);\n      this._pinger.start(() => {\n        if (this._webSocket.readyState === StompSocketState.OPEN) {\n          this._webSocket.send(BYTE.LF);\n          this.debug('>>> PING');\n        }\n      });\n    }\n    if (this.heartbeatIncoming !== 0 && serverOutgoing !== 0) {\n      const ttl = Math.max(this.heartbeatIncoming, serverOutgoing);\n      this.debug(`check PONG every ${ttl}ms`);\n      this._ponger = setInterval(() => {\n        const delta = Date.now() - this._lastServerActivityTS;\n        // We wait twice the TTL to be flexible on window's setInterval calls\n        if (delta > ttl * 2) {\n          this.debug(`did not receive server activity for the last ${delta}ms`);\n          this._closeOrDiscardWebsocket();\n        }\n      }, ttl);\n    }\n  }\n  _closeOrDiscardWebsocket() {\n    if (this.discardWebsocketOnCommFailure) {\n      this.debug('Discarding websocket, the underlying socket may linger for a while');\n      this.discardWebsocket();\n    } else {\n      this.debug('Issuing close on the websocket');\n      this._closeWebsocket();\n    }\n  }\n  forceDisconnect() {\n    if (this._webSocket) {\n      if (this._webSocket.readyState === StompSocketState.CONNECTING || this._webSocket.readyState === StompSocketState.OPEN) {\n        this._closeOrDiscardWebsocket();\n      }\n    }\n  }\n  _closeWebsocket() {\n    this._webSocket.onmessage = () => {}; // ignore messages\n    this._webSocket.close();\n  }\n  discardWebsocket() {\n    if (typeof this._webSocket.terminate !== 'function') {\n      augmentWebsocket(this._webSocket, msg => this.debug(msg));\n    }\n    // @ts-ignore - this method will be there at this stage\n    this._webSocket.terminate();\n  }\n  _transmit(params) {\n    const {\n      command,\n      headers,\n      body,\n      binaryBody,\n      skipContentLengthHeader\n    } = params;\n    const frame = new FrameImpl({\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues: this._escapeHeaderValues,\n      skipContentLengthHeader\n    });\n    let rawChunk = frame.serialize();\n    if (this.logRawCommunication) {\n      this.debug(`>>> ${rawChunk}`);\n    } else {\n      this.debug(`>>> ${frame}`);\n    }\n    if (this.forceBinaryWSFrames && typeof rawChunk === 'string') {\n      rawChunk = new TextEncoder().encode(rawChunk);\n    }\n    if (typeof rawChunk !== 'string' || !this.splitLargeFrames) {\n      this._webSocket.send(rawChunk);\n    } else {\n      let out = rawChunk;\n      while (out.length > 0) {\n        const chunk = out.substring(0, this.maxWebSocketChunkSize);\n        out = out.substring(this.maxWebSocketChunkSize);\n        this._webSocket.send(chunk);\n        this.debug(`chunk sent = ${chunk.length}, remaining = ${out.length}`);\n      }\n    }\n  }\n  dispose() {\n    if (this.connected) {\n      try {\n        // clone before updating\n        const disconnectHeaders = Object.assign({}, this.disconnectHeaders);\n        if (!disconnectHeaders.receipt) {\n          disconnectHeaders.receipt = `close-${this._counter++}`;\n        }\n        this.watchForReceipt(disconnectHeaders.receipt, frame => {\n          this._closeWebsocket();\n          this._cleanUp();\n          this.onDisconnect(frame);\n        });\n        this._transmit({\n          command: 'DISCONNECT',\n          headers: disconnectHeaders\n        });\n      } catch (error) {\n        this.debug(`Ignoring error during disconnect ${error}`);\n      }\n    } else {\n      if (this._webSocket.readyState === StompSocketState.CONNECTING || this._webSocket.readyState === StompSocketState.OPEN) {\n        this._closeWebsocket();\n      }\n    }\n  }\n  _cleanUp() {\n    this._connected = false;\n    if (this._pinger) {\n      this._pinger.stop();\n      this._pinger = undefined;\n    }\n    if (this._ponger) {\n      clearInterval(this._ponger);\n      this._ponger = undefined;\n    }\n  }\n  publish(params) {\n    const {\n      destination,\n      headers,\n      body,\n      binaryBody,\n      skipContentLengthHeader\n    } = params;\n    const hdrs = Object.assign({\n      destination\n    }, headers);\n    this._transmit({\n      command: 'SEND',\n      headers: hdrs,\n      body,\n      binaryBody,\n      skipContentLengthHeader\n    });\n  }\n  watchForReceipt(receiptId, callback) {\n    this._receiptWatchers[receiptId] = callback;\n  }\n  subscribe(destination, callback, headers = {}) {\n    headers = Object.assign({}, headers);\n    if (!headers.id) {\n      headers.id = `sub-${this._counter++}`;\n    }\n    headers.destination = destination;\n    this._subscriptions[headers.id] = callback;\n    this._transmit({\n      command: 'SUBSCRIBE',\n      headers\n    });\n    const client = this;\n    return {\n      id: headers.id,\n      unsubscribe(hdrs) {\n        return client.unsubscribe(headers.id, hdrs);\n      }\n    };\n  }\n  unsubscribe(id, headers = {}) {\n    headers = Object.assign({}, headers);\n    delete this._subscriptions[id];\n    headers.id = id;\n    this._transmit({\n      command: 'UNSUBSCRIBE',\n      headers\n    });\n  }\n  begin(transactionId) {\n    const txId = transactionId || `tx-${this._counter++}`;\n    this._transmit({\n      command: 'BEGIN',\n      headers: {\n        transaction: txId\n      }\n    });\n    const client = this;\n    return {\n      id: txId,\n      commit() {\n        client.commit(txId);\n      },\n      abort() {\n        client.abort(txId);\n      }\n    };\n  }\n  commit(transactionId) {\n    this._transmit({\n      command: 'COMMIT',\n      headers: {\n        transaction: transactionId\n      }\n    });\n  }\n  abort(transactionId) {\n    this._transmit({\n      command: 'ABORT',\n      headers: {\n        transaction: transactionId\n      }\n    });\n  }\n  ack(messageId, subscriptionId, headers = {}) {\n    headers = Object.assign({}, headers);\n    if (this._connectedVersion === Versions.V1_2) {\n      headers.id = messageId;\n    } else {\n      headers['message-id'] = messageId;\n    }\n    headers.subscription = subscriptionId;\n    this._transmit({\n      command: 'ACK',\n      headers\n    });\n  }\n  nack(messageId, subscriptionId, headers = {}) {\n    headers = Object.assign({}, headers);\n    if (this._connectedVersion === Versions.V1_2) {\n      headers.id = messageId;\n    } else {\n      headers['message-id'] = messageId;\n    }\n    headers.subscription = subscriptionId;\n    return this._transmit({\n      command: 'NACK',\n      headers\n    });\n  }\n}", "map": {"version": 3, "names": ["augmentWebsocket", "BYTE", "FrameImpl", "<PERSON><PERSON><PERSON>", "Ticker", "StompSocketState", "Versions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectedVersion", "_connectedVersion", "connected", "_connected", "constructor", "_client", "_webSocket", "config", "_serverFrameHandlers", "CONNECTED", "frame", "debug", "headers", "server", "version", "V1_2", "_escapeHeaderV<PERSON>ues", "_setupHeartbeat", "onConnect", "MESSAGE", "subscription", "onReceive", "_subscriptions", "onUnhandledMessage", "message", "client", "messageId", "ack", "nack", "RECEIPT", "callback", "_receiptWatchers", "onUnhandledReceipt", "ERROR", "onStompError", "_counter", "_partialData", "_lastServerActivityTS", "Date", "now", "stompV<PERSON><PERSON>", "connectHeaders", "disconnectHeaders", "heartbeatIncoming", "heartbeatOutgoing", "splitLargeFrames", "maxWebSocketChunkSize", "forceBinaryWSFrames", "logRawCommunication", "appendMissingNULLonIncoming", "discardWebsocketOnCommFailure", "onDisconnect", "onWebSocketClose", "onWebSocketError", "onUnhandledFrame", "start", "parser", "rawFrame", "fromRawFrame", "serverFrameHandler", "command", "onmessage", "evt", "rawChunkAsString", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TextDecoder", "decode", "parseChunk", "onclose", "closeEvent", "url", "_cleanUp", "onerror", "errorEvent", "onopen", "Object", "assign", "supportedVersions", "join", "_transmit", "V1_1", "serverOutgoing", "serverIncoming", "split", "map", "v", "parseInt", "ttl", "Math", "max", "_pinger", "heartbeatStrategy", "readyState", "OPEN", "send", "LF", "_ponger", "setInterval", "delta", "_closeOrDiscardWebsocket", "discardWeb<PERSON>cket", "_closeWebsocket", "forceDisconnect", "CONNECTING", "close", "terminate", "msg", "params", "body", "binaryBody", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapeHeader<PERSON><PERSON>ues", "rawChunk", "serialize", "TextEncoder", "encode", "out", "length", "chunk", "substring", "dispose", "receipt", "watchForReceipt", "error", "stop", "undefined", "clearInterval", "publish", "destination", "hdrs", "receiptId", "subscribe", "id", "unsubscribe", "begin", "transactionId", "txId", "transaction", "commit", "abort", "subscriptionId"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@stomp/stompjs/src/stomp-handler.ts"], "sourcesContent": ["import { augmentWebsocket } from './augment-websocket.js';\nimport { BYTE } from './byte.js';\nimport { Client } from './client.js';\nimport { FrameImpl } from './frame-impl.js';\nimport type { IMessage } from './i-message.js';\nimport { ITransaction } from './i-transaction.js';\nimport { Parser } from './parser.js';\nimport { StompHeaders } from './stomp-headers.js';\nimport { StompSubscription } from './stomp-subscription.js';\nimport { Ticker } from './ticker.js';\nimport {\n  closeEventCallbackType,\n  debugFnType,\n  frameCallbackType,\n  IPublishParams,\n  IStompSocket,\n  IStompSocketMessageEvent,\n  IStomptHandlerConfig,\n  messageCallbackType,\n  StompSocketState,\n  wsErrorCallbackType,\n} from './types.js';\nimport { Versions } from './versions.js';\n\n/**\n * The STOMP protocol handler\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nexport class StompHandler {\n  public debug: debugFnType;\n\n  public stompVersions: Versions;\n\n  public connectHeaders: StompHeaders;\n\n  public disconnectHeaders: StompHeaders;\n\n  public heartbeatIncoming: number;\n\n  public heartbeatOutgoing: number;\n\n  public onUnhandledMessage: messageCallbackType;\n\n  public onUnhandledReceipt: frameCallbackType;\n\n  public onUnhandledFrame: frameCallbackType;\n\n  public onConnect: frameCallbackType;\n\n  public onDisconnect: frameCallbackType;\n\n  public onStompError: frameCallbackType;\n\n  public onWebSocketClose: closeEventCallbackType;\n\n  public onWebSocketError: wsErrorCallbackType;\n\n  public logRawCommunication: boolean;\n\n  public splitLargeFrames: boolean;\n\n  public maxWebSocketChunkSize: number;\n\n  public forceBinaryWSFrames: boolean;\n\n  public appendMissingNULLonIncoming: boolean;\n\n  public discardWebsocketOnCommFailure: boolean;\n\n  get connectedVersion(): string | undefined {\n    return this._connectedVersion;\n  }\n  private _connectedVersion: string | undefined;\n\n  get connected(): boolean {\n    return this._connected;\n  }\n\n  private _connected: boolean = false;\n\n  private readonly _subscriptions: { [key: string]: messageCallbackType };\n  private readonly _receiptWatchers: { [key: string]: frameCallbackType };\n  private _partialData: string;\n  private _escapeHeaderValues: boolean;\n  private _counter: number;\n  private _pinger?: Ticker;\n  private _ponger: any;\n  private _lastServerActivityTS: number;\n\n  constructor(\n    private _client: Client,\n    public _webSocket: IStompSocket,\n    config: IStomptHandlerConfig\n  ) {\n    // used to index subscribers\n    this._counter = 0;\n\n    // subscription callbacks indexed by subscriber's ID\n    this._subscriptions = {};\n\n    // receipt-watchers indexed by receipts-ids\n    this._receiptWatchers = {};\n\n    this._partialData = '';\n\n    this._escapeHeaderValues = false;\n\n    this._lastServerActivityTS = Date.now();\n\n    this.debug = config.debug;\n    this.stompVersions = config.stompVersions;\n    this.connectHeaders = config.connectHeaders;\n    this.disconnectHeaders = config.disconnectHeaders;\n    this.heartbeatIncoming = config.heartbeatIncoming;\n    this.heartbeatOutgoing = config.heartbeatOutgoing;\n    this.splitLargeFrames = config.splitLargeFrames;\n    this.maxWebSocketChunkSize = config.maxWebSocketChunkSize;\n    this.forceBinaryWSFrames = config.forceBinaryWSFrames;\n    this.logRawCommunication = config.logRawCommunication;\n    this.appendMissingNULLonIncoming = config.appendMissingNULLonIncoming;\n    this.discardWebsocketOnCommFailure = config.discardWebsocketOnCommFailure;\n    this.onConnect = config.onConnect;\n    this.onDisconnect = config.onDisconnect;\n    this.onStompError = config.onStompError;\n    this.onWebSocketClose = config.onWebSocketClose;\n    this.onWebSocketError = config.onWebSocketError;\n    this.onUnhandledMessage = config.onUnhandledMessage;\n    this.onUnhandledReceipt = config.onUnhandledReceipt;\n    this.onUnhandledFrame = config.onUnhandledFrame;\n  }\n\n  public start(): void {\n    const parser = new Parser(\n      // On Frame\n      rawFrame => {\n        const frame = FrameImpl.fromRawFrame(\n          rawFrame,\n          this._escapeHeaderValues\n        );\n\n        // if this.logRawCommunication is set, the rawChunk is logged at this._webSocket.onmessage\n        if (!this.logRawCommunication) {\n          this.debug(`<<< ${frame}`);\n        }\n\n        const serverFrameHandler =\n          this._serverFrameHandlers[frame.command] || this.onUnhandledFrame;\n        serverFrameHandler(frame);\n      },\n      // On Incoming Ping\n      () => {\n        this.debug('<<< PONG');\n      }\n    );\n\n    this._webSocket.onmessage = (evt: IStompSocketMessageEvent) => {\n      this.debug('Received data');\n      this._lastServerActivityTS = Date.now();\n\n      if (this.logRawCommunication) {\n        const rawChunkAsString =\n          evt.data instanceof ArrayBuffer\n            ? new TextDecoder().decode(evt.data)\n            : evt.data;\n        this.debug(`<<< ${rawChunkAsString}`);\n      }\n\n      parser.parseChunk(\n        evt.data as string | ArrayBuffer,\n        this.appendMissingNULLonIncoming\n      );\n    };\n\n    this._webSocket.onclose = (closeEvent): void => {\n      this.debug(`Connection closed to ${this._webSocket.url}`);\n      this._cleanUp();\n      this.onWebSocketClose(closeEvent);\n    };\n\n    this._webSocket.onerror = (errorEvent): void => {\n      this.onWebSocketError(errorEvent);\n    };\n\n    this._webSocket.onopen = () => {\n      // Clone before updating\n      const connectHeaders = (Object as any).assign({}, this.connectHeaders);\n\n      this.debug('Web Socket Opened...');\n      connectHeaders['accept-version'] = this.stompVersions.supportedVersions();\n      connectHeaders['heart-beat'] = [\n        this.heartbeatOutgoing,\n        this.heartbeatIncoming,\n      ].join(',');\n      this._transmit({ command: 'CONNECT', headers: connectHeaders });\n    };\n  }\n\n  private readonly _serverFrameHandlers: {\n    [key: string]: frameCallbackType;\n  } = {\n    // [CONNECTED Frame](https://stomp.github.com/stomp-specification-1.2.html#CONNECTED_Frame)\n    CONNECTED: frame => {\n      this.debug(`connected to server ${frame.headers.server}`);\n      this._connected = true;\n      this._connectedVersion = frame.headers.version;\n      // STOMP version 1.2 needs header values to be escaped\n      if (this._connectedVersion === Versions.V1_2) {\n        this._escapeHeaderValues = true;\n      }\n\n      this._setupHeartbeat(frame.headers);\n      this.onConnect(frame);\n    },\n\n    // [MESSAGE Frame](https://stomp.github.com/stomp-specification-1.2.html#MESSAGE)\n    MESSAGE: frame => {\n      // the callback is registered when the client calls\n      // `subscribe()`.\n      // If there is no registered subscription for the received message,\n      // the default `onUnhandledMessage` callback is used that the client can set.\n      // This is useful for subscriptions that are automatically created\n      // on the browser side (e.g. [RabbitMQ's temporary\n      // queues](https://www.rabbitmq.com/stomp.html)).\n      const subscription = frame.headers.subscription;\n      const onReceive =\n        this._subscriptions[subscription] || this.onUnhandledMessage;\n\n      // bless the frame to be a Message\n      const message = frame as IMessage;\n\n      const client = this;\n      const messageId =\n        this._connectedVersion === Versions.V1_2\n          ? message.headers.ack\n          : message.headers['message-id'];\n\n      // add `ack()` and `nack()` methods directly to the returned frame\n      // so that a simple call to `message.ack()` can acknowledge the message.\n      message.ack = (headers: StompHeaders = {}): void => {\n        return client.ack(messageId, subscription, headers);\n      };\n      message.nack = (headers: StompHeaders = {}): void => {\n        return client.nack(messageId, subscription, headers);\n      };\n      onReceive(message);\n    },\n\n    // [RECEIPT Frame](https://stomp.github.com/stomp-specification-1.2.html#RECEIPT)\n    RECEIPT: frame => {\n      const callback = this._receiptWatchers[frame.headers['receipt-id']];\n      if (callback) {\n        callback(frame);\n        // Server will acknowledge only once, remove the callback\n        delete this._receiptWatchers[frame.headers['receipt-id']];\n      } else {\n        this.onUnhandledReceipt(frame);\n      }\n    },\n\n    // [ERROR Frame](https://stomp.github.com/stomp-specification-1.2.html#ERROR)\n    ERROR: frame => {\n      this.onStompError(frame);\n    },\n  };\n\n  private _setupHeartbeat(headers: StompHeaders): void {\n    if (\n      headers.version !== Versions.V1_1 &&\n      headers.version !== Versions.V1_2\n    ) {\n      return;\n    }\n\n    // It is valid for the server to not send this header\n    // https://stomp.github.io/stomp-specification-1.2.html#Heart-beating\n    if (!headers['heart-beat']) {\n      return;\n    }\n\n    // heart-beat header received from the server looks like:\n    //\n    //     heart-beat: sx, sy\n    const [serverOutgoing, serverIncoming] = headers['heart-beat']\n      .split(',')\n      .map((v: string) => parseInt(v, 10));\n\n    if (this.heartbeatOutgoing !== 0 && serverIncoming !== 0) {\n      const ttl: number = Math.max(this.heartbeatOutgoing, serverIncoming);\n      this.debug(`send PING every ${ttl}ms`);\n\n      this._pinger = new Ticker(ttl, this._client.heartbeatStrategy, this.debug);\n      this._pinger.start(() => {\n        if (this._webSocket.readyState === StompSocketState.OPEN) {\n          this._webSocket.send(BYTE.LF);\n          this.debug('>>> PING');\n        }\n      });\n    }\n\n    if (this.heartbeatIncoming !== 0 && serverOutgoing !== 0) {\n      const ttl: number = Math.max(this.heartbeatIncoming, serverOutgoing);\n      this.debug(`check PONG every ${ttl}ms`);\n      this._ponger = setInterval(() => {\n        const delta = Date.now() - this._lastServerActivityTS;\n        // We wait twice the TTL to be flexible on window's setInterval calls\n        if (delta > ttl * 2) {\n          this.debug(`did not receive server activity for the last ${delta}ms`);\n          this._closeOrDiscardWebsocket();\n        }\n      }, ttl);\n    }\n  }\n\n  private _closeOrDiscardWebsocket() {\n    if (this.discardWebsocketOnCommFailure) {\n      this.debug(\n        'Discarding websocket, the underlying socket may linger for a while'\n      );\n      this.discardWebsocket();\n    } else {\n      this.debug('Issuing close on the websocket');\n      this._closeWebsocket();\n    }\n  }\n\n  public forceDisconnect() {\n    if (this._webSocket) {\n      if (\n        this._webSocket.readyState === StompSocketState.CONNECTING ||\n        this._webSocket.readyState === StompSocketState.OPEN\n      ) {\n        this._closeOrDiscardWebsocket();\n      }\n    }\n  }\n\n  public _closeWebsocket() {\n    this._webSocket.onmessage = () => {}; // ignore messages\n    this._webSocket.close();\n  }\n\n  public discardWebsocket() {\n    if (typeof this._webSocket.terminate !== 'function') {\n      augmentWebsocket(this._webSocket, (msg: string) => this.debug(msg));\n    }\n\n    // @ts-ignore - this method will be there at this stage\n    this._webSocket.terminate();\n  }\n\n  private _transmit(params: {\n    command: string;\n    headers?: StompHeaders;\n    body?: string;\n    binaryBody?: Uint8Array;\n    skipContentLengthHeader?: boolean;\n  }): void {\n    const { command, headers, body, binaryBody, skipContentLengthHeader } =\n      params;\n    const frame = new FrameImpl({\n      command,\n      headers,\n      body,\n      binaryBody,\n      escapeHeaderValues: this._escapeHeaderValues,\n      skipContentLengthHeader,\n    });\n\n    let rawChunk = frame.serialize();\n\n    if (this.logRawCommunication) {\n      this.debug(`>>> ${rawChunk}`);\n    } else {\n      this.debug(`>>> ${frame}`);\n    }\n\n    if (this.forceBinaryWSFrames && typeof rawChunk === 'string') {\n      rawChunk = new TextEncoder().encode(rawChunk);\n    }\n\n    if (typeof rawChunk !== 'string' || !this.splitLargeFrames) {\n      this._webSocket.send(rawChunk);\n    } else {\n      let out = rawChunk as string;\n      while (out.length > 0) {\n        const chunk = out.substring(0, this.maxWebSocketChunkSize);\n        out = out.substring(this.maxWebSocketChunkSize);\n        this._webSocket.send(chunk);\n        this.debug(`chunk sent = ${chunk.length}, remaining = ${out.length}`);\n      }\n    }\n  }\n\n  public dispose(): void {\n    if (this.connected) {\n      try {\n        // clone before updating\n        const disconnectHeaders = (Object as any).assign(\n          {},\n          this.disconnectHeaders\n        );\n\n        if (!disconnectHeaders.receipt) {\n          disconnectHeaders.receipt = `close-${this._counter++}`;\n        }\n        this.watchForReceipt(disconnectHeaders.receipt, frame => {\n          this._closeWebsocket();\n          this._cleanUp();\n          this.onDisconnect(frame);\n        });\n        this._transmit({ command: 'DISCONNECT', headers: disconnectHeaders });\n      } catch (error) {\n        this.debug(`Ignoring error during disconnect ${error}`);\n      }\n    } else {\n      if (\n        this._webSocket.readyState === StompSocketState.CONNECTING ||\n        this._webSocket.readyState === StompSocketState.OPEN\n      ) {\n        this._closeWebsocket();\n      }\n    }\n  }\n\n  private _cleanUp() {\n    this._connected = false;\n\n    if (this._pinger) {\n      this._pinger.stop();\n      this._pinger = undefined;\n    }\n    if (this._ponger) {\n      clearInterval(this._ponger);\n      this._ponger = undefined;\n    }\n  }\n\n  public publish(params: IPublishParams): void {\n    const { destination, headers, body, binaryBody, skipContentLengthHeader } =\n      params;\n    const hdrs: StompHeaders = (Object as any).assign({ destination }, headers);\n    this._transmit({\n      command: 'SEND',\n      headers: hdrs,\n      body,\n      binaryBody,\n      skipContentLengthHeader,\n    });\n  }\n\n  public watchForReceipt(receiptId: string, callback: frameCallbackType): void {\n    this._receiptWatchers[receiptId] = callback;\n  }\n\n  public subscribe(\n    destination: string,\n    callback: messageCallbackType,\n    headers: StompHeaders = {}\n  ): StompSubscription {\n    headers = (Object as any).assign({}, headers);\n\n    if (!headers.id) {\n      headers.id = `sub-${this._counter++}`;\n    }\n    headers.destination = destination;\n    this._subscriptions[headers.id] = callback;\n    this._transmit({ command: 'SUBSCRIBE', headers });\n    const client = this;\n    return {\n      id: headers.id,\n\n      unsubscribe(hdrs) {\n        return client.unsubscribe(headers.id, hdrs);\n      },\n    };\n  }\n\n  public unsubscribe(id: string, headers: StompHeaders = {}): void {\n    headers = (Object as any).assign({}, headers);\n\n    delete this._subscriptions[id];\n    headers.id = id;\n    this._transmit({ command: 'UNSUBSCRIBE', headers });\n  }\n\n  public begin(transactionId: string): ITransaction {\n    const txId = transactionId || `tx-${this._counter++}`;\n    this._transmit({\n      command: 'BEGIN',\n      headers: {\n        transaction: txId,\n      },\n    });\n    const client = this;\n    return {\n      id: txId,\n      commit(): void {\n        client.commit(txId);\n      },\n      abort(): void {\n        client.abort(txId);\n      },\n    };\n  }\n\n  public commit(transactionId: string): void {\n    this._transmit({\n      command: 'COMMIT',\n      headers: {\n        transaction: transactionId,\n      },\n    });\n  }\n\n  public abort(transactionId: string): void {\n    this._transmit({\n      command: 'ABORT',\n      headers: {\n        transaction: transactionId,\n      },\n    });\n  }\n\n  public ack(\n    messageId: string,\n    subscriptionId: string,\n    headers: StompHeaders = {}\n  ): void {\n    headers = (Object as any).assign({}, headers);\n\n    if (this._connectedVersion === Versions.V1_2) {\n      headers.id = messageId;\n    } else {\n      headers['message-id'] = messageId;\n    }\n    headers.subscription = subscriptionId;\n    this._transmit({ command: 'ACK', headers });\n  }\n\n  public nack(\n    messageId: string,\n    subscriptionId: string,\n    headers: StompHeaders = {}\n  ): void {\n    headers = (Object as any).assign({}, headers);\n\n    if (this._connectedVersion === Versions.V1_2) {\n      headers.id = messageId;\n    } else {\n      headers['message-id'] = messageId;\n    }\n    headers.subscription = subscriptionId;\n    return this._transmit({ command: 'NACK', headers });\n  }\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,IAAI,QAAQ,WAAW;AAEhC,SAASC,SAAS,QAAQ,iBAAiB;AAG3C,SAASC,MAAM,QAAQ,aAAa;AAGpC,SAASC,MAAM,QAAQ,aAAa;AACpC,SASEC,gBAAgB,QAEX,YAAY;AACnB,SAASC,QAAQ,QAAQ,eAAe;AAExC;;;;;;;AAOA,OAAM,MAAOC,YAAY;EAyCvB,IAAIC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACC,iBAAiB;EAC/B;EAGA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAaAC,YACUC,OAAe,EAChBC,UAAwB,EAC/BC,MAA4B;IAFpB,KAAAF,OAAO,GAAPA,OAAO;IACR,KAAAC,UAAU,GAAVA,UAAU;IAbX,KAAAH,UAAU,GAAY,KAAK;IAuHlB,KAAAK,oBAAoB,GAEjC;MACF;MACAC,SAAS,EAAEC,KAAK,IAAG;QACjB,IAAI,CAACC,KAAK,CAAC,uBAAuBD,KAAK,CAACE,OAAO,CAACC,MAAM,EAAE,CAAC;QACzD,IAAI,CAACV,UAAU,GAAG,IAAI;QACtB,IAAI,CAACF,iBAAiB,GAAGS,KAAK,CAACE,OAAO,CAACE,OAAO;QAC9C;QACA,IAAI,IAAI,CAACb,iBAAiB,KAAKH,QAAQ,CAACiB,IAAI,EAAE;UAC5C,IAAI,CAACC,mBAAmB,GAAG,IAAI;QACjC;QAEA,IAAI,CAACC,eAAe,CAACP,KAAK,CAACE,OAAO,CAAC;QACnC,IAAI,CAACM,SAAS,CAACR,KAAK,CAAC;MACvB,CAAC;MAED;MACAS,OAAO,EAAET,KAAK,IAAG;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAMU,YAAY,GAAGV,KAAK,CAACE,OAAO,CAACQ,YAAY;QAC/C,MAAMC,SAAS,GACb,IAAI,CAACC,cAAc,CAACF,YAAY,CAAC,IAAI,IAAI,CAACG,kBAAkB;QAE9D;QACA,MAAMC,OAAO,GAAGd,KAAiB;QAEjC,MAAMe,MAAM,GAAG,IAAI;QACnB,MAAMC,SAAS,GACb,IAAI,CAACzB,iBAAiB,KAAKH,QAAQ,CAACiB,IAAI,GACpCS,OAAO,CAACZ,OAAO,CAACe,GAAG,GACnBH,OAAO,CAACZ,OAAO,CAAC,YAAY,CAAC;QAEnC;QACA;QACAY,OAAO,CAACG,GAAG,GAAG,CAACf,OAAA,GAAwB,EAAE,KAAU;UACjD,OAAOa,MAAM,CAACE,GAAG,CAACD,SAAS,EAAEN,YAAY,EAAER,OAAO,CAAC;QACrD,CAAC;QACDY,OAAO,CAACI,IAAI,GAAG,CAAChB,OAAA,GAAwB,EAAE,KAAU;UAClD,OAAOa,MAAM,CAACG,IAAI,CAACF,SAAS,EAAEN,YAAY,EAAER,OAAO,CAAC;QACtD,CAAC;QACDS,SAAS,CAACG,OAAO,CAAC;MACpB,CAAC;MAED;MACAK,OAAO,EAAEnB,KAAK,IAAG;QACf,MAAMoB,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAACrB,KAAK,CAACE,OAAO,CAAC,YAAY,CAAC,CAAC;QACnE,IAAIkB,QAAQ,EAAE;UACZA,QAAQ,CAACpB,KAAK,CAAC;UACf;UACA,OAAO,IAAI,CAACqB,gBAAgB,CAACrB,KAAK,CAACE,OAAO,CAAC,YAAY,CAAC,CAAC;QAC3D,CAAC,MAAM;UACL,IAAI,CAACoB,kBAAkB,CAACtB,KAAK,CAAC;QAChC;MACF,CAAC;MAED;MACAuB,KAAK,EAAEvB,KAAK,IAAG;QACb,IAAI,CAACwB,YAAY,CAACxB,KAAK,CAAC;MAC1B;KACD;IAzKC;IACA,IAAI,CAACyB,QAAQ,GAAG,CAAC;IAEjB;IACA,IAAI,CAACb,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAACS,gBAAgB,GAAG,EAAE;IAE1B,IAAI,CAACK,YAAY,GAAG,EAAE;IAEtB,IAAI,CAACpB,mBAAmB,GAAG,KAAK;IAEhC,IAAI,CAACqB,qBAAqB,GAAGC,IAAI,CAACC,GAAG,EAAE;IAEvC,IAAI,CAAC5B,KAAK,GAAGJ,MAAM,CAACI,KAAK;IACzB,IAAI,CAAC6B,aAAa,GAAGjC,MAAM,CAACiC,aAAa;IACzC,IAAI,CAACC,cAAc,GAAGlC,MAAM,CAACkC,cAAc;IAC3C,IAAI,CAACC,iBAAiB,GAAGnC,MAAM,CAACmC,iBAAiB;IACjD,IAAI,CAACC,iBAAiB,GAAGpC,MAAM,CAACoC,iBAAiB;IACjD,IAAI,CAACC,iBAAiB,GAAGrC,MAAM,CAACqC,iBAAiB;IACjD,IAAI,CAACC,gBAAgB,GAAGtC,MAAM,CAACsC,gBAAgB;IAC/C,IAAI,CAACC,qBAAqB,GAAGvC,MAAM,CAACuC,qBAAqB;IACzD,IAAI,CAACC,mBAAmB,GAAGxC,MAAM,CAACwC,mBAAmB;IACrD,IAAI,CAACC,mBAAmB,GAAGzC,MAAM,CAACyC,mBAAmB;IACrD,IAAI,CAACC,2BAA2B,GAAG1C,MAAM,CAAC0C,2BAA2B;IACrE,IAAI,CAACC,6BAA6B,GAAG3C,MAAM,CAAC2C,6BAA6B;IACzE,IAAI,CAAChC,SAAS,GAAGX,MAAM,CAACW,SAAS;IACjC,IAAI,CAACiC,YAAY,GAAG5C,MAAM,CAAC4C,YAAY;IACvC,IAAI,CAACjB,YAAY,GAAG3B,MAAM,CAAC2B,YAAY;IACvC,IAAI,CAACkB,gBAAgB,GAAG7C,MAAM,CAAC6C,gBAAgB;IAC/C,IAAI,CAACC,gBAAgB,GAAG9C,MAAM,CAAC8C,gBAAgB;IAC/C,IAAI,CAAC9B,kBAAkB,GAAGhB,MAAM,CAACgB,kBAAkB;IACnD,IAAI,CAACS,kBAAkB,GAAGzB,MAAM,CAACyB,kBAAkB;IACnD,IAAI,CAACsB,gBAAgB,GAAG/C,MAAM,CAAC+C,gBAAgB;EACjD;EAEOC,KAAKA,CAAA;IACV,MAAMC,MAAM,GAAG,IAAI7D,MAAM;IACvB;IACA8D,QAAQ,IAAG;MACT,MAAM/C,KAAK,GAAGhB,SAAS,CAACgE,YAAY,CAClCD,QAAQ,EACR,IAAI,CAACzC,mBAAmB,CACzB;MAED;MACA,IAAI,CAAC,IAAI,CAACgC,mBAAmB,EAAE;QAC7B,IAAI,CAACrC,KAAK,CAAC,OAAOD,KAAK,EAAE,CAAC;MAC5B;MAEA,MAAMiD,kBAAkB,GACtB,IAAI,CAACnD,oBAAoB,CAACE,KAAK,CAACkD,OAAO,CAAC,IAAI,IAAI,CAACN,gBAAgB;MACnEK,kBAAkB,CAACjD,KAAK,CAAC;IAC3B,CAAC;IACD;IACA,MAAK;MACH,IAAI,CAACC,KAAK,CAAC,UAAU,CAAC;IACxB,CAAC,CACF;IAED,IAAI,CAACL,UAAU,CAACuD,SAAS,GAAIC,GAA6B,IAAI;MAC5D,IAAI,CAACnD,KAAK,CAAC,eAAe,CAAC;MAC3B,IAAI,CAAC0B,qBAAqB,GAAGC,IAAI,CAACC,GAAG,EAAE;MAEvC,IAAI,IAAI,CAACS,mBAAmB,EAAE;QAC5B,MAAMe,gBAAgB,GACpBD,GAAG,CAACE,IAAI,YAAYC,WAAW,GAC3B,IAAIC,WAAW,EAAE,CAACC,MAAM,CAACL,GAAG,CAACE,IAAI,CAAC,GAClCF,GAAG,CAACE,IAAI;QACd,IAAI,CAACrD,KAAK,CAAC,OAAOoD,gBAAgB,EAAE,CAAC;MACvC;MAEAP,MAAM,CAACY,UAAU,CACfN,GAAG,CAACE,IAA4B,EAChC,IAAI,CAACf,2BAA2B,CACjC;IACH,CAAC;IAED,IAAI,CAAC3C,UAAU,CAAC+D,OAAO,GAAIC,UAAU,IAAU;MAC7C,IAAI,CAAC3D,KAAK,CAAC,wBAAwB,IAAI,CAACL,UAAU,CAACiE,GAAG,EAAE,CAAC;MACzD,IAAI,CAACC,QAAQ,EAAE;MACf,IAAI,CAACpB,gBAAgB,CAACkB,UAAU,CAAC;IACnC,CAAC;IAED,IAAI,CAAChE,UAAU,CAACmE,OAAO,GAAIC,UAAU,IAAU;MAC7C,IAAI,CAACrB,gBAAgB,CAACqB,UAAU,CAAC;IACnC,CAAC;IAED,IAAI,CAACpE,UAAU,CAACqE,MAAM,GAAG,MAAK;MAC5B;MACA,MAAMlC,cAAc,GAAImC,MAAc,CAACC,MAAM,CAAC,EAAE,EAAE,IAAI,CAACpC,cAAc,CAAC;MAEtE,IAAI,CAAC9B,KAAK,CAAC,sBAAsB,CAAC;MAClC8B,cAAc,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAACD,aAAa,CAACsC,iBAAiB,EAAE;MACzErC,cAAc,CAAC,YAAY,CAAC,GAAG,CAC7B,IAAI,CAACG,iBAAiB,EACtB,IAAI,CAACD,iBAAiB,CACvB,CAACoC,IAAI,CAAC,GAAG,CAAC;MACX,IAAI,CAACC,SAAS,CAAC;QAAEpB,OAAO,EAAE,SAAS;QAAEhD,OAAO,EAAE6B;MAAc,CAAE,CAAC;IACjE,CAAC;EACH;EAsEQxB,eAAeA,CAACL,OAAqB;IAC3C,IACEA,OAAO,CAACE,OAAO,KAAKhB,QAAQ,CAACmF,IAAI,IACjCrE,OAAO,CAACE,OAAO,KAAKhB,QAAQ,CAACiB,IAAI,EACjC;MACA;IACF;IAEA;IACA;IACA,IAAI,CAACH,OAAO,CAAC,YAAY,CAAC,EAAE;MAC1B;IACF;IAEA;IACA;IACA;IACA,MAAM,CAACsE,cAAc,EAAEC,cAAc,CAAC,GAAGvE,OAAO,CAAC,YAAY,CAAC,CAC3DwE,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,CAAS,IAAKC,QAAQ,CAACD,CAAC,EAAE,EAAE,CAAC,CAAC;IAEtC,IAAI,IAAI,CAAC1C,iBAAiB,KAAK,CAAC,IAAIuC,cAAc,KAAK,CAAC,EAAE;MACxD,MAAMK,GAAG,GAAWC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC9C,iBAAiB,EAAEuC,cAAc,CAAC;MACpE,IAAI,CAACxE,KAAK,CAAC,mBAAmB6E,GAAG,IAAI,CAAC;MAEtC,IAAI,CAACG,OAAO,GAAG,IAAI/F,MAAM,CAAC4F,GAAG,EAAE,IAAI,CAACnF,OAAO,CAACuF,iBAAiB,EAAE,IAAI,CAACjF,KAAK,CAAC;MAC1E,IAAI,CAACgF,OAAO,CAACpC,KAAK,CAAC,MAAK;QACtB,IAAI,IAAI,CAACjD,UAAU,CAACuF,UAAU,KAAKhG,gBAAgB,CAACiG,IAAI,EAAE;UACxD,IAAI,CAACxF,UAAU,CAACyF,IAAI,CAACtG,IAAI,CAACuG,EAAE,CAAC;UAC7B,IAAI,CAACrF,KAAK,CAAC,UAAU,CAAC;QACxB;MACF,CAAC,CAAC;IACJ;IAEA,IAAI,IAAI,CAACgC,iBAAiB,KAAK,CAAC,IAAIuC,cAAc,KAAK,CAAC,EAAE;MACxD,MAAMM,GAAG,GAAWC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/C,iBAAiB,EAAEuC,cAAc,CAAC;MACpE,IAAI,CAACvE,KAAK,CAAC,oBAAoB6E,GAAG,IAAI,CAAC;MACvC,IAAI,CAACS,OAAO,GAAGC,WAAW,CAAC,MAAK;QAC9B,MAAMC,KAAK,GAAG7D,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACF,qBAAqB;QACrD;QACA,IAAI8D,KAAK,GAAGX,GAAG,GAAG,CAAC,EAAE;UACnB,IAAI,CAAC7E,KAAK,CAAC,gDAAgDwF,KAAK,IAAI,CAAC;UACrE,IAAI,CAACC,wBAAwB,EAAE;QACjC;MACF,CAAC,EAAEZ,GAAG,CAAC;IACT;EACF;EAEQY,wBAAwBA,CAAA;IAC9B,IAAI,IAAI,CAAClD,6BAA6B,EAAE;MACtC,IAAI,CAACvC,KAAK,CACR,oEAAoE,CACrE;MACD,IAAI,CAAC0F,gBAAgB,EAAE;IACzB,CAAC,MAAM;MACL,IAAI,CAAC1F,KAAK,CAAC,gCAAgC,CAAC;MAC5C,IAAI,CAAC2F,eAAe,EAAE;IACxB;EACF;EAEOC,eAAeA,CAAA;IACpB,IAAI,IAAI,CAACjG,UAAU,EAAE;MACnB,IACE,IAAI,CAACA,UAAU,CAACuF,UAAU,KAAKhG,gBAAgB,CAAC2G,UAAU,IAC1D,IAAI,CAAClG,UAAU,CAACuF,UAAU,KAAKhG,gBAAgB,CAACiG,IAAI,EACpD;QACA,IAAI,CAACM,wBAAwB,EAAE;MACjC;IACF;EACF;EAEOE,eAAeA,CAAA;IACpB,IAAI,CAAChG,UAAU,CAACuD,SAAS,GAAG,MAAK,CAAE,CAAC,CAAC,CAAC;IACtC,IAAI,CAACvD,UAAU,CAACmG,KAAK,EAAE;EACzB;EAEOJ,gBAAgBA,CAAA;IACrB,IAAI,OAAO,IAAI,CAAC/F,UAAU,CAACoG,SAAS,KAAK,UAAU,EAAE;MACnDlH,gBAAgB,CAAC,IAAI,CAACc,UAAU,EAAGqG,GAAW,IAAK,IAAI,CAAChG,KAAK,CAACgG,GAAG,CAAC,CAAC;IACrE;IAEA;IACA,IAAI,CAACrG,UAAU,CAACoG,SAAS,EAAE;EAC7B;EAEQ1B,SAASA,CAAC4B,MAMjB;IACC,MAAM;MAAEhD,OAAO;MAAEhD,OAAO;MAAEiG,IAAI;MAAEC,UAAU;MAAEC;IAAuB,CAAE,GACnEH,MAAM;IACR,MAAMlG,KAAK,GAAG,IAAIhB,SAAS,CAAC;MAC1BkE,OAAO;MACPhD,OAAO;MACPiG,IAAI;MACJC,UAAU;MACVE,kBAAkB,EAAE,IAAI,CAAChG,mBAAmB;MAC5C+F;KACD,CAAC;IAEF,IAAIE,QAAQ,GAAGvG,KAAK,CAACwG,SAAS,EAAE;IAEhC,IAAI,IAAI,CAAClE,mBAAmB,EAAE;MAC5B,IAAI,CAACrC,KAAK,CAAC,OAAOsG,QAAQ,EAAE,CAAC;IAC/B,CAAC,MAAM;MACL,IAAI,CAACtG,KAAK,CAAC,OAAOD,KAAK,EAAE,CAAC;IAC5B;IAEA,IAAI,IAAI,CAACqC,mBAAmB,IAAI,OAAOkE,QAAQ,KAAK,QAAQ,EAAE;MAC5DA,QAAQ,GAAG,IAAIE,WAAW,EAAE,CAACC,MAAM,CAACH,QAAQ,CAAC;IAC/C;IAEA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAI,CAAC,IAAI,CAACpE,gBAAgB,EAAE;MAC1D,IAAI,CAACvC,UAAU,CAACyF,IAAI,CAACkB,QAAQ,CAAC;IAChC,CAAC,MAAM;MACL,IAAII,GAAG,GAAGJ,QAAkB;MAC5B,OAAOI,GAAG,CAACC,MAAM,GAAG,CAAC,EAAE;QACrB,MAAMC,KAAK,GAAGF,GAAG,CAACG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC1E,qBAAqB,CAAC;QAC1DuE,GAAG,GAAGA,GAAG,CAACG,SAAS,CAAC,IAAI,CAAC1E,qBAAqB,CAAC;QAC/C,IAAI,CAACxC,UAAU,CAACyF,IAAI,CAACwB,KAAK,CAAC;QAC3B,IAAI,CAAC5G,KAAK,CAAC,gBAAgB4G,KAAK,CAACD,MAAM,iBAAiBD,GAAG,CAACC,MAAM,EAAE,CAAC;MACvE;IACF;EACF;EAEOG,OAAOA,CAAA;IACZ,IAAI,IAAI,CAACvH,SAAS,EAAE;MAClB,IAAI;QACF;QACA,MAAMwC,iBAAiB,GAAIkC,MAAc,CAACC,MAAM,CAC9C,EAAE,EACF,IAAI,CAACnC,iBAAiB,CACvB;QAED,IAAI,CAACA,iBAAiB,CAACgF,OAAO,EAAE;UAC9BhF,iBAAiB,CAACgF,OAAO,GAAG,SAAS,IAAI,CAACvF,QAAQ,EAAE,EAAE;QACxD;QACA,IAAI,CAACwF,eAAe,CAACjF,iBAAiB,CAACgF,OAAO,EAAEhH,KAAK,IAAG;UACtD,IAAI,CAAC4F,eAAe,EAAE;UACtB,IAAI,CAAC9B,QAAQ,EAAE;UACf,IAAI,CAACrB,YAAY,CAACzC,KAAK,CAAC;QAC1B,CAAC,CAAC;QACF,IAAI,CAACsE,SAAS,CAAC;UAAEpB,OAAO,EAAE,YAAY;UAAEhD,OAAO,EAAE8B;QAAiB,CAAE,CAAC;MACvE,CAAC,CAAC,OAAOkF,KAAK,EAAE;QACd,IAAI,CAACjH,KAAK,CAAC,oCAAoCiH,KAAK,EAAE,CAAC;MACzD;IACF,CAAC,MAAM;MACL,IACE,IAAI,CAACtH,UAAU,CAACuF,UAAU,KAAKhG,gBAAgB,CAAC2G,UAAU,IAC1D,IAAI,CAAClG,UAAU,CAACuF,UAAU,KAAKhG,gBAAgB,CAACiG,IAAI,EACpD;QACA,IAAI,CAACQ,eAAe,EAAE;MACxB;IACF;EACF;EAEQ9B,QAAQA,CAAA;IACd,IAAI,CAACrE,UAAU,GAAG,KAAK;IAEvB,IAAI,IAAI,CAACwF,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACkC,IAAI,EAAE;MACnB,IAAI,CAAClC,OAAO,GAAGmC,SAAS;IAC1B;IACA,IAAI,IAAI,CAAC7B,OAAO,EAAE;MAChB8B,aAAa,CAAC,IAAI,CAAC9B,OAAO,CAAC;MAC3B,IAAI,CAACA,OAAO,GAAG6B,SAAS;IAC1B;EACF;EAEOE,OAAOA,CAACpB,MAAsB;IACnC,MAAM;MAAEqB,WAAW;MAAErH,OAAO;MAAEiG,IAAI;MAAEC,UAAU;MAAEC;IAAuB,CAAE,GACvEH,MAAM;IACR,MAAMsB,IAAI,GAAkBtD,MAAc,CAACC,MAAM,CAAC;MAAEoD;IAAW,CAAE,EAAErH,OAAO,CAAC;IAC3E,IAAI,CAACoE,SAAS,CAAC;MACbpB,OAAO,EAAE,MAAM;MACfhD,OAAO,EAAEsH,IAAI;MACbrB,IAAI;MACJC,UAAU;MACVC;KACD,CAAC;EACJ;EAEOY,eAAeA,CAACQ,SAAiB,EAAErG,QAA2B;IACnE,IAAI,CAACC,gBAAgB,CAACoG,SAAS,CAAC,GAAGrG,QAAQ;EAC7C;EAEOsG,SAASA,CACdH,WAAmB,EACnBnG,QAA6B,EAC7BlB,OAAA,GAAwB,EAAE;IAE1BA,OAAO,GAAIgE,MAAc,CAACC,MAAM,CAAC,EAAE,EAAEjE,OAAO,CAAC;IAE7C,IAAI,CAACA,OAAO,CAACyH,EAAE,EAAE;MACfzH,OAAO,CAACyH,EAAE,GAAG,OAAO,IAAI,CAAClG,QAAQ,EAAE,EAAE;IACvC;IACAvB,OAAO,CAACqH,WAAW,GAAGA,WAAW;IACjC,IAAI,CAAC3G,cAAc,CAACV,OAAO,CAACyH,EAAE,CAAC,GAAGvG,QAAQ;IAC1C,IAAI,CAACkD,SAAS,CAAC;MAAEpB,OAAO,EAAE,WAAW;MAAEhD;IAAO,CAAE,CAAC;IACjD,MAAMa,MAAM,GAAG,IAAI;IACnB,OAAO;MACL4G,EAAE,EAAEzH,OAAO,CAACyH,EAAE;MAEdC,WAAWA,CAACJ,IAAI;QACd,OAAOzG,MAAM,CAAC6G,WAAW,CAAC1H,OAAO,CAACyH,EAAE,EAAEH,IAAI,CAAC;MAC7C;KACD;EACH;EAEOI,WAAWA,CAACD,EAAU,EAAEzH,OAAA,GAAwB,EAAE;IACvDA,OAAO,GAAIgE,MAAc,CAACC,MAAM,CAAC,EAAE,EAAEjE,OAAO,CAAC;IAE7C,OAAO,IAAI,CAACU,cAAc,CAAC+G,EAAE,CAAC;IAC9BzH,OAAO,CAACyH,EAAE,GAAGA,EAAE;IACf,IAAI,CAACrD,SAAS,CAAC;MAAEpB,OAAO,EAAE,aAAa;MAAEhD;IAAO,CAAE,CAAC;EACrD;EAEO2H,KAAKA,CAACC,aAAqB;IAChC,MAAMC,IAAI,GAAGD,aAAa,IAAI,MAAM,IAAI,CAACrG,QAAQ,EAAE,EAAE;IACrD,IAAI,CAAC6C,SAAS,CAAC;MACbpB,OAAO,EAAE,OAAO;MAChBhD,OAAO,EAAE;QACP8H,WAAW,EAAED;;KAEhB,CAAC;IACF,MAAMhH,MAAM,GAAG,IAAI;IACnB,OAAO;MACL4G,EAAE,EAAEI,IAAI;MACRE,MAAMA,CAAA;QACJlH,MAAM,CAACkH,MAAM,CAACF,IAAI,CAAC;MACrB,CAAC;MACDG,KAAKA,CAAA;QACHnH,MAAM,CAACmH,KAAK,CAACH,IAAI,CAAC;MACpB;KACD;EACH;EAEOE,MAAMA,CAACH,aAAqB;IACjC,IAAI,CAACxD,SAAS,CAAC;MACbpB,OAAO,EAAE,QAAQ;MACjBhD,OAAO,EAAE;QACP8H,WAAW,EAAEF;;KAEhB,CAAC;EACJ;EAEOI,KAAKA,CAACJ,aAAqB;IAChC,IAAI,CAACxD,SAAS,CAAC;MACbpB,OAAO,EAAE,OAAO;MAChBhD,OAAO,EAAE;QACP8H,WAAW,EAAEF;;KAEhB,CAAC;EACJ;EAEO7G,GAAGA,CACRD,SAAiB,EACjBmH,cAAsB,EACtBjI,OAAA,GAAwB,EAAE;IAE1BA,OAAO,GAAIgE,MAAc,CAACC,MAAM,CAAC,EAAE,EAAEjE,OAAO,CAAC;IAE7C,IAAI,IAAI,CAACX,iBAAiB,KAAKH,QAAQ,CAACiB,IAAI,EAAE;MAC5CH,OAAO,CAACyH,EAAE,GAAG3G,SAAS;IACxB,CAAC,MAAM;MACLd,OAAO,CAAC,YAAY,CAAC,GAAGc,SAAS;IACnC;IACAd,OAAO,CAACQ,YAAY,GAAGyH,cAAc;IACrC,IAAI,CAAC7D,SAAS,CAAC;MAAEpB,OAAO,EAAE,KAAK;MAAEhD;IAAO,CAAE,CAAC;EAC7C;EAEOgB,IAAIA,CACTF,SAAiB,EACjBmH,cAAsB,EACtBjI,OAAA,GAAwB,EAAE;IAE1BA,OAAO,GAAIgE,MAAc,CAACC,MAAM,CAAC,EAAE,EAAEjE,OAAO,CAAC;IAE7C,IAAI,IAAI,CAACX,iBAAiB,KAAKH,QAAQ,CAACiB,IAAI,EAAE;MAC5CH,OAAO,CAACyH,EAAE,GAAG3G,SAAS;IACxB,CAAC,MAAM;MACLd,OAAO,CAAC,YAAY,CAAC,GAAGc,SAAS;IACnC;IACAd,OAAO,CAACQ,YAAY,GAAGyH,cAAc;IACrC,OAAO,IAAI,CAAC7D,SAAS,CAAC;MAAEpB,OAAO,EAAE,MAAM;MAAEhD;IAAO,CAAE,CAAC;EACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}