{"ast": null, "code": "export { default } from './getScrollbarSize';", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@mui/utils/esm/getScrollbarSize/index.js"], "sourcesContent": ["export { default } from './getScrollbarSize';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}