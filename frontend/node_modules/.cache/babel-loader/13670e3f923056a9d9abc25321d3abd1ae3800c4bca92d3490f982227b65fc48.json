{"ast": null, "code": "module.exports = global.EventSource;", "map": {"version": 3, "names": ["module", "exports", "global", "EventSource"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/browser/eventsource.js"], "sourcesContent": ["module.exports = global.EventSource;\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}