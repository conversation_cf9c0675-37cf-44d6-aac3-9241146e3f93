{"ast": null, "code": "export default function composeClasses(slots, getUtilityClass, classes = undefined) {\n  const output = {};\n  Object.keys(slots).forEach(\n  // `Object.keys(slots)` can't be wider than `T` because we infer `T` from `slots`.\n  // @ts-expect-error https://github.com/microsoft/TypeScript/pull/12253#issuecomment-263132208\n  slot => {\n    output[slot] = slots[slot].reduce((acc, key) => {\n      if (key) {\n        const utilityClass = getUtilityClass(key);\n        if (utilityClass !== '') {\n          acc.push(utilityClass);\n        }\n        if (classes && classes[key]) {\n          acc.push(classes[key]);\n        }\n      }\n      return acc;\n    }, []).join(' ');\n  });\n  return output;\n}", "map": {"version": 3, "names": ["composeClasses", "slots", "getUtilityClass", "classes", "undefined", "output", "Object", "keys", "for<PERSON>ach", "slot", "reduce", "acc", "key", "utilityClass", "push", "join"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@mui/utils/esm/composeClasses/composeClasses.js"], "sourcesContent": ["export default function composeClasses(slots, getUtilityClass, classes = undefined) {\n  const output = {};\n  Object.keys(slots).forEach(\n  // `Object.keys(slots)` can't be wider than `T` because we infer `T` from `slots`.\n  // @ts-expect-error https://github.com/microsoft/TypeScript/pull/12253#issuecomment-263132208\n  slot => {\n    output[slot] = slots[slot].reduce((acc, key) => {\n      if (key) {\n        const utilityClass = getUtilityClass(key);\n        if (utilityClass !== '') {\n          acc.push(utilityClass);\n        }\n        if (classes && classes[key]) {\n          acc.push(classes[key]);\n        }\n      }\n      return acc;\n    }, []).join(' ');\n  });\n  return output;\n}"], "mappings": "AAAA,eAAe,SAASA,cAAcA,CAACC,KAAK,EAAEC,eAAe,EAAEC,OAAO,GAAGC,SAAS,EAAE;EAClF,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjBC,MAAM,CAACC,IAAI,CAACN,KAAK,CAAC,CAACO,OAAO;EAC1B;EACA;EACAC,IAAI,IAAI;IACNJ,MAAM,CAACI,IAAI,CAAC,GAAGR,KAAK,CAACQ,IAAI,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MAC9C,IAAIA,GAAG,EAAE;QACP,MAAMC,YAAY,GAAGX,eAAe,CAACU,GAAG,CAAC;QACzC,IAAIC,YAAY,KAAK,EAAE,EAAE;UACvBF,GAAG,CAACG,IAAI,CAACD,YAAY,CAAC;QACxB;QACA,IAAIV,OAAO,IAAIA,OAAO,CAACS,GAAG,CAAC,EAAE;UAC3BD,GAAG,CAACG,IAAI,CAACX,OAAO,CAACS,GAAG,CAAC,CAAC;QACxB;MACF;MACA,OAAOD,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC;EAClB,CAAC,CAAC;EACF,OAAOV,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}