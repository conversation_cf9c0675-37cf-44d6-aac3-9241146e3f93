{"ast": null, "code": "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "map": {"version": 3, "names": ["getWindowScroll", "getWindow", "isHTMLElement", "getHTMLElementScroll", "getNodeScroll", "node"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js"], "sourcesContent": ["import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,sBAAsB;AAClD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,OAAOC,oBAAoB,MAAM,2BAA2B;AAC5D,eAAe,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC1C,IAAIA,IAAI,KAAKJ,SAAS,CAACI,IAAI,CAAC,IAAI,CAACH,aAAa,CAACG,IAAI,CAAC,EAAE;IACpD,OAAOL,eAAe,CAACK,IAAI,CAAC;EAC9B,CAAC,MAAM;IACL,OAAOF,oBAAoB,CAACE,IAAI,CAAC;EACnC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}