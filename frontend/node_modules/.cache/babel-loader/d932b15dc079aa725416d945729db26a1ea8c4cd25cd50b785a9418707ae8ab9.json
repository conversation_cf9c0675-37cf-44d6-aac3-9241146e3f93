{"ast": null, "code": "'use strict';\n\nvar random = require('./random');\nvar onUnload = {},\n  afterUnload = false\n  // detect google chrome packaged apps because they don't allow the 'unload' event\n  ,\n  isChromePackagedApp = global.chrome && global.chrome.app && global.chrome.app.runtime;\nmodule.exports = {\n  attachEvent: function (event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.addEventListener(event, listener, false);\n    } else if (global.document && global.attachEvent) {\n      // IE quirks.\n      // According to: http://stevesouders.com/misc/test-postmessage.php\n      // the message gets delivered only to 'document', not 'window'.\n      global.document.attachEvent('on' + event, listener);\n      // I get 'window' for ie8.\n      global.attachEvent('on' + event, listener);\n    }\n  },\n  detachEvent: function (event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.removeEventListener(event, listener, false);\n    } else if (global.document && global.detachEvent) {\n      global.document.detachEvent('on' + event, listener);\n      global.detachEvent('on' + event, listener);\n    }\n  },\n  unloadAdd: function (listener) {\n    if (isChromePackagedApp) {\n      return null;\n    }\n    var ref = random.string(8);\n    onUnload[ref] = listener;\n    if (afterUnload) {\n      setTimeout(this.triggerUnloadCallbacks, 0);\n    }\n    return ref;\n  },\n  unloadDel: function (ref) {\n    if (ref in onUnload) {\n      delete onUnload[ref];\n    }\n  },\n  triggerUnloadCallbacks: function () {\n    for (var ref in onUnload) {\n      onUnload[ref]();\n      delete onUnload[ref];\n    }\n  }\n};\nvar unloadTriggered = function () {\n  if (afterUnload) {\n    return;\n  }\n  afterUnload = true;\n  module.exports.triggerUnloadCallbacks();\n};\n\n// 'unload' alone is not reliable in opera within an iframe, but we\n// can't use `beforeunload` as IE fires it on javascript: links.\nif (!isChromePackagedApp) {\n  module.exports.attachEvent('unload', unloadTriggered);\n}", "map": {"version": 3, "names": ["random", "require", "onUnload", "afterUnload", "isChromePackagedApp", "global", "chrome", "app", "runtime", "module", "exports", "attachEvent", "event", "listener", "addEventListener", "document", "detachEvent", "removeEventListener", "unloadAdd", "ref", "string", "setTimeout", "triggerUnloadCallbacks", "unloadDel", "unloadTriggered"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/utils/event.js"], "sourcesContent": ["'use strict';\n\nvar random = require('./random');\n\nvar onUnload = {}\n  , afterUnload = false\n    // detect google chrome packaged apps because they don't allow the 'unload' event\n  , isChromePackagedApp = global.chrome && global.chrome.app && global.chrome.app.runtime\n  ;\n\nmodule.exports = {\n  attachEvent: function(event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.addEventListener(event, listener, false);\n    } else if (global.document && global.attachEvent) {\n      // IE quirks.\n      // According to: http://stevesouders.com/misc/test-postmessage.php\n      // the message gets delivered only to 'document', not 'window'.\n      global.document.attachEvent('on' + event, listener);\n      // I get 'window' for ie8.\n      global.attachEvent('on' + event, listener);\n    }\n  }\n\n, detachEvent: function(event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.removeEventListener(event, listener, false);\n    } else if (global.document && global.detachEvent) {\n      global.document.detachEvent('on' + event, listener);\n      global.detachEvent('on' + event, listener);\n    }\n  }\n\n, unloadAdd: function(listener) {\n    if (isChromePackagedApp) {\n      return null;\n    }\n\n    var ref = random.string(8);\n    onUnload[ref] = listener;\n    if (afterUnload) {\n      setTimeout(this.triggerUnloadCallbacks, 0);\n    }\n    return ref;\n  }\n\n, unloadDel: function(ref) {\n    if (ref in onUnload) {\n      delete onUnload[ref];\n    }\n  }\n\n, triggerUnloadCallbacks: function() {\n    for (var ref in onUnload) {\n      onUnload[ref]();\n      delete onUnload[ref];\n    }\n  }\n};\n\nvar unloadTriggered = function() {\n  if (afterUnload) {\n    return;\n  }\n  afterUnload = true;\n  module.exports.triggerUnloadCallbacks();\n};\n\n// 'unload' alone is not reliable in opera within an iframe, but we\n// can't use `beforeunload` as IE fires it on javascript: links.\nif (!isChromePackagedApp) {\n  module.exports.attachEvent('unload', unloadTriggered);\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,UAAU,CAAC;AAEhC,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACbC,WAAW,GAAG;EACd;EAAA;EACAC,mBAAmB,GAAGC,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,GAAG,IAAIF,MAAM,CAACC,MAAM,CAACC,GAAG,CAACC,OAAO;AAGzFC,MAAM,CAACC,OAAO,GAAG;EACfC,WAAW,EAAE,SAAAA,CAASC,KAAK,EAAEC,QAAQ,EAAE;IACrC,IAAI,OAAOR,MAAM,CAACS,gBAAgB,KAAK,WAAW,EAAE;MAClDT,MAAM,CAACS,gBAAgB,CAACF,KAAK,EAAEC,QAAQ,EAAE,KAAK,CAAC;IACjD,CAAC,MAAM,IAAIR,MAAM,CAACU,QAAQ,IAAIV,MAAM,CAACM,WAAW,EAAE;MAChD;MACA;MACA;MACAN,MAAM,CAACU,QAAQ,CAACJ,WAAW,CAAC,IAAI,GAAGC,KAAK,EAAEC,QAAQ,CAAC;MACnD;MACAR,MAAM,CAACM,WAAW,CAAC,IAAI,GAAGC,KAAK,EAAEC,QAAQ,CAAC;IAC5C;EACF,CAAC;EAEDG,WAAW,EAAE,SAAAA,CAASJ,KAAK,EAAEC,QAAQ,EAAE;IACrC,IAAI,OAAOR,MAAM,CAACS,gBAAgB,KAAK,WAAW,EAAE;MAClDT,MAAM,CAACY,mBAAmB,CAACL,KAAK,EAAEC,QAAQ,EAAE,KAAK,CAAC;IACpD,CAAC,MAAM,IAAIR,MAAM,CAACU,QAAQ,IAAIV,MAAM,CAACW,WAAW,EAAE;MAChDX,MAAM,CAACU,QAAQ,CAACC,WAAW,CAAC,IAAI,GAAGJ,KAAK,EAAEC,QAAQ,CAAC;MACnDR,MAAM,CAACW,WAAW,CAAC,IAAI,GAAGJ,KAAK,EAAEC,QAAQ,CAAC;IAC5C;EACF,CAAC;EAEDK,SAAS,EAAE,SAAAA,CAASL,QAAQ,EAAE;IAC5B,IAAIT,mBAAmB,EAAE;MACvB,OAAO,IAAI;IACb;IAEA,IAAIe,GAAG,GAAGnB,MAAM,CAACoB,MAAM,CAAC,CAAC,CAAC;IAC1BlB,QAAQ,CAACiB,GAAG,CAAC,GAAGN,QAAQ;IACxB,IAAIV,WAAW,EAAE;MACfkB,UAAU,CAAC,IAAI,CAACC,sBAAsB,EAAE,CAAC,CAAC;IAC5C;IACA,OAAOH,GAAG;EACZ,CAAC;EAEDI,SAAS,EAAE,SAAAA,CAASJ,GAAG,EAAE;IACvB,IAAIA,GAAG,IAAIjB,QAAQ,EAAE;MACnB,OAAOA,QAAQ,CAACiB,GAAG,CAAC;IACtB;EACF,CAAC;EAEDG,sBAAsB,EAAE,SAAAA,CAAA,EAAW;IACjC,KAAK,IAAIH,GAAG,IAAIjB,QAAQ,EAAE;MACxBA,QAAQ,CAACiB,GAAG,CAAC,CAAC,CAAC;MACf,OAAOjB,QAAQ,CAACiB,GAAG,CAAC;IACtB;EACF;AACF,CAAC;AAED,IAAIK,eAAe,GAAG,SAAAA,CAAA,EAAW;EAC/B,IAAIrB,WAAW,EAAE;IACf;EACF;EACAA,WAAW,GAAG,IAAI;EAClBM,MAAM,CAACC,OAAO,CAACY,sBAAsB,CAAC,CAAC;AACzC,CAAC;;AAED;AACA;AACA,IAAI,CAAClB,mBAAmB,EAAE;EACxBK,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,QAAQ,EAAEa,eAAe,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}