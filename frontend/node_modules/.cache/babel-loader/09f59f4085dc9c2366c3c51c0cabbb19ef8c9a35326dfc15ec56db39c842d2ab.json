{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  HtmlfileReceiver = require('./receiver/htmlfile'),\n  XHRLocalObject = require('./sender/xhr-local'),\n  AjaxBasedTransport = require('./lib/ajax-based');\nfunction HtmlFileTransport(transUrl) {\n  if (!HtmlfileReceiver.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/htmlfile', HtmlfileReceiver, XHRLocalObject);\n}\ninherits(HtmlFileTransport, AjaxBasedTransport);\nHtmlFileTransport.enabled = function (info) {\n  return HtmlfileReceiver.enabled && info.sameOrigin;\n};\nHtmlFileTransport.transportName = 'htmlfile';\nHtmlFileTransport.roundTrips = 2;\nmodule.exports = HtmlFileTransport;", "map": {"version": 3, "names": ["inherits", "require", "HtmlfileReceiver", "XHRLocalObject", "AjaxBasedTransport", "HtmlFileTransport", "transUrl", "enabled", "Error", "call", "info", "<PERSON><PERSON><PERSON><PERSON>", "transportName", "roundTrips", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/htmlfile.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , HtmlfileReceiver = require('./receiver/htmlfile')\n  , XHRLocalObject = require('./sender/xhr-local')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  ;\n\nfunction HtmlFileTransport(transUrl) {\n  if (!HtmlfileReceiver.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/htmlfile', HtmlfileReceiver, XHRLocalObject);\n}\n\ninherits(HtmlFileTransport, AjaxBasedTransport);\n\nHtmlFileTransport.enabled = function(info) {\n  return HtmlfileReceiver.enabled && info.sameOrigin;\n};\n\nHtmlFileTransport.transportName = 'htmlfile';\nHtmlFileTransport.roundTrips = 2;\n\nmodule.exports = HtmlFileTransport;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,gBAAgB,GAAGD,OAAO,CAAC,qBAAqB,CAAC;EACjDE,cAAc,GAAGF,OAAO,CAAC,oBAAoB,CAAC;EAC9CG,kBAAkB,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AAGpD,SAASI,iBAAiBA,CAACC,QAAQ,EAAE;EACnC,IAAI,CAACJ,gBAAgB,CAACK,OAAO,EAAE;IAC7B,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACAJ,kBAAkB,CAACK,IAAI,CAAC,IAAI,EAAEH,QAAQ,EAAE,WAAW,EAAEJ,gBAAgB,EAAEC,cAAc,CAAC;AACxF;AAEAH,QAAQ,CAACK,iBAAiB,EAAED,kBAAkB,CAAC;AAE/CC,iBAAiB,CAACE,OAAO,GAAG,UAASG,IAAI,EAAE;EACzC,OAAOR,gBAAgB,CAACK,OAAO,IAAIG,IAAI,CAACC,UAAU;AACpD,CAAC;AAEDN,iBAAiB,CAACO,aAAa,GAAG,UAAU;AAC5CP,iBAAiB,CAACQ,UAAU,GAAG,CAAC;AAEhCC,MAAM,CAACC,OAAO,GAAGV,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}