{"ast": null, "code": "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n    return pending;\n  };\n}", "map": {"version": 3, "names": ["debounce", "fn", "pending", "Promise", "resolve", "then", "undefined"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@popperjs/core/lib/utils/debounce.js"], "sourcesContent": ["export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAACC,EAAE,EAAE;EACnC,IAAIC,OAAO;EACX,OAAO,YAAY;IACjB,IAAI,CAACA,OAAO,EAAE;MACZA,OAAO,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;QACvCD,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;UACjCH,OAAO,GAAGI,SAAS;UACnBF,OAAO,CAACH,EAAE,CAAC,CAAC,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,OAAOC,OAAO;EAChB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}