{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport config from './config';\nimport { timeoutsShape } from './utils/PropTypes';\nimport TransitionGroupContext from './TransitionGroupContext';\nimport { forceReflow } from './utils/reflow';\nexport var UNMOUNTED = 'unmounted';\nexport var EXITED = 'exited';\nexport var ENTERING = 'entering';\nexport var ENTERED = 'entered';\nexport var EXITING = 'exiting';\n/**\n * The Transition component lets you describe a transition from one component\n * state to another _over time_ with a simple declarative API. Most commonly\n * it's used to animate the mounting and unmounting of a component, but can also\n * be used to describe in-place transition states as well.\n *\n * ---\n *\n * **Note**: `Transition` is a platform-agnostic base component. If you're using\n * transitions in CSS, you'll probably want to use\n * [`CSSTransition`](https://reactcommunity.org/react-transition-group/css-transition)\n * instead. It inherits all the features of `Transition`, but contains\n * additional features necessary to play nice with CSS transitions (hence the\n * name of the component).\n *\n * ---\n *\n * By default the `Transition` component does not alter the behavior of the\n * component it renders, it only tracks \"enter\" and \"exit\" states for the\n * components. It's up to you to give meaning and effect to those states. For\n * example we can add styles to a component when it enters or exits:\n *\n * ```jsx\n * import { Transition } from 'react-transition-group';\n *\n * const duration = 300;\n *\n * const defaultStyle = {\n *   transition: `opacity ${duration}ms ease-in-out`,\n *   opacity: 0,\n * }\n *\n * const transitionStyles = {\n *   entering: { opacity: 1 },\n *   entered:  { opacity: 1 },\n *   exiting:  { opacity: 0 },\n *   exited:  { opacity: 0 },\n * };\n *\n * const Fade = ({ in: inProp }) => (\n *   <Transition in={inProp} timeout={duration}>\n *     {state => (\n *       <div style={{\n *         ...defaultStyle,\n *         ...transitionStyles[state]\n *       }}>\n *         I'm a fade Transition!\n *       </div>\n *     )}\n *   </Transition>\n * );\n * ```\n *\n * There are 4 main states a Transition can be in:\n *  - `'entering'`\n *  - `'entered'`\n *  - `'exiting'`\n *  - `'exited'`\n *\n * Transition state is toggled via the `in` prop. When `true` the component\n * begins the \"Enter\" stage. During this stage, the component will shift from\n * its current transition state, to `'entering'` for the duration of the\n * transition and then to the `'entered'` stage once it's complete. Let's take\n * the following example (we'll use the\n * [useState](https://reactjs.org/docs/hooks-reference.html#usestate) hook):\n *\n * ```jsx\n * function App() {\n *   const [inProp, setInProp] = useState(false);\n *   return (\n *     <div>\n *       <Transition in={inProp} timeout={500}>\n *         {state => (\n *           // ...\n *         )}\n *       </Transition>\n *       <button onClick={() => setInProp(true)}>\n *         Click to Enter\n *       </button>\n *     </div>\n *   );\n * }\n * ```\n *\n * When the button is clicked the component will shift to the `'entering'` state\n * and stay there for 500ms (the value of `timeout`) before it finally switches\n * to `'entered'`.\n *\n * When `in` is `false` the same thing happens except the state moves from\n * `'exiting'` to `'exited'`.\n */\n\nvar Transition = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Transition, _React$Component);\n  function Transition(props, context) {\n    var _this;\n    _this = _React$Component.call(this, props, context) || this;\n    var parentGroup = context; // In the context of a TransitionGroup all enters are really appears\n\n    var appear = parentGroup && !parentGroup.isMounting ? props.enter : props.appear;\n    var initialStatus;\n    _this.appearStatus = null;\n    if (props.in) {\n      if (appear) {\n        initialStatus = EXITED;\n        _this.appearStatus = ENTERING;\n      } else {\n        initialStatus = ENTERED;\n      }\n    } else {\n      if (props.unmountOnExit || props.mountOnEnter) {\n        initialStatus = UNMOUNTED;\n      } else {\n        initialStatus = EXITED;\n      }\n    }\n    _this.state = {\n      status: initialStatus\n    };\n    _this.nextCallback = null;\n    return _this;\n  }\n  Transition.getDerivedStateFromProps = function getDerivedStateFromProps(_ref, prevState) {\n    var nextIn = _ref.in;\n    if (nextIn && prevState.status === UNMOUNTED) {\n      return {\n        status: EXITED\n      };\n    }\n    return null;\n  } // getSnapshotBeforeUpdate(prevProps) {\n  //   let nextStatus = null\n  //   if (prevProps !== this.props) {\n  //     const { status } = this.state\n  //     if (this.props.in) {\n  //       if (status !== ENTERING && status !== ENTERED) {\n  //         nextStatus = ENTERING\n  //       }\n  //     } else {\n  //       if (status === ENTERING || status === ENTERED) {\n  //         nextStatus = EXITING\n  //       }\n  //     }\n  //   }\n  //   return { nextStatus }\n  // }\n  ;\n  var _proto = Transition.prototype;\n  _proto.componentDidMount = function componentDidMount() {\n    this.updateStatus(true, this.appearStatus);\n  };\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    var nextStatus = null;\n    if (prevProps !== this.props) {\n      var status = this.state.status;\n      if (this.props.in) {\n        if (status !== ENTERING && status !== ENTERED) {\n          nextStatus = ENTERING;\n        }\n      } else {\n        if (status === ENTERING || status === ENTERED) {\n          nextStatus = EXITING;\n        }\n      }\n    }\n    this.updateStatus(false, nextStatus);\n  };\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.cancelNextCallback();\n  };\n  _proto.getTimeouts = function getTimeouts() {\n    var timeout = this.props.timeout;\n    var exit, enter, appear;\n    exit = enter = appear = timeout;\n    if (timeout != null && typeof timeout !== 'number') {\n      exit = timeout.exit;\n      enter = timeout.enter; // TODO: remove fallback for next major\n\n      appear = timeout.appear !== undefined ? timeout.appear : enter;\n    }\n    return {\n      exit: exit,\n      enter: enter,\n      appear: appear\n    };\n  };\n  _proto.updateStatus = function updateStatus(mounting, nextStatus) {\n    if (mounting === void 0) {\n      mounting = false;\n    }\n    if (nextStatus !== null) {\n      // nextStatus will always be ENTERING or EXITING.\n      this.cancelNextCallback();\n      if (nextStatus === ENTERING) {\n        if (this.props.unmountOnExit || this.props.mountOnEnter) {\n          var node = this.props.nodeRef ? this.props.nodeRef.current : ReactDOM.findDOMNode(this); // https://github.com/reactjs/react-transition-group/pull/749\n          // With unmountOnExit or mountOnEnter, the enter animation should happen at the transition between `exited` and `entering`.\n          // To make the animation happen,  we have to separate each rendering and avoid being processed as batched.\n\n          if (node) forceReflow(node);\n        }\n        this.performEnter(mounting);\n      } else {\n        this.performExit();\n      }\n    } else if (this.props.unmountOnExit && this.state.status === EXITED) {\n      this.setState({\n        status: UNMOUNTED\n      });\n    }\n  };\n  _proto.performEnter = function performEnter(mounting) {\n    var _this2 = this;\n    var enter = this.props.enter;\n    var appearing = this.context ? this.context.isMounting : mounting;\n    var _ref2 = this.props.nodeRef ? [appearing] : [ReactDOM.findDOMNode(this), appearing],\n      maybeNode = _ref2[0],\n      maybeAppearing = _ref2[1];\n    var timeouts = this.getTimeouts();\n    var enterTimeout = appearing ? timeouts.appear : timeouts.enter; // no enter animation skip right to ENTERED\n    // if we are mounting and running this it means appear _must_ be set\n\n    if (!mounting && !enter || config.disabled) {\n      this.safeSetState({\n        status: ENTERED\n      }, function () {\n        _this2.props.onEntered(maybeNode);\n      });\n      return;\n    }\n    this.props.onEnter(maybeNode, maybeAppearing);\n    this.safeSetState({\n      status: ENTERING\n    }, function () {\n      _this2.props.onEntering(maybeNode, maybeAppearing);\n      _this2.onTransitionEnd(enterTimeout, function () {\n        _this2.safeSetState({\n          status: ENTERED\n        }, function () {\n          _this2.props.onEntered(maybeNode, maybeAppearing);\n        });\n      });\n    });\n  };\n  _proto.performExit = function performExit() {\n    var _this3 = this;\n    var exit = this.props.exit;\n    var timeouts = this.getTimeouts();\n    var maybeNode = this.props.nodeRef ? undefined : ReactDOM.findDOMNode(this); // no exit animation skip right to EXITED\n\n    if (!exit || config.disabled) {\n      this.safeSetState({\n        status: EXITED\n      }, function () {\n        _this3.props.onExited(maybeNode);\n      });\n      return;\n    }\n    this.props.onExit(maybeNode);\n    this.safeSetState({\n      status: EXITING\n    }, function () {\n      _this3.props.onExiting(maybeNode);\n      _this3.onTransitionEnd(timeouts.exit, function () {\n        _this3.safeSetState({\n          status: EXITED\n        }, function () {\n          _this3.props.onExited(maybeNode);\n        });\n      });\n    });\n  };\n  _proto.cancelNextCallback = function cancelNextCallback() {\n    if (this.nextCallback !== null) {\n      this.nextCallback.cancel();\n      this.nextCallback = null;\n    }\n  };\n  _proto.safeSetState = function safeSetState(nextState, callback) {\n    // This shouldn't be necessary, but there are weird race conditions with\n    // setState callbacks and unmounting in testing, so always make sure that\n    // we can cancel any pending setState callbacks after we unmount.\n    callback = this.setNextCallback(callback);\n    this.setState(nextState, callback);\n  };\n  _proto.setNextCallback = function setNextCallback(callback) {\n    var _this4 = this;\n    var active = true;\n    this.nextCallback = function (event) {\n      if (active) {\n        active = false;\n        _this4.nextCallback = null;\n        callback(event);\n      }\n    };\n    this.nextCallback.cancel = function () {\n      active = false;\n    };\n    return this.nextCallback;\n  };\n  _proto.onTransitionEnd = function onTransitionEnd(timeout, handler) {\n    this.setNextCallback(handler);\n    var node = this.props.nodeRef ? this.props.nodeRef.current : ReactDOM.findDOMNode(this);\n    var doesNotHaveTimeoutOrListener = timeout == null && !this.props.addEndListener;\n    if (!node || doesNotHaveTimeoutOrListener) {\n      setTimeout(this.nextCallback, 0);\n      return;\n    }\n    if (this.props.addEndListener) {\n      var _ref3 = this.props.nodeRef ? [this.nextCallback] : [node, this.nextCallback],\n        maybeNode = _ref3[0],\n        maybeNextCallback = _ref3[1];\n      this.props.addEndListener(maybeNode, maybeNextCallback);\n    }\n    if (timeout != null) {\n      setTimeout(this.nextCallback, timeout);\n    }\n  };\n  _proto.render = function render() {\n    var status = this.state.status;\n    if (status === UNMOUNTED) {\n      return null;\n    }\n    var _this$props = this.props,\n      children = _this$props.children,\n      _in = _this$props.in,\n      _mountOnEnter = _this$props.mountOnEnter,\n      _unmountOnExit = _this$props.unmountOnExit,\n      _appear = _this$props.appear,\n      _enter = _this$props.enter,\n      _exit = _this$props.exit,\n      _timeout = _this$props.timeout,\n      _addEndListener = _this$props.addEndListener,\n      _onEnter = _this$props.onEnter,\n      _onEntering = _this$props.onEntering,\n      _onEntered = _this$props.onEntered,\n      _onExit = _this$props.onExit,\n      _onExiting = _this$props.onExiting,\n      _onExited = _this$props.onExited,\n      _nodeRef = _this$props.nodeRef,\n      childProps = _objectWithoutPropertiesLoose(_this$props, [\"children\", \"in\", \"mountOnEnter\", \"unmountOnExit\", \"appear\", \"enter\", \"exit\", \"timeout\", \"addEndListener\", \"onEnter\", \"onEntering\", \"onEntered\", \"onExit\", \"onExiting\", \"onExited\", \"nodeRef\"]);\n    return (/*#__PURE__*/\n      // allows for nested Transitions\n      React.createElement(TransitionGroupContext.Provider, {\n        value: null\n      }, typeof children === 'function' ? children(status, childProps) : React.cloneElement(React.Children.only(children), childProps))\n    );\n  };\n  return Transition;\n}(React.Component);\nTransition.contextType = TransitionGroupContext;\nTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * A React reference to DOM element that need to transition:\n   * https://stackoverflow.com/a/51127130/4671932\n   *\n   *   - When `nodeRef` prop is used, `node` is not passed to callback functions\n   *      (e.g. `onEnter`) because user already has direct access to the node.\n   *   - When changing `key` prop of `Transition` in a `TransitionGroup` a new\n   *     `nodeRef` need to be provided to `Transition` with changed `key` prop\n   *     (see\n   *     [test/CSSTransition-test.js](https://github.com/reactjs/react-transition-group/blob/13435f897b3ab71f6e19d724f145596f5910581c/test/CSSTransition-test.js#L362-L437)).\n   */\n  nodeRef: PropTypes.shape({\n    current: typeof Element === 'undefined' ? PropTypes.any : function (propValue, key, componentName, location, propFullName, secret) {\n      var value = propValue[key];\n      return PropTypes.instanceOf(value && 'ownerDocument' in value ? value.ownerDocument.defaultView.Element : Element)(propValue, key, componentName, location, propFullName, secret);\n    }\n  }),\n  /**\n   * A `function` child can be used instead of a React element. This function is\n   * called with the current transition status (`'entering'`, `'entered'`,\n   * `'exiting'`, `'exited'`), which can be used to apply context\n   * specific props to a component.\n   *\n   * ```jsx\n   * <Transition in={this.state.in} timeout={150}>\n   *   {state => (\n   *     <MyComponent className={`fade fade-${state}`} />\n   *   )}\n   * </Transition>\n   * ```\n   */\n  children: PropTypes.oneOfType([PropTypes.func.isRequired, PropTypes.element.isRequired]).isRequired,\n  /**\n   * Show the component; triggers the enter or exit states\n   */\n  in: PropTypes.bool,\n  /**\n   * By default the child component is mounted immediately along with\n   * the parent `Transition` component. If you want to \"lazy mount\" the component on the\n   * first `in={true}` you can set `mountOnEnter`. After the first enter transition the component will stay\n   * mounted, even on \"exited\", unless you also specify `unmountOnExit`.\n   */\n  mountOnEnter: PropTypes.bool,\n  /**\n   * By default the child component stays mounted after it reaches the `'exited'` state.\n   * Set `unmountOnExit` if you'd prefer to unmount the component after it finishes exiting.\n   */\n  unmountOnExit: PropTypes.bool,\n  /**\n   * By default the child component does not perform the enter transition when\n   * it first mounts, regardless of the value of `in`. If you want this\n   * behavior, set both `appear` and `in` to `true`.\n   *\n   * > **Note**: there are no special appear states like `appearing`/`appeared`, this prop\n   * > only adds an additional enter transition. However, in the\n   * > `<CSSTransition>` component that first enter transition does result in\n   * > additional `.appear-*` classes, that way you can choose to style it\n   * > differently.\n   */\n  appear: PropTypes.bool,\n  /**\n   * Enable or disable enter transitions.\n   */\n  enter: PropTypes.bool,\n  /**\n   * Enable or disable exit transitions.\n   */\n  exit: PropTypes.bool,\n  /**\n   * The duration of the transition, in milliseconds.\n   * Required unless `addEndListener` is provided.\n   *\n   * You may specify a single timeout for all transitions:\n   *\n   * ```jsx\n   * timeout={500}\n   * ```\n   *\n   * or individually:\n   *\n   * ```jsx\n   * timeout={{\n   *  appear: 500,\n   *  enter: 300,\n   *  exit: 500,\n   * }}\n   * ```\n   *\n   * - `appear` defaults to the value of `enter`\n   * - `enter` defaults to `0`\n   * - `exit` defaults to `0`\n   *\n   * @type {number | { enter?: number, exit?: number, appear?: number }}\n   */\n  timeout: function timeout(props) {\n    var pt = timeoutsShape;\n    if (!props.addEndListener) pt = pt.isRequired;\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return pt.apply(void 0, [props].concat(args));\n  },\n  /**\n   * Add a custom transition end trigger. Called with the transitioning\n   * DOM node and a `done` callback. Allows for more fine grained transition end\n   * logic. Timeouts are still used as a fallback if provided.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * ```jsx\n   * addEndListener={(node, done) => {\n   *   // use the css transitionend event to mark the finish of a transition\n   *   node.addEventListener('transitionend', done, false);\n   * }}\n   * ```\n   */\n  addEndListener: PropTypes.func,\n  /**\n   * Callback fired before the \"entering\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\n   */\n  onEnter: PropTypes.func,\n  /**\n   * Callback fired after the \"entering\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntering: PropTypes.func,\n  /**\n   * Callback fired after the \"entered\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\n   */\n  onEntered: PropTypes.func,\n  /**\n   * Callback fired before the \"exiting\" status is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExit: PropTypes.func,\n  /**\n   * Callback fired after the \"exiting\" status is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExiting: PropTypes.func,\n  /**\n   * Callback fired after the \"exited\" status is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExited: PropTypes.func\n} : {}; // Name the function so it is clearer in the documentation\n\nfunction noop() {}\nTransition.defaultProps = {\n  in: false,\n  mountOnEnter: false,\n  unmountOnExit: false,\n  appear: false,\n  enter: true,\n  exit: true,\n  onEnter: noop,\n  onEntering: noop,\n  onEntered: noop,\n  onExit: noop,\n  onExiting: noop,\n  onExited: noop\n};\nTransition.UNMOUNTED = UNMOUNTED;\nTransition.EXITED = EXITED;\nTransition.ENTERING = ENTERING;\nTransition.ENTERED = ENTERED;\nTransition.EXITING = EXITING;\nexport default Transition;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_inherits<PERSON><PERSON>e", "PropTypes", "React", "ReactDOM", "config", "timeoutsShape", "TransitionGroupContext", "forceReflow", "UNMOUNTED", "EXITED", "ENTERING", "ENTERED", "EXITING", "Transition", "_React$Component", "props", "context", "_this", "call", "parentGroup", "appear", "isMounting", "enter", "initialStatus", "appearStatus", "in", "unmountOnExit", "mountOnEnter", "state", "status", "nextCallback", "getDerivedStateFromProps", "_ref", "prevState", "nextIn", "_proto", "prototype", "componentDidMount", "updateStatus", "componentDidUpdate", "prevProps", "nextStatus", "componentWillUnmount", "cancelNextCallback", "getTimeouts", "timeout", "exit", "undefined", "mounting", "node", "nodeRef", "current", "findDOMNode", "performEnter", "performExit", "setState", "_this2", "appearing", "_ref2", "maybeNode", "maybeAppearing", "timeouts", "enterTimeout", "disabled", "safeSetState", "onEntered", "onEnter", "onEntering", "onTransitionEnd", "_this3", "onExited", "onExit", "onExiting", "cancel", "nextState", "callback", "setNextCallback", "_this4", "active", "event", "handler", "doesNotHaveTimeoutOrListener", "addEndListener", "setTimeout", "_ref3", "maybeNextCallback", "render", "_this$props", "children", "_in", "_mountOnEnter", "_unmountOnExit", "_appear", "_enter", "_exit", "_timeout", "_addEndListener", "_onEnter", "_onEntering", "_onEntered", "_onExit", "_onExiting", "_onExited", "_nodeRef", "childProps", "createElement", "Provider", "value", "cloneElement", "Children", "only", "Component", "contextType", "propTypes", "process", "env", "NODE_ENV", "shape", "Element", "any", "propValue", "key", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "secret", "instanceOf", "ownerDocument", "defaultView", "oneOfType", "func", "isRequired", "element", "bool", "pt", "_len", "arguments", "length", "args", "Array", "_key", "apply", "concat", "noop", "defaultProps"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/react-transition-group/esm/Transition.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport config from './config';\nimport { timeoutsShape } from './utils/PropTypes';\nimport TransitionGroupContext from './TransitionGroupContext';\nimport { forceReflow } from './utils/reflow';\nexport var UNMOUNTED = 'unmounted';\nexport var EXITED = 'exited';\nexport var ENTERING = 'entering';\nexport var ENTERED = 'entered';\nexport var EXITING = 'exiting';\n/**\n * The Transition component lets you describe a transition from one component\n * state to another _over time_ with a simple declarative API. Most commonly\n * it's used to animate the mounting and unmounting of a component, but can also\n * be used to describe in-place transition states as well.\n *\n * ---\n *\n * **Note**: `Transition` is a platform-agnostic base component. If you're using\n * transitions in CSS, you'll probably want to use\n * [`CSSTransition`](https://reactcommunity.org/react-transition-group/css-transition)\n * instead. It inherits all the features of `Transition`, but contains\n * additional features necessary to play nice with CSS transitions (hence the\n * name of the component).\n *\n * ---\n *\n * By default the `Transition` component does not alter the behavior of the\n * component it renders, it only tracks \"enter\" and \"exit\" states for the\n * components. It's up to you to give meaning and effect to those states. For\n * example we can add styles to a component when it enters or exits:\n *\n * ```jsx\n * import { Transition } from 'react-transition-group';\n *\n * const duration = 300;\n *\n * const defaultStyle = {\n *   transition: `opacity ${duration}ms ease-in-out`,\n *   opacity: 0,\n * }\n *\n * const transitionStyles = {\n *   entering: { opacity: 1 },\n *   entered:  { opacity: 1 },\n *   exiting:  { opacity: 0 },\n *   exited:  { opacity: 0 },\n * };\n *\n * const Fade = ({ in: inProp }) => (\n *   <Transition in={inProp} timeout={duration}>\n *     {state => (\n *       <div style={{\n *         ...defaultStyle,\n *         ...transitionStyles[state]\n *       }}>\n *         I'm a fade Transition!\n *       </div>\n *     )}\n *   </Transition>\n * );\n * ```\n *\n * There are 4 main states a Transition can be in:\n *  - `'entering'`\n *  - `'entered'`\n *  - `'exiting'`\n *  - `'exited'`\n *\n * Transition state is toggled via the `in` prop. When `true` the component\n * begins the \"Enter\" stage. During this stage, the component will shift from\n * its current transition state, to `'entering'` for the duration of the\n * transition and then to the `'entered'` stage once it's complete. Let's take\n * the following example (we'll use the\n * [useState](https://reactjs.org/docs/hooks-reference.html#usestate) hook):\n *\n * ```jsx\n * function App() {\n *   const [inProp, setInProp] = useState(false);\n *   return (\n *     <div>\n *       <Transition in={inProp} timeout={500}>\n *         {state => (\n *           // ...\n *         )}\n *       </Transition>\n *       <button onClick={() => setInProp(true)}>\n *         Click to Enter\n *       </button>\n *     </div>\n *   );\n * }\n * ```\n *\n * When the button is clicked the component will shift to the `'entering'` state\n * and stay there for 500ms (the value of `timeout`) before it finally switches\n * to `'entered'`.\n *\n * When `in` is `false` the same thing happens except the state moves from\n * `'exiting'` to `'exited'`.\n */\n\nvar Transition = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Transition, _React$Component);\n\n  function Transition(props, context) {\n    var _this;\n\n    _this = _React$Component.call(this, props, context) || this;\n    var parentGroup = context; // In the context of a TransitionGroup all enters are really appears\n\n    var appear = parentGroup && !parentGroup.isMounting ? props.enter : props.appear;\n    var initialStatus;\n    _this.appearStatus = null;\n\n    if (props.in) {\n      if (appear) {\n        initialStatus = EXITED;\n        _this.appearStatus = ENTERING;\n      } else {\n        initialStatus = ENTERED;\n      }\n    } else {\n      if (props.unmountOnExit || props.mountOnEnter) {\n        initialStatus = UNMOUNTED;\n      } else {\n        initialStatus = EXITED;\n      }\n    }\n\n    _this.state = {\n      status: initialStatus\n    };\n    _this.nextCallback = null;\n    return _this;\n  }\n\n  Transition.getDerivedStateFromProps = function getDerivedStateFromProps(_ref, prevState) {\n    var nextIn = _ref.in;\n\n    if (nextIn && prevState.status === UNMOUNTED) {\n      return {\n        status: EXITED\n      };\n    }\n\n    return null;\n  } // getSnapshotBeforeUpdate(prevProps) {\n  //   let nextStatus = null\n  //   if (prevProps !== this.props) {\n  //     const { status } = this.state\n  //     if (this.props.in) {\n  //       if (status !== ENTERING && status !== ENTERED) {\n  //         nextStatus = ENTERING\n  //       }\n  //     } else {\n  //       if (status === ENTERING || status === ENTERED) {\n  //         nextStatus = EXITING\n  //       }\n  //     }\n  //   }\n  //   return { nextStatus }\n  // }\n  ;\n\n  var _proto = Transition.prototype;\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.updateStatus(true, this.appearStatus);\n  };\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    var nextStatus = null;\n\n    if (prevProps !== this.props) {\n      var status = this.state.status;\n\n      if (this.props.in) {\n        if (status !== ENTERING && status !== ENTERED) {\n          nextStatus = ENTERING;\n        }\n      } else {\n        if (status === ENTERING || status === ENTERED) {\n          nextStatus = EXITING;\n        }\n      }\n    }\n\n    this.updateStatus(false, nextStatus);\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.cancelNextCallback();\n  };\n\n  _proto.getTimeouts = function getTimeouts() {\n    var timeout = this.props.timeout;\n    var exit, enter, appear;\n    exit = enter = appear = timeout;\n\n    if (timeout != null && typeof timeout !== 'number') {\n      exit = timeout.exit;\n      enter = timeout.enter; // TODO: remove fallback for next major\n\n      appear = timeout.appear !== undefined ? timeout.appear : enter;\n    }\n\n    return {\n      exit: exit,\n      enter: enter,\n      appear: appear\n    };\n  };\n\n  _proto.updateStatus = function updateStatus(mounting, nextStatus) {\n    if (mounting === void 0) {\n      mounting = false;\n    }\n\n    if (nextStatus !== null) {\n      // nextStatus will always be ENTERING or EXITING.\n      this.cancelNextCallback();\n\n      if (nextStatus === ENTERING) {\n        if (this.props.unmountOnExit || this.props.mountOnEnter) {\n          var node = this.props.nodeRef ? this.props.nodeRef.current : ReactDOM.findDOMNode(this); // https://github.com/reactjs/react-transition-group/pull/749\n          // With unmountOnExit or mountOnEnter, the enter animation should happen at the transition between `exited` and `entering`.\n          // To make the animation happen,  we have to separate each rendering and avoid being processed as batched.\n\n          if (node) forceReflow(node);\n        }\n\n        this.performEnter(mounting);\n      } else {\n        this.performExit();\n      }\n    } else if (this.props.unmountOnExit && this.state.status === EXITED) {\n      this.setState({\n        status: UNMOUNTED\n      });\n    }\n  };\n\n  _proto.performEnter = function performEnter(mounting) {\n    var _this2 = this;\n\n    var enter = this.props.enter;\n    var appearing = this.context ? this.context.isMounting : mounting;\n\n    var _ref2 = this.props.nodeRef ? [appearing] : [ReactDOM.findDOMNode(this), appearing],\n        maybeNode = _ref2[0],\n        maybeAppearing = _ref2[1];\n\n    var timeouts = this.getTimeouts();\n    var enterTimeout = appearing ? timeouts.appear : timeouts.enter; // no enter animation skip right to ENTERED\n    // if we are mounting and running this it means appear _must_ be set\n\n    if (!mounting && !enter || config.disabled) {\n      this.safeSetState({\n        status: ENTERED\n      }, function () {\n        _this2.props.onEntered(maybeNode);\n      });\n      return;\n    }\n\n    this.props.onEnter(maybeNode, maybeAppearing);\n    this.safeSetState({\n      status: ENTERING\n    }, function () {\n      _this2.props.onEntering(maybeNode, maybeAppearing);\n\n      _this2.onTransitionEnd(enterTimeout, function () {\n        _this2.safeSetState({\n          status: ENTERED\n        }, function () {\n          _this2.props.onEntered(maybeNode, maybeAppearing);\n        });\n      });\n    });\n  };\n\n  _proto.performExit = function performExit() {\n    var _this3 = this;\n\n    var exit = this.props.exit;\n    var timeouts = this.getTimeouts();\n    var maybeNode = this.props.nodeRef ? undefined : ReactDOM.findDOMNode(this); // no exit animation skip right to EXITED\n\n    if (!exit || config.disabled) {\n      this.safeSetState({\n        status: EXITED\n      }, function () {\n        _this3.props.onExited(maybeNode);\n      });\n      return;\n    }\n\n    this.props.onExit(maybeNode);\n    this.safeSetState({\n      status: EXITING\n    }, function () {\n      _this3.props.onExiting(maybeNode);\n\n      _this3.onTransitionEnd(timeouts.exit, function () {\n        _this3.safeSetState({\n          status: EXITED\n        }, function () {\n          _this3.props.onExited(maybeNode);\n        });\n      });\n    });\n  };\n\n  _proto.cancelNextCallback = function cancelNextCallback() {\n    if (this.nextCallback !== null) {\n      this.nextCallback.cancel();\n      this.nextCallback = null;\n    }\n  };\n\n  _proto.safeSetState = function safeSetState(nextState, callback) {\n    // This shouldn't be necessary, but there are weird race conditions with\n    // setState callbacks and unmounting in testing, so always make sure that\n    // we can cancel any pending setState callbacks after we unmount.\n    callback = this.setNextCallback(callback);\n    this.setState(nextState, callback);\n  };\n\n  _proto.setNextCallback = function setNextCallback(callback) {\n    var _this4 = this;\n\n    var active = true;\n\n    this.nextCallback = function (event) {\n      if (active) {\n        active = false;\n        _this4.nextCallback = null;\n        callback(event);\n      }\n    };\n\n    this.nextCallback.cancel = function () {\n      active = false;\n    };\n\n    return this.nextCallback;\n  };\n\n  _proto.onTransitionEnd = function onTransitionEnd(timeout, handler) {\n    this.setNextCallback(handler);\n    var node = this.props.nodeRef ? this.props.nodeRef.current : ReactDOM.findDOMNode(this);\n    var doesNotHaveTimeoutOrListener = timeout == null && !this.props.addEndListener;\n\n    if (!node || doesNotHaveTimeoutOrListener) {\n      setTimeout(this.nextCallback, 0);\n      return;\n    }\n\n    if (this.props.addEndListener) {\n      var _ref3 = this.props.nodeRef ? [this.nextCallback] : [node, this.nextCallback],\n          maybeNode = _ref3[0],\n          maybeNextCallback = _ref3[1];\n\n      this.props.addEndListener(maybeNode, maybeNextCallback);\n    }\n\n    if (timeout != null) {\n      setTimeout(this.nextCallback, timeout);\n    }\n  };\n\n  _proto.render = function render() {\n    var status = this.state.status;\n\n    if (status === UNMOUNTED) {\n      return null;\n    }\n\n    var _this$props = this.props,\n        children = _this$props.children,\n        _in = _this$props.in,\n        _mountOnEnter = _this$props.mountOnEnter,\n        _unmountOnExit = _this$props.unmountOnExit,\n        _appear = _this$props.appear,\n        _enter = _this$props.enter,\n        _exit = _this$props.exit,\n        _timeout = _this$props.timeout,\n        _addEndListener = _this$props.addEndListener,\n        _onEnter = _this$props.onEnter,\n        _onEntering = _this$props.onEntering,\n        _onEntered = _this$props.onEntered,\n        _onExit = _this$props.onExit,\n        _onExiting = _this$props.onExiting,\n        _onExited = _this$props.onExited,\n        _nodeRef = _this$props.nodeRef,\n        childProps = _objectWithoutPropertiesLoose(_this$props, [\"children\", \"in\", \"mountOnEnter\", \"unmountOnExit\", \"appear\", \"enter\", \"exit\", \"timeout\", \"addEndListener\", \"onEnter\", \"onEntering\", \"onEntered\", \"onExit\", \"onExiting\", \"onExited\", \"nodeRef\"]);\n\n    return (\n      /*#__PURE__*/\n      // allows for nested Transitions\n      React.createElement(TransitionGroupContext.Provider, {\n        value: null\n      }, typeof children === 'function' ? children(status, childProps) : React.cloneElement(React.Children.only(children), childProps))\n    );\n  };\n\n  return Transition;\n}(React.Component);\n\nTransition.contextType = TransitionGroupContext;\nTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * A React reference to DOM element that need to transition:\n   * https://stackoverflow.com/a/51127130/4671932\n   *\n   *   - When `nodeRef` prop is used, `node` is not passed to callback functions\n   *      (e.g. `onEnter`) because user already has direct access to the node.\n   *   - When changing `key` prop of `Transition` in a `TransitionGroup` a new\n   *     `nodeRef` need to be provided to `Transition` with changed `key` prop\n   *     (see\n   *     [test/CSSTransition-test.js](https://github.com/reactjs/react-transition-group/blob/13435f897b3ab71f6e19d724f145596f5910581c/test/CSSTransition-test.js#L362-L437)).\n   */\n  nodeRef: PropTypes.shape({\n    current: typeof Element === 'undefined' ? PropTypes.any : function (propValue, key, componentName, location, propFullName, secret) {\n      var value = propValue[key];\n      return PropTypes.instanceOf(value && 'ownerDocument' in value ? value.ownerDocument.defaultView.Element : Element)(propValue, key, componentName, location, propFullName, secret);\n    }\n  }),\n\n  /**\n   * A `function` child can be used instead of a React element. This function is\n   * called with the current transition status (`'entering'`, `'entered'`,\n   * `'exiting'`, `'exited'`), which can be used to apply context\n   * specific props to a component.\n   *\n   * ```jsx\n   * <Transition in={this.state.in} timeout={150}>\n   *   {state => (\n   *     <MyComponent className={`fade fade-${state}`} />\n   *   )}\n   * </Transition>\n   * ```\n   */\n  children: PropTypes.oneOfType([PropTypes.func.isRequired, PropTypes.element.isRequired]).isRequired,\n\n  /**\n   * Show the component; triggers the enter or exit states\n   */\n  in: PropTypes.bool,\n\n  /**\n   * By default the child component is mounted immediately along with\n   * the parent `Transition` component. If you want to \"lazy mount\" the component on the\n   * first `in={true}` you can set `mountOnEnter`. After the first enter transition the component will stay\n   * mounted, even on \"exited\", unless you also specify `unmountOnExit`.\n   */\n  mountOnEnter: PropTypes.bool,\n\n  /**\n   * By default the child component stays mounted after it reaches the `'exited'` state.\n   * Set `unmountOnExit` if you'd prefer to unmount the component after it finishes exiting.\n   */\n  unmountOnExit: PropTypes.bool,\n\n  /**\n   * By default the child component does not perform the enter transition when\n   * it first mounts, regardless of the value of `in`. If you want this\n   * behavior, set both `appear` and `in` to `true`.\n   *\n   * > **Note**: there are no special appear states like `appearing`/`appeared`, this prop\n   * > only adds an additional enter transition. However, in the\n   * > `<CSSTransition>` component that first enter transition does result in\n   * > additional `.appear-*` classes, that way you can choose to style it\n   * > differently.\n   */\n  appear: PropTypes.bool,\n\n  /**\n   * Enable or disable enter transitions.\n   */\n  enter: PropTypes.bool,\n\n  /**\n   * Enable or disable exit transitions.\n   */\n  exit: PropTypes.bool,\n\n  /**\n   * The duration of the transition, in milliseconds.\n   * Required unless `addEndListener` is provided.\n   *\n   * You may specify a single timeout for all transitions:\n   *\n   * ```jsx\n   * timeout={500}\n   * ```\n   *\n   * or individually:\n   *\n   * ```jsx\n   * timeout={{\n   *  appear: 500,\n   *  enter: 300,\n   *  exit: 500,\n   * }}\n   * ```\n   *\n   * - `appear` defaults to the value of `enter`\n   * - `enter` defaults to `0`\n   * - `exit` defaults to `0`\n   *\n   * @type {number | { enter?: number, exit?: number, appear?: number }}\n   */\n  timeout: function timeout(props) {\n    var pt = timeoutsShape;\n    if (!props.addEndListener) pt = pt.isRequired;\n\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return pt.apply(void 0, [props].concat(args));\n  },\n\n  /**\n   * Add a custom transition end trigger. Called with the transitioning\n   * DOM node and a `done` callback. Allows for more fine grained transition end\n   * logic. Timeouts are still used as a fallback if provided.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * ```jsx\n   * addEndListener={(node, done) => {\n   *   // use the css transitionend event to mark the finish of a transition\n   *   node.addEventListener('transitionend', done, false);\n   * }}\n   * ```\n   */\n  addEndListener: PropTypes.func,\n\n  /**\n   * Callback fired before the \"entering\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\n   */\n  onEnter: PropTypes.func,\n\n  /**\n   * Callback fired after the \"entering\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntering: PropTypes.func,\n\n  /**\n   * Callback fired after the \"entered\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\n   */\n  onEntered: PropTypes.func,\n\n  /**\n   * Callback fired before the \"exiting\" status is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExit: PropTypes.func,\n\n  /**\n   * Callback fired after the \"exiting\" status is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExiting: PropTypes.func,\n\n  /**\n   * Callback fired after the \"exited\" status is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExited: PropTypes.func\n} : {}; // Name the function so it is clearer in the documentation\n\nfunction noop() {}\n\nTransition.defaultProps = {\n  in: false,\n  mountOnEnter: false,\n  unmountOnExit: false,\n  appear: false,\n  enter: true,\n  exit: true,\n  onEnter: noop,\n  onEntering: noop,\n  onEntered: noop,\n  onExit: noop,\n  onExiting: noop,\n  onExited: noop\n};\nTransition.UNMOUNTED = UNMOUNTED;\nTransition.EXITED = EXITED;\nTransition.ENTERING = ENTERING;\nTransition.ENTERED = ENTERED;\nTransition.EXITING = EXITING;\nexport default Transition;"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,aAAa,QAAQ,mBAAmB;AACjD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAO,IAAIC,SAAS,GAAG,WAAW;AAClC,OAAO,IAAIC,MAAM,GAAG,QAAQ;AAC5B,OAAO,IAAIC,QAAQ,GAAG,UAAU;AAChC,OAAO,IAAIC,OAAO,GAAG,SAAS;AAC9B,OAAO,IAAIC,OAAO,GAAG,SAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,UAAU,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACxDd,cAAc,CAACa,UAAU,EAAEC,gBAAgB,CAAC;EAE5C,SAASD,UAAUA,CAACE,KAAK,EAAEC,OAAO,EAAE;IAClC,IAAIC,KAAK;IAETA,KAAK,GAAGH,gBAAgB,CAACI,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,IAAI,IAAI;IAC3D,IAAIG,WAAW,GAAGH,OAAO,CAAC,CAAC;;IAE3B,IAAII,MAAM,GAAGD,WAAW,IAAI,CAACA,WAAW,CAACE,UAAU,GAAGN,KAAK,CAACO,KAAK,GAAGP,KAAK,CAACK,MAAM;IAChF,IAAIG,aAAa;IACjBN,KAAK,CAACO,YAAY,GAAG,IAAI;IAEzB,IAAIT,KAAK,CAACU,EAAE,EAAE;MACZ,IAAIL,MAAM,EAAE;QACVG,aAAa,GAAGd,MAAM;QACtBQ,KAAK,CAACO,YAAY,GAAGd,QAAQ;MAC/B,CAAC,MAAM;QACLa,aAAa,GAAGZ,OAAO;MACzB;IACF,CAAC,MAAM;MACL,IAAII,KAAK,CAACW,aAAa,IAAIX,KAAK,CAACY,YAAY,EAAE;QAC7CJ,aAAa,GAAGf,SAAS;MAC3B,CAAC,MAAM;QACLe,aAAa,GAAGd,MAAM;MACxB;IACF;IAEAQ,KAAK,CAACW,KAAK,GAAG;MACZC,MAAM,EAAEN;IACV,CAAC;IACDN,KAAK,CAACa,YAAY,GAAG,IAAI;IACzB,OAAOb,KAAK;EACd;EAEAJ,UAAU,CAACkB,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,IAAI,EAAEC,SAAS,EAAE;IACvF,IAAIC,MAAM,GAAGF,IAAI,CAACP,EAAE;IAEpB,IAAIS,MAAM,IAAID,SAAS,CAACJ,MAAM,KAAKrB,SAAS,EAAE;MAC5C,OAAO;QACLqB,MAAM,EAAEpB;MACV,CAAC;IACH;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;EAGA,IAAI0B,MAAM,GAAGtB,UAAU,CAACuB,SAAS;EAEjCD,MAAM,CAACE,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACtD,IAAI,CAACC,YAAY,CAAC,IAAI,EAAE,IAAI,CAACd,YAAY,CAAC;EAC5C,CAAC;EAEDW,MAAM,CAACI,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,SAAS,EAAE;IACjE,IAAIC,UAAU,GAAG,IAAI;IAErB,IAAID,SAAS,KAAK,IAAI,CAACzB,KAAK,EAAE;MAC5B,IAAIc,MAAM,GAAG,IAAI,CAACD,KAAK,CAACC,MAAM;MAE9B,IAAI,IAAI,CAACd,KAAK,CAACU,EAAE,EAAE;QACjB,IAAII,MAAM,KAAKnB,QAAQ,IAAImB,MAAM,KAAKlB,OAAO,EAAE;UAC7C8B,UAAU,GAAG/B,QAAQ;QACvB;MACF,CAAC,MAAM;QACL,IAAImB,MAAM,KAAKnB,QAAQ,IAAImB,MAAM,KAAKlB,OAAO,EAAE;UAC7C8B,UAAU,GAAG7B,OAAO;QACtB;MACF;IACF;IAEA,IAAI,CAAC0B,YAAY,CAAC,KAAK,EAAEG,UAAU,CAAC;EACtC,CAAC;EAEDN,MAAM,CAACO,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IAC5D,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC3B,CAAC;EAEDR,MAAM,CAACS,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IAC1C,IAAIC,OAAO,GAAG,IAAI,CAAC9B,KAAK,CAAC8B,OAAO;IAChC,IAAIC,IAAI,EAAExB,KAAK,EAAEF,MAAM;IACvB0B,IAAI,GAAGxB,KAAK,GAAGF,MAAM,GAAGyB,OAAO;IAE/B,IAAIA,OAAO,IAAI,IAAI,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAClDC,IAAI,GAAGD,OAAO,CAACC,IAAI;MACnBxB,KAAK,GAAGuB,OAAO,CAACvB,KAAK,CAAC,CAAC;;MAEvBF,MAAM,GAAGyB,OAAO,CAACzB,MAAM,KAAK2B,SAAS,GAAGF,OAAO,CAACzB,MAAM,GAAGE,KAAK;IAChE;IAEA,OAAO;MACLwB,IAAI,EAAEA,IAAI;MACVxB,KAAK,EAAEA,KAAK;MACZF,MAAM,EAAEA;IACV,CAAC;EACH,CAAC;EAEDe,MAAM,CAACG,YAAY,GAAG,SAASA,YAAYA,CAACU,QAAQ,EAAEP,UAAU,EAAE;IAChE,IAAIO,QAAQ,KAAK,KAAK,CAAC,EAAE;MACvBA,QAAQ,GAAG,KAAK;IAClB;IAEA,IAAIP,UAAU,KAAK,IAAI,EAAE;MACvB;MACA,IAAI,CAACE,kBAAkB,CAAC,CAAC;MAEzB,IAAIF,UAAU,KAAK/B,QAAQ,EAAE;QAC3B,IAAI,IAAI,CAACK,KAAK,CAACW,aAAa,IAAI,IAAI,CAACX,KAAK,CAACY,YAAY,EAAE;UACvD,IAAIsB,IAAI,GAAG,IAAI,CAAClC,KAAK,CAACmC,OAAO,GAAG,IAAI,CAACnC,KAAK,CAACmC,OAAO,CAACC,OAAO,GAAGhD,QAAQ,CAACiD,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;UACzF;UACA;;UAEA,IAAIH,IAAI,EAAE1C,WAAW,CAAC0C,IAAI,CAAC;QAC7B;QAEA,IAAI,CAACI,YAAY,CAACL,QAAQ,CAAC;MAC7B,CAAC,MAAM;QACL,IAAI,CAACM,WAAW,CAAC,CAAC;MACpB;IACF,CAAC,MAAM,IAAI,IAAI,CAACvC,KAAK,CAACW,aAAa,IAAI,IAAI,CAACE,KAAK,CAACC,MAAM,KAAKpB,MAAM,EAAE;MACnE,IAAI,CAAC8C,QAAQ,CAAC;QACZ1B,MAAM,EAAErB;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED2B,MAAM,CAACkB,YAAY,GAAG,SAASA,YAAYA,CAACL,QAAQ,EAAE;IACpD,IAAIQ,MAAM,GAAG,IAAI;IAEjB,IAAIlC,KAAK,GAAG,IAAI,CAACP,KAAK,CAACO,KAAK;IAC5B,IAAImC,SAAS,GAAG,IAAI,CAACzC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACK,UAAU,GAAG2B,QAAQ;IAEjE,IAAIU,KAAK,GAAG,IAAI,CAAC3C,KAAK,CAACmC,OAAO,GAAG,CAACO,SAAS,CAAC,GAAG,CAACtD,QAAQ,CAACiD,WAAW,CAAC,IAAI,CAAC,EAAEK,SAAS,CAAC;MAClFE,SAAS,GAAGD,KAAK,CAAC,CAAC,CAAC;MACpBE,cAAc,GAAGF,KAAK,CAAC,CAAC,CAAC;IAE7B,IAAIG,QAAQ,GAAG,IAAI,CAACjB,WAAW,CAAC,CAAC;IACjC,IAAIkB,YAAY,GAAGL,SAAS,GAAGI,QAAQ,CAACzC,MAAM,GAAGyC,QAAQ,CAACvC,KAAK,CAAC,CAAC;IACjE;;IAEA,IAAI,CAAC0B,QAAQ,IAAI,CAAC1B,KAAK,IAAIlB,MAAM,CAAC2D,QAAQ,EAAE;MAC1C,IAAI,CAACC,YAAY,CAAC;QAChBnC,MAAM,EAAElB;MACV,CAAC,EAAE,YAAY;QACb6C,MAAM,CAACzC,KAAK,CAACkD,SAAS,CAACN,SAAS,CAAC;MACnC,CAAC,CAAC;MACF;IACF;IAEA,IAAI,CAAC5C,KAAK,CAACmD,OAAO,CAACP,SAAS,EAAEC,cAAc,CAAC;IAC7C,IAAI,CAACI,YAAY,CAAC;MAChBnC,MAAM,EAAEnB;IACV,CAAC,EAAE,YAAY;MACb8C,MAAM,CAACzC,KAAK,CAACoD,UAAU,CAACR,SAAS,EAAEC,cAAc,CAAC;MAElDJ,MAAM,CAACY,eAAe,CAACN,YAAY,EAAE,YAAY;QAC/CN,MAAM,CAACQ,YAAY,CAAC;UAClBnC,MAAM,EAAElB;QACV,CAAC,EAAE,YAAY;UACb6C,MAAM,CAACzC,KAAK,CAACkD,SAAS,CAACN,SAAS,EAAEC,cAAc,CAAC;QACnD,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDzB,MAAM,CAACmB,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IAC1C,IAAIe,MAAM,GAAG,IAAI;IAEjB,IAAIvB,IAAI,GAAG,IAAI,CAAC/B,KAAK,CAAC+B,IAAI;IAC1B,IAAIe,QAAQ,GAAG,IAAI,CAACjB,WAAW,CAAC,CAAC;IACjC,IAAIe,SAAS,GAAG,IAAI,CAAC5C,KAAK,CAACmC,OAAO,GAAGH,SAAS,GAAG5C,QAAQ,CAACiD,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;;IAE7E,IAAI,CAACN,IAAI,IAAI1C,MAAM,CAAC2D,QAAQ,EAAE;MAC5B,IAAI,CAACC,YAAY,CAAC;QAChBnC,MAAM,EAAEpB;MACV,CAAC,EAAE,YAAY;QACb4D,MAAM,CAACtD,KAAK,CAACuD,QAAQ,CAACX,SAAS,CAAC;MAClC,CAAC,CAAC;MACF;IACF;IAEA,IAAI,CAAC5C,KAAK,CAACwD,MAAM,CAACZ,SAAS,CAAC;IAC5B,IAAI,CAACK,YAAY,CAAC;MAChBnC,MAAM,EAAEjB;IACV,CAAC,EAAE,YAAY;MACbyD,MAAM,CAACtD,KAAK,CAACyD,SAAS,CAACb,SAAS,CAAC;MAEjCU,MAAM,CAACD,eAAe,CAACP,QAAQ,CAACf,IAAI,EAAE,YAAY;QAChDuB,MAAM,CAACL,YAAY,CAAC;UAClBnC,MAAM,EAAEpB;QACV,CAAC,EAAE,YAAY;UACb4D,MAAM,CAACtD,KAAK,CAACuD,QAAQ,CAACX,SAAS,CAAC;QAClC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDxB,MAAM,CAACQ,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACxD,IAAI,IAAI,CAACb,YAAY,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACA,YAAY,CAAC2C,MAAM,CAAC,CAAC;MAC1B,IAAI,CAAC3C,YAAY,GAAG,IAAI;IAC1B;EACF,CAAC;EAEDK,MAAM,CAAC6B,YAAY,GAAG,SAASA,YAAYA,CAACU,SAAS,EAAEC,QAAQ,EAAE;IAC/D;IACA;IACA;IACAA,QAAQ,GAAG,IAAI,CAACC,eAAe,CAACD,QAAQ,CAAC;IACzC,IAAI,CAACpB,QAAQ,CAACmB,SAAS,EAAEC,QAAQ,CAAC;EACpC,CAAC;EAEDxC,MAAM,CAACyC,eAAe,GAAG,SAASA,eAAeA,CAACD,QAAQ,EAAE;IAC1D,IAAIE,MAAM,GAAG,IAAI;IAEjB,IAAIC,MAAM,GAAG,IAAI;IAEjB,IAAI,CAAChD,YAAY,GAAG,UAAUiD,KAAK,EAAE;MACnC,IAAID,MAAM,EAAE;QACVA,MAAM,GAAG,KAAK;QACdD,MAAM,CAAC/C,YAAY,GAAG,IAAI;QAC1B6C,QAAQ,CAACI,KAAK,CAAC;MACjB;IACF,CAAC;IAED,IAAI,CAACjD,YAAY,CAAC2C,MAAM,GAAG,YAAY;MACrCK,MAAM,GAAG,KAAK;IAChB,CAAC;IAED,OAAO,IAAI,CAAChD,YAAY;EAC1B,CAAC;EAEDK,MAAM,CAACiC,eAAe,GAAG,SAASA,eAAeA,CAACvB,OAAO,EAAEmC,OAAO,EAAE;IAClE,IAAI,CAACJ,eAAe,CAACI,OAAO,CAAC;IAC7B,IAAI/B,IAAI,GAAG,IAAI,CAAClC,KAAK,CAACmC,OAAO,GAAG,IAAI,CAACnC,KAAK,CAACmC,OAAO,CAACC,OAAO,GAAGhD,QAAQ,CAACiD,WAAW,CAAC,IAAI,CAAC;IACvF,IAAI6B,4BAA4B,GAAGpC,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC9B,KAAK,CAACmE,cAAc;IAEhF,IAAI,CAACjC,IAAI,IAAIgC,4BAA4B,EAAE;MACzCE,UAAU,CAAC,IAAI,CAACrD,YAAY,EAAE,CAAC,CAAC;MAChC;IACF;IAEA,IAAI,IAAI,CAACf,KAAK,CAACmE,cAAc,EAAE;MAC7B,IAAIE,KAAK,GAAG,IAAI,CAACrE,KAAK,CAACmC,OAAO,GAAG,CAAC,IAAI,CAACpB,YAAY,CAAC,GAAG,CAACmB,IAAI,EAAE,IAAI,CAACnB,YAAY,CAAC;QAC5E6B,SAAS,GAAGyB,KAAK,CAAC,CAAC,CAAC;QACpBC,iBAAiB,GAAGD,KAAK,CAAC,CAAC,CAAC;MAEhC,IAAI,CAACrE,KAAK,CAACmE,cAAc,CAACvB,SAAS,EAAE0B,iBAAiB,CAAC;IACzD;IAEA,IAAIxC,OAAO,IAAI,IAAI,EAAE;MACnBsC,UAAU,CAAC,IAAI,CAACrD,YAAY,EAAEe,OAAO,CAAC;IACxC;EACF,CAAC;EAEDV,MAAM,CAACmD,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAChC,IAAIzD,MAAM,GAAG,IAAI,CAACD,KAAK,CAACC,MAAM;IAE9B,IAAIA,MAAM,KAAKrB,SAAS,EAAE;MACxB,OAAO,IAAI;IACb;IAEA,IAAI+E,WAAW,GAAG,IAAI,CAACxE,KAAK;MACxByE,QAAQ,GAAGD,WAAW,CAACC,QAAQ;MAC/BC,GAAG,GAAGF,WAAW,CAAC9D,EAAE;MACpBiE,aAAa,GAAGH,WAAW,CAAC5D,YAAY;MACxCgE,cAAc,GAAGJ,WAAW,CAAC7D,aAAa;MAC1CkE,OAAO,GAAGL,WAAW,CAACnE,MAAM;MAC5ByE,MAAM,GAAGN,WAAW,CAACjE,KAAK;MAC1BwE,KAAK,GAAGP,WAAW,CAACzC,IAAI;MACxBiD,QAAQ,GAAGR,WAAW,CAAC1C,OAAO;MAC9BmD,eAAe,GAAGT,WAAW,CAACL,cAAc;MAC5Ce,QAAQ,GAAGV,WAAW,CAACrB,OAAO;MAC9BgC,WAAW,GAAGX,WAAW,CAACpB,UAAU;MACpCgC,UAAU,GAAGZ,WAAW,CAACtB,SAAS;MAClCmC,OAAO,GAAGb,WAAW,CAAChB,MAAM;MAC5B8B,UAAU,GAAGd,WAAW,CAACf,SAAS;MAClC8B,SAAS,GAAGf,WAAW,CAACjB,QAAQ;MAChCiC,QAAQ,GAAGhB,WAAW,CAACrC,OAAO;MAC9BsD,UAAU,GAAGzG,6BAA6B,CAACwF,WAAW,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,EAAE,eAAe,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IAE5P,QACE;MACA;MACArF,KAAK,CAACuG,aAAa,CAACnG,sBAAsB,CAACoG,QAAQ,EAAE;QACnDC,KAAK,EAAE;MACT,CAAC,EAAE,OAAOnB,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC3D,MAAM,EAAE2E,UAAU,CAAC,GAAGtG,KAAK,CAAC0G,YAAY,CAAC1G,KAAK,CAAC2G,QAAQ,CAACC,IAAI,CAACtB,QAAQ,CAAC,EAAEgB,UAAU,CAAC;IAAC;EAErI,CAAC;EAED,OAAO3F,UAAU;AACnB,CAAC,CAACX,KAAK,CAAC6G,SAAS,CAAC;AAElBlG,UAAU,CAACmG,WAAW,GAAG1G,sBAAsB;AAC/CO,UAAU,CAACoG,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EAC7D;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACElE,OAAO,EAAEjD,SAAS,CAACoH,KAAK,CAAC;IACvBlE,OAAO,EAAE,OAAOmE,OAAO,KAAK,WAAW,GAAGrH,SAAS,CAACsH,GAAG,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAE;MACjI,IAAIlB,KAAK,GAAGa,SAAS,CAACC,GAAG,CAAC;MAC1B,OAAOxH,SAAS,CAAC6H,UAAU,CAACnB,KAAK,IAAI,eAAe,IAAIA,KAAK,GAAGA,KAAK,CAACoB,aAAa,CAACC,WAAW,CAACV,OAAO,GAAGA,OAAO,CAAC,CAACE,SAAS,EAAEC,GAAG,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,CAAC;IACnL;EACF,CAAC,CAAC;EAEF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACErC,QAAQ,EAAEvF,SAAS,CAACgI,SAAS,CAAC,CAAChI,SAAS,CAACiI,IAAI,CAACC,UAAU,EAAElI,SAAS,CAACmI,OAAO,CAACD,UAAU,CAAC,CAAC,CAACA,UAAU;EAEnG;AACF;AACA;EACE1G,EAAE,EAAExB,SAAS,CAACoI,IAAI;EAElB;AACF;AACA;AACA;AACA;AACA;EACE1G,YAAY,EAAE1B,SAAS,CAACoI,IAAI;EAE5B;AACF;AACA;AACA;EACE3G,aAAa,EAAEzB,SAAS,CAACoI,IAAI;EAE7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEjH,MAAM,EAAEnB,SAAS,CAACoI,IAAI;EAEtB;AACF;AACA;EACE/G,KAAK,EAAErB,SAAS,CAACoI,IAAI;EAErB;AACF;AACA;EACEvF,IAAI,EAAE7C,SAAS,CAACoI,IAAI;EAEpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExF,OAAO,EAAE,SAASA,OAAOA,CAAC9B,KAAK,EAAE;IAC/B,IAAIuH,EAAE,GAAGjI,aAAa;IACtB,IAAI,CAACU,KAAK,CAACmE,cAAc,EAAEoD,EAAE,GAAGA,EAAE,CAACH,UAAU;IAE7C,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAClC;IAEA,OAAON,EAAE,CAACO,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC9H,KAAK,CAAC,CAAC+H,MAAM,CAACJ,IAAI,CAAC,CAAC;EAC/C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExD,cAAc,EAAEjF,SAAS,CAACiI,IAAI;EAE9B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEhE,OAAO,EAAEjE,SAAS,CAACiI,IAAI;EAEvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE/D,UAAU,EAAElE,SAAS,CAACiI,IAAI;EAE1B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEjE,SAAS,EAAEhE,SAAS,CAACiI,IAAI;EAEzB;AACF;AACA;AACA;AACA;AACA;AACA;EACE3D,MAAM,EAAEtE,SAAS,CAACiI,IAAI;EAEtB;AACF;AACA;AACA;AACA;AACA;AACA;EACE1D,SAAS,EAAEvE,SAAS,CAACiI,IAAI;EAEzB;AACF;AACA;AACA;AACA;AACA;AACA;EACE5D,QAAQ,EAAErE,SAAS,CAACiI;AACtB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAER,SAASa,IAAIA,CAAA,EAAG,CAAC;AAEjBlI,UAAU,CAACmI,YAAY,GAAG;EACxBvH,EAAE,EAAE,KAAK;EACTE,YAAY,EAAE,KAAK;EACnBD,aAAa,EAAE,KAAK;EACpBN,MAAM,EAAE,KAAK;EACbE,KAAK,EAAE,IAAI;EACXwB,IAAI,EAAE,IAAI;EACVoB,OAAO,EAAE6E,IAAI;EACb5E,UAAU,EAAE4E,IAAI;EAChB9E,SAAS,EAAE8E,IAAI;EACfxE,MAAM,EAAEwE,IAAI;EACZvE,SAAS,EAAEuE,IAAI;EACfzE,QAAQ,EAAEyE;AACZ,CAAC;AACDlI,UAAU,CAACL,SAAS,GAAGA,SAAS;AAChCK,UAAU,CAACJ,MAAM,GAAGA,MAAM;AAC1BI,UAAU,CAACH,QAAQ,GAAGA,QAAQ;AAC9BG,UAAU,CAACF,OAAO,GAAGA,OAAO;AAC5BE,UAAU,CAACD,OAAO,GAAGA,OAAO;AAC5B,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}