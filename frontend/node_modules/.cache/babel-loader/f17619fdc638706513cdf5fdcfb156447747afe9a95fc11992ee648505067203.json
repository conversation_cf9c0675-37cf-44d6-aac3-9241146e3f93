{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"getTrigger\", \"target\"];\nimport * as React from 'react';\nfunction defaultTrigger(store, options) {\n  const {\n    disableHysteresis = false,\n    threshold = 100,\n    target\n  } = options;\n  const previous = store.current;\n  if (target) {\n    // Get vertical scroll\n    store.current = target.pageYOffset !== undefined ? target.pageYOffset : target.scrollTop;\n  }\n  if (!disableHysteresis && previous !== undefined) {\n    if (store.current < previous) {\n      return false;\n    }\n  }\n  return store.current > threshold;\n}\nconst defaultTarget = typeof window !== 'undefined' ? window : null;\nexport default function useScrollTrigger(options = {}) {\n  const {\n      getTrigger = defaultTrigger,\n      target = defaultTarget\n    } = options,\n    other = _objectWithoutPropertiesLoose(options, _excluded);\n  const store = React.useRef();\n  const [trigger, setTrigger] = React.useState(() => getTrigger(store, other));\n  React.useEffect(() => {\n    const handleScroll = () => {\n      setTrigger(getTrigger(store, _extends({\n        target\n      }, other)));\n    };\n    handleScroll(); // Re-evaluate trigger when dependencies change\n    target.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    return () => {\n      target.removeEventListener('scroll', handleScroll, {\n        passive: true\n      });\n    };\n    // See Option 3. https://github.com/facebook/react/issues/14476#issuecomment-*********\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [target, getTrigger, JSON.stringify(other)]);\n  return trigger;\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "defaultTrigger", "store", "options", "disableHysteresis", "threshold", "target", "previous", "current", "pageYOffset", "undefined", "scrollTop", "defaultTarget", "window", "useScrollTrigger", "getTrigger", "other", "useRef", "trigger", "setTrigger", "useState", "useEffect", "handleScroll", "addEventListener", "passive", "removeEventListener", "JSON", "stringify"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@mui/material/useScrollTrigger/useScrollTrigger.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"getTrigger\", \"target\"];\nimport * as React from 'react';\nfunction defaultTrigger(store, options) {\n  const {\n    disableHysteresis = false,\n    threshold = 100,\n    target\n  } = options;\n  const previous = store.current;\n  if (target) {\n    // Get vertical scroll\n    store.current = target.pageYOffset !== undefined ? target.pageYOffset : target.scrollTop;\n  }\n  if (!disableHysteresis && previous !== undefined) {\n    if (store.current < previous) {\n      return false;\n    }\n  }\n  return store.current > threshold;\n}\nconst defaultTarget = typeof window !== 'undefined' ? window : null;\nexport default function useScrollTrigger(options = {}) {\n  const {\n      getTrigger = defaultTrigger,\n      target = defaultTarget\n    } = options,\n    other = _objectWithoutPropertiesLoose(options, _excluded);\n  const store = React.useRef();\n  const [trigger, setTrigger] = React.useState(() => getTrigger(store, other));\n  React.useEffect(() => {\n    const handleScroll = () => {\n      setTrigger(getTrigger(store, _extends({\n        target\n      }, other)));\n    };\n    handleScroll(); // Re-evaluate trigger when dependencies change\n    target.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    return () => {\n      target.removeEventListener('scroll', handleScroll, {\n        passive: true\n      });\n    };\n    // See Option 3. https://github.com/facebook/react/issues/14476#issuecomment-*********\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [target, getTrigger, JSON.stringify(other)]);\n  return trigger;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAcA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACtC,MAAM;IACJC,iBAAiB,GAAG,KAAK;IACzBC,SAAS,GAAG,GAAG;IACfC;EACF,CAAC,GAAGH,OAAO;EACX,MAAMI,QAAQ,GAAGL,KAAK,CAACM,OAAO;EAC9B,IAAIF,MAAM,EAAE;IACV;IACAJ,KAAK,CAACM,OAAO,GAAGF,MAAM,CAACG,WAAW,KAAKC,SAAS,GAAGJ,MAAM,CAACG,WAAW,GAAGH,MAAM,CAACK,SAAS;EAC1F;EACA,IAAI,CAACP,iBAAiB,IAAIG,QAAQ,KAAKG,SAAS,EAAE;IAChD,IAAIR,KAAK,CAACM,OAAO,GAAGD,QAAQ,EAAE;MAC5B,OAAO,KAAK;IACd;EACF;EACA,OAAOL,KAAK,CAACM,OAAO,GAAGH,SAAS;AAClC;AACA,MAAMO,aAAa,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,IAAI;AACnE,eAAe,SAASC,gBAAgBA,CAACX,OAAO,GAAG,CAAC,CAAC,EAAE;EACrD,MAAM;MACFY,UAAU,GAAGd,cAAc;MAC3BK,MAAM,GAAGM;IACX,CAAC,GAAGT,OAAO;IACXa,KAAK,GAAGlB,6BAA6B,CAACK,OAAO,EAAEJ,SAAS,CAAC;EAC3D,MAAMG,KAAK,GAAGF,KAAK,CAACiB,MAAM,CAAC,CAAC;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnB,KAAK,CAACoB,QAAQ,CAAC,MAAML,UAAU,CAACb,KAAK,EAAEc,KAAK,CAAC,CAAC;EAC5EhB,KAAK,CAACqB,SAAS,CAAC,MAAM;IACpB,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBH,UAAU,CAACJ,UAAU,CAACb,KAAK,EAAEL,QAAQ,CAAC;QACpCS;MACF,CAAC,EAAEU,KAAK,CAAC,CAAC,CAAC;IACb,CAAC;IACDM,YAAY,CAAC,CAAC,CAAC,CAAC;IAChBhB,MAAM,CAACiB,gBAAgB,CAAC,QAAQ,EAAED,YAAY,EAAE;MAC9CE,OAAO,EAAE;IACX,CAAC,CAAC;IACF,OAAO,MAAM;MACXlB,MAAM,CAACmB,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,EAAE;QACjDE,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC;IACD;IACA;EACF,CAAC,EAAE,CAAClB,MAAM,EAAES,UAAU,EAAEW,IAAI,CAACC,SAAS,CAACX,KAAK,CAAC,CAAC,CAAC;EAC/C,OAAOE,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}