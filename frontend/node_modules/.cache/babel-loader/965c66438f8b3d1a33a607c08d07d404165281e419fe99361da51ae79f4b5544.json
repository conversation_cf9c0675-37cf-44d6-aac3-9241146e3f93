{"ast": null, "code": "'use strict';\n\nvar inherits = require('inherits'),\n  EventEmitter = require('events').EventEmitter;\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:polling');\n}\nfunction Polling(Receiver, receiveUrl, AjaxObject) {\n  debug(receiveUrl);\n  EventEmitter.call(this);\n  this.Receiver = Receiver;\n  this.receiveUrl = receiveUrl;\n  this.AjaxObject = AjaxObject;\n  this._scheduleReceiver();\n}\ninherits(Polling, EventEmitter);\nPolling.prototype._scheduleReceiver = function () {\n  debug('_scheduleReceiver');\n  var self = this;\n  var poll = this.poll = new this.Receiver(this.receiveUrl, this.AjaxObject);\n  poll.on('message', function (msg) {\n    debug('message', msg);\n    self.emit('message', msg);\n  });\n  poll.once('close', function (code, reason) {\n    debug('close', code, reason, self.pollIsClosing);\n    self.poll = poll = null;\n    if (!self.pollIsClosing) {\n      if (reason === 'network') {\n        self._scheduleReceiver();\n      } else {\n        self.emit('close', code || 1006, reason);\n        self.removeAllListeners();\n      }\n    }\n  });\n};\nPolling.prototype.abort = function () {\n  debug('abort');\n  this.removeAllListeners();\n  this.pollIsClosing = true;\n  if (this.poll) {\n    this.poll.abort();\n  }\n};\nmodule.exports = Polling;", "map": {"version": 3, "names": ["inherits", "require", "EventEmitter", "debug", "process", "env", "NODE_ENV", "Polling", "Receiver", "receiveUrl", "AjaxObject", "call", "_scheduleReceiver", "prototype", "self", "poll", "on", "msg", "emit", "once", "code", "reason", "pollIsClosing", "removeAllListeners", "abort", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/transport/lib/polling.js"], "sourcesContent": ["'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:polling');\n}\n\nfunction Polling(Receiver, receiveUrl, AjaxObject) {\n  debug(receiveUrl);\n  EventEmitter.call(this);\n  this.Receiver = Receiver;\n  this.receiveUrl = receiveUrl;\n  this.AjaxObject = AjaxObject;\n  this._scheduleReceiver();\n}\n\ninherits(Polling, EventEmitter);\n\nPolling.prototype._scheduleReceiver = function() {\n  debug('_scheduleReceiver');\n  var self = this;\n  var poll = this.poll = new this.Receiver(this.receiveUrl, this.AjaxObject);\n\n  poll.on('message', function(msg) {\n    debug('message', msg);\n    self.emit('message', msg);\n  });\n\n  poll.once('close', function(code, reason) {\n    debug('close', code, reason, self.pollIsClosing);\n    self.poll = poll = null;\n\n    if (!self.pollIsClosing) {\n      if (reason === 'network') {\n        self._scheduleReceiver();\n      } else {\n        self.emit('close', code || 1006, reason);\n        self.removeAllListeners();\n      }\n    }\n  });\n};\n\nPolling.prototype.abort = function() {\n  debug('abort');\n  this.removeAllListeners();\n  this.pollIsClosing = true;\n  if (this.poll) {\n    this.poll.abort();\n  }\n};\n\nmodule.exports = Polling;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAC9BC,YAAY,GAAGD,OAAO,CAAC,QAAQ,CAAC,CAACC,YAAY;AAGjD,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGF,OAAO,CAAC,OAAO,CAAC,CAAC,uBAAuB,CAAC;AACnD;AAEA,SAASM,OAAOA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAE;EACjDP,KAAK,CAACM,UAAU,CAAC;EACjBP,YAAY,CAACS,IAAI,CAAC,IAAI,CAAC;EACvB,IAAI,CAACH,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC5B,IAAI,CAACE,iBAAiB,CAAC,CAAC;AAC1B;AAEAZ,QAAQ,CAACO,OAAO,EAAEL,YAAY,CAAC;AAE/BK,OAAO,CAACM,SAAS,CAACD,iBAAiB,GAAG,YAAW;EAC/CT,KAAK,CAAC,mBAAmB,CAAC;EAC1B,IAAIW,IAAI,GAAG,IAAI;EACf,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,IAAI,IAAI,CAACP,QAAQ,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,UAAU,CAAC;EAE1EK,IAAI,CAACC,EAAE,CAAC,SAAS,EAAE,UAASC,GAAG,EAAE;IAC/Bd,KAAK,CAAC,SAAS,EAAEc,GAAG,CAAC;IACrBH,IAAI,CAACI,IAAI,CAAC,SAAS,EAAED,GAAG,CAAC;EAC3B,CAAC,CAAC;EAEFF,IAAI,CAACI,IAAI,CAAC,OAAO,EAAE,UAASC,IAAI,EAAEC,MAAM,EAAE;IACxClB,KAAK,CAAC,OAAO,EAAEiB,IAAI,EAAEC,MAAM,EAAEP,IAAI,CAACQ,aAAa,CAAC;IAChDR,IAAI,CAACC,IAAI,GAAGA,IAAI,GAAG,IAAI;IAEvB,IAAI,CAACD,IAAI,CAACQ,aAAa,EAAE;MACvB,IAAID,MAAM,KAAK,SAAS,EAAE;QACxBP,IAAI,CAACF,iBAAiB,CAAC,CAAC;MAC1B,CAAC,MAAM;QACLE,IAAI,CAACI,IAAI,CAAC,OAAO,EAAEE,IAAI,IAAI,IAAI,EAAEC,MAAM,CAAC;QACxCP,IAAI,CAACS,kBAAkB,CAAC,CAAC;MAC3B;IACF;EACF,CAAC,CAAC;AACJ,CAAC;AAEDhB,OAAO,CAACM,SAAS,CAACW,KAAK,GAAG,YAAW;EACnCrB,KAAK,CAAC,OAAO,CAAC;EACd,IAAI,CAACoB,kBAAkB,CAAC,CAAC;EACzB,IAAI,CAACD,aAAa,GAAG,IAAI;EACzB,IAAI,IAAI,CAACP,IAAI,EAAE;IACb,IAAI,CAACA,IAAI,CAACS,KAAK,CAAC,CAAC;EACnB;AACF,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGnB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}