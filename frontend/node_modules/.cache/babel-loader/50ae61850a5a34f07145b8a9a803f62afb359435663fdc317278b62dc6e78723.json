{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n  inherits = require('inherits'),\n  utils = require('./utils/event'),\n  IframeTransport = require('./transport/iframe'),\n  InfoReceiverIframe = require('./info-iframe-receiver');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-iframe');\n}\nfunction InfoIframe(baseUrl, url) {\n  var self = this;\n  EventEmitter.call(this);\n  var go = function () {\n    var ifr = self.ifr = new IframeTransport(InfoReceiverIframe.transportName, url, baseUrl);\n    ifr.once('message', function (msg) {\n      if (msg) {\n        var d;\n        try {\n          d = JSON.parse(msg);\n        } catch (e) {\n          debug('bad json', msg);\n          self.emit('finish');\n          self.close();\n          return;\n        }\n        var info = d[0],\n          rtt = d[1];\n        self.emit('finish', info, rtt);\n      }\n      self.close();\n    });\n    ifr.once('close', function () {\n      self.emit('finish');\n      self.close();\n    });\n  };\n\n  // TODO this seems the same as the 'needBody' from transports\n  if (!global.document.body) {\n    utils.attachEvent('load', go);\n  } else {\n    go();\n  }\n}\ninherits(InfoIframe, EventEmitter);\nInfoIframe.enabled = function () {\n  return IframeTransport.enabled();\n};\nInfoIframe.prototype.close = function () {\n  if (this.ifr) {\n    this.ifr.close();\n  }\n  this.removeAllListeners();\n  this.ifr = null;\n};\nmodule.exports = InfoIframe;", "map": {"version": 3, "names": ["EventEmitter", "require", "inherits", "utils", "IframeTransport", "InfoReceiverIframe", "debug", "process", "env", "NODE_ENV", "InfoIframe", "baseUrl", "url", "self", "call", "go", "ifr", "transportName", "once", "msg", "d", "JSON", "parse", "e", "emit", "close", "info", "rtt", "global", "document", "body", "attachEvent", "enabled", "prototype", "removeAllListeners", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/info-iframe.js"], "sourcesContent": ["'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , utils = require('./utils/event')\n  , IframeTransport = require('./transport/iframe')\n  , InfoReceiverIframe = require('./info-iframe-receiver')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-iframe');\n}\n\nfunction InfoIframe(baseUrl, url) {\n  var self = this;\n  EventEmitter.call(this);\n\n  var go = function() {\n    var ifr = self.ifr = new IframeTransport(InfoReceiverIframe.transportName, url, baseUrl);\n\n    ifr.once('message', function(msg) {\n      if (msg) {\n        var d;\n        try {\n          d = JSON.parse(msg);\n        } catch (e) {\n          debug('bad json', msg);\n          self.emit('finish');\n          self.close();\n          return;\n        }\n\n        var info = d[0], rtt = d[1];\n        self.emit('finish', info, rtt);\n      }\n      self.close();\n    });\n\n    ifr.once('close', function() {\n      self.emit('finish');\n      self.close();\n    });\n  };\n\n  // TODO this seems the same as the 'needBody' from transports\n  if (!global.document.body) {\n    utils.attachEvent('load', go);\n  } else {\n    go();\n  }\n}\n\ninherits(InfoIframe, EventEmitter);\n\nInfoIframe.enabled = function() {\n  return IframeTransport.enabled();\n};\n\nInfoIframe.prototype.close = function() {\n  if (this.ifr) {\n    this.ifr.close();\n  }\n  this.removeAllListeners();\n  this.ifr = null;\n};\n\nmodule.exports = InfoIframe;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAQ,CAAC,CAACD,YAAY;EAC7CE,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;EAC9BE,KAAK,GAAGF,OAAO,CAAC,eAAe,CAAC;EAChCG,eAAe,GAAGH,OAAO,CAAC,oBAAoB,CAAC;EAC/CI,kBAAkB,GAAGJ,OAAO,CAAC,wBAAwB,CAAC;AAG1D,IAAIK,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGL,OAAO,CAAC,OAAO,CAAC,CAAC,2BAA2B,CAAC;AACvD;AAEA,SAASS,UAAUA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAChC,IAAIC,IAAI,GAAG,IAAI;EACfb,YAAY,CAACc,IAAI,CAAC,IAAI,CAAC;EAEvB,IAAIC,EAAE,GAAG,SAAAA,CAAA,EAAW;IAClB,IAAIC,GAAG,GAAGH,IAAI,CAACG,GAAG,GAAG,IAAIZ,eAAe,CAACC,kBAAkB,CAACY,aAAa,EAAEL,GAAG,EAAED,OAAO,CAAC;IAExFK,GAAG,CAACE,IAAI,CAAC,SAAS,EAAE,UAASC,GAAG,EAAE;MAChC,IAAIA,GAAG,EAAE;QACP,IAAIC,CAAC;QACL,IAAI;UACFA,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC;QACrB,CAAC,CAAC,OAAOI,CAAC,EAAE;UACVjB,KAAK,CAAC,UAAU,EAAEa,GAAG,CAAC;UACtBN,IAAI,CAACW,IAAI,CAAC,QAAQ,CAAC;UACnBX,IAAI,CAACY,KAAK,CAAC,CAAC;UACZ;QACF;QAEA,IAAIC,IAAI,GAAGN,CAAC,CAAC,CAAC,CAAC;UAAEO,GAAG,GAAGP,CAAC,CAAC,CAAC,CAAC;QAC3BP,IAAI,CAACW,IAAI,CAAC,QAAQ,EAAEE,IAAI,EAAEC,GAAG,CAAC;MAChC;MACAd,IAAI,CAACY,KAAK,CAAC,CAAC;IACd,CAAC,CAAC;IAEFT,GAAG,CAACE,IAAI,CAAC,OAAO,EAAE,YAAW;MAC3BL,IAAI,CAACW,IAAI,CAAC,QAAQ,CAAC;MACnBX,IAAI,CAACY,KAAK,CAAC,CAAC;IACd,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAI,CAACG,MAAM,CAACC,QAAQ,CAACC,IAAI,EAAE;IACzB3B,KAAK,CAAC4B,WAAW,CAAC,MAAM,EAAEhB,EAAE,CAAC;EAC/B,CAAC,MAAM;IACLA,EAAE,CAAC,CAAC;EACN;AACF;AAEAb,QAAQ,CAACQ,UAAU,EAAEV,YAAY,CAAC;AAElCU,UAAU,CAACsB,OAAO,GAAG,YAAW;EAC9B,OAAO5B,eAAe,CAAC4B,OAAO,CAAC,CAAC;AAClC,CAAC;AAEDtB,UAAU,CAACuB,SAAS,CAACR,KAAK,GAAG,YAAW;EACtC,IAAI,IAAI,CAACT,GAAG,EAAE;IACZ,IAAI,CAACA,GAAG,CAACS,KAAK,CAAC,CAAC;EAClB;EACA,IAAI,CAACS,kBAAkB,CAAC,CAAC;EACzB,IAAI,CAAClB,GAAG,GAAG,IAAI;AACjB,CAAC;AAEDmB,MAAM,CAACC,OAAO,GAAG1B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}