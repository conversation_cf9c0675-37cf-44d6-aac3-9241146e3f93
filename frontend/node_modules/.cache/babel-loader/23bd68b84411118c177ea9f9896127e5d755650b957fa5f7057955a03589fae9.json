{"ast": null, "code": "import { useEffect, useRef } from 'react';\nexport function createElementObject(instance, context, container) {\n  return Object.freeze({\n    instance,\n    context,\n    container\n  });\n}\nexport function createElementHook(createElement, updateElement) {\n  if (updateElement == null) {\n    return function useImmutableLeafletElement(props, context) {\n      const elementRef = useRef();\n      if (!elementRef.current) elementRef.current = createElement(props, context);\n      return elementRef;\n    };\n  }\n  return function useMutableLeafletElement(props, context) {\n    const elementRef = useRef();\n    if (!elementRef.current) elementRef.current = createElement(props, context);\n    const propsRef = useRef(props);\n    const {\n      instance\n    } = elementRef.current;\n    useEffect(function updateElementProps() {\n      if (propsRef.current !== props) {\n        updateElement(instance, props, propsRef.current);\n        propsRef.current = props;\n      }\n    }, [instance, props, context]);\n    return elementRef;\n  };\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "createElementObject", "instance", "context", "container", "Object", "freeze", "createElementHook", "createElement", "updateElement", "useImmutableLeafletElement", "props", "elementRef", "current", "useMutableLeafletElement", "propsRef", "updateElementProps"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-leaflet/core/lib/element.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nexport function createElementObject(instance, context, container) {\n    return Object.freeze({\n        instance,\n        context,\n        container\n    });\n}\nexport function createElementHook(createElement, updateElement) {\n    if (updateElement == null) {\n        return function useImmutableLeafletElement(props, context) {\n            const elementRef = useRef();\n            if (!elementRef.current) elementRef.current = createElement(props, context);\n            return elementRef;\n        };\n    }\n    return function useMutableLeafletElement(props, context) {\n        const elementRef = useRef();\n        if (!elementRef.current) elementRef.current = createElement(props, context);\n        const propsRef = useRef(props);\n        const { instance  } = elementRef.current;\n        useEffect(function updateElementProps() {\n            if (propsRef.current !== props) {\n                updateElement(instance, props, propsRef.current);\n                propsRef.current = props;\n            }\n        }, [\n            instance,\n            props,\n            context\n        ]);\n        return elementRef;\n    };\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAO,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAE;EAC9D,OAAOC,MAAM,CAACC,MAAM,CAAC;IACjBJ,QAAQ;IACRC,OAAO;IACPC;EACJ,CAAC,CAAC;AACN;AACA,OAAO,SAASG,iBAAiBA,CAACC,aAAa,EAAEC,aAAa,EAAE;EAC5D,IAAIA,aAAa,IAAI,IAAI,EAAE;IACvB,OAAO,SAASC,0BAA0BA,CAACC,KAAK,EAAER,OAAO,EAAE;MACvD,MAAMS,UAAU,GAAGZ,MAAM,CAAC,CAAC;MAC3B,IAAI,CAACY,UAAU,CAACC,OAAO,EAAED,UAAU,CAACC,OAAO,GAAGL,aAAa,CAACG,KAAK,EAAER,OAAO,CAAC;MAC3E,OAAOS,UAAU;IACrB,CAAC;EACL;EACA,OAAO,SAASE,wBAAwBA,CAACH,KAAK,EAAER,OAAO,EAAE;IACrD,MAAMS,UAAU,GAAGZ,MAAM,CAAC,CAAC;IAC3B,IAAI,CAACY,UAAU,CAACC,OAAO,EAAED,UAAU,CAACC,OAAO,GAAGL,aAAa,CAACG,KAAK,EAAER,OAAO,CAAC;IAC3E,MAAMY,QAAQ,GAAGf,MAAM,CAACW,KAAK,CAAC;IAC9B,MAAM;MAAET;IAAU,CAAC,GAAGU,UAAU,CAACC,OAAO;IACxCd,SAAS,CAAC,SAASiB,kBAAkBA,CAAA,EAAG;MACpC,IAAID,QAAQ,CAACF,OAAO,KAAKF,KAAK,EAAE;QAC5BF,aAAa,CAACP,QAAQ,EAAES,KAAK,EAAEI,QAAQ,CAACF,OAAO,CAAC;QAChDE,QAAQ,CAACF,OAAO,GAAGF,KAAK;MAC5B;IACJ,CAAC,EAAE,CACCT,QAAQ,EACRS,KAAK,EACLR,OAAO,CACV,CAAC;IACF,OAAOS,UAAU;EACrB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}