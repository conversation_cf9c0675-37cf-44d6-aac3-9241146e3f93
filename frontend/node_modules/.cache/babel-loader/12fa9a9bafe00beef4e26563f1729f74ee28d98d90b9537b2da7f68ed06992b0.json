{"ast": null, "code": "'use strict';\n\nvar has = Object.prototype.hasOwnProperty,\n  undef;\n\n/**\n * Decode a URI encoded string.\n *\n * @param {String} input The URI encoded string.\n * @returns {String|Null} The decoded string.\n * @api private\n */\nfunction decode(input) {\n  try {\n    return decodeURIComponent(input.replace(/\\+/g, ' '));\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\n * Attempts to encode a given input.\n *\n * @param {String} input The string that needs to be encoded.\n * @returns {String|Null} The encoded string.\n * @api private\n */\nfunction encode(input) {\n  try {\n    return encodeURIComponent(input);\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\n * Simple query string parser.\n *\n * @param {String} query The query string that needs to be parsed.\n * @returns {Object}\n * @api public\n */\nfunction querystring(query) {\n  var parser = /([^=?#&]+)=?([^&]*)/g,\n    result = {},\n    part;\n  while (part = parser.exec(query)) {\n    var key = decode(part[1]),\n      value = decode(part[2]);\n\n    //\n    // Prevent overriding of existing properties. This ensures that build-in\n    // methods like `toString` or __proto__ are not overriden by malicious\n    // querystrings.\n    //\n    // In the case if failed decoding, we want to omit the key/value pairs\n    // from the result.\n    //\n    if (key === null || value === null || key in result) continue;\n    result[key] = value;\n  }\n  return result;\n}\n\n/**\n * Transform a query string to an object.\n *\n * @param {Object} obj Object that should be transformed.\n * @param {String} prefix Optional prefix.\n * @returns {String}\n * @api public\n */\nfunction querystringify(obj, prefix) {\n  prefix = prefix || '';\n  var pairs = [],\n    value,\n    key;\n\n  //\n  // Optionally prefix with a '?' if needed\n  //\n  if ('string' !== typeof prefix) prefix = '?';\n  for (key in obj) {\n    if (has.call(obj, key)) {\n      value = obj[key];\n\n      //\n      // Edge cases where we actually want to encode the value to an empty\n      // string instead of the stringified value.\n      //\n      if (!value && (value === null || value === undef || isNaN(value))) {\n        value = '';\n      }\n      key = encode(key);\n      value = encode(value);\n\n      //\n      // If we failed to encode the strings, we should bail out as we don't\n      // want to add invalid strings to the query.\n      //\n      if (key === null || value === null) continue;\n      pairs.push(key + '=' + value);\n    }\n  }\n  return pairs.length ? prefix + pairs.join('&') : '';\n}\n\n//\n// Expose the module.\n//\nexports.stringify = querystringify;\nexports.parse = querystring;", "map": {"version": 3, "names": ["has", "Object", "prototype", "hasOwnProperty", "undef", "decode", "input", "decodeURIComponent", "replace", "e", "encode", "encodeURIComponent", "querystring", "query", "parser", "result", "part", "exec", "key", "value", "querystringify", "obj", "prefix", "pairs", "call", "isNaN", "push", "length", "join", "exports", "stringify", "parse"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/querystringify/index.js"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , undef;\n\n/**\n * Decode a URI encoded string.\n *\n * @param {String} input The URI encoded string.\n * @returns {String|Null} The decoded string.\n * @api private\n */\nfunction decode(input) {\n  try {\n    return decodeURIComponent(input.replace(/\\+/g, ' '));\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\n * Attempts to encode a given input.\n *\n * @param {String} input The string that needs to be encoded.\n * @returns {String|Null} The encoded string.\n * @api private\n */\nfunction encode(input) {\n  try {\n    return encodeURIComponent(input);\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\n * Simple query string parser.\n *\n * @param {String} query The query string that needs to be parsed.\n * @returns {Object}\n * @api public\n */\nfunction querystring(query) {\n  var parser = /([^=?#&]+)=?([^&]*)/g\n    , result = {}\n    , part;\n\n  while (part = parser.exec(query)) {\n    var key = decode(part[1])\n      , value = decode(part[2]);\n\n    //\n    // Prevent overriding of existing properties. This ensures that build-in\n    // methods like `toString` or __proto__ are not overriden by malicious\n    // querystrings.\n    //\n    // In the case if failed decoding, we want to omit the key/value pairs\n    // from the result.\n    //\n    if (key === null || value === null || key in result) continue;\n    result[key] = value;\n  }\n\n  return result;\n}\n\n/**\n * Transform a query string to an object.\n *\n * @param {Object} obj Object that should be transformed.\n * @param {String} prefix Optional prefix.\n * @returns {String}\n * @api public\n */\nfunction querystringify(obj, prefix) {\n  prefix = prefix || '';\n\n  var pairs = []\n    , value\n    , key;\n\n  //\n  // Optionally prefix with a '?' if needed\n  //\n  if ('string' !== typeof prefix) prefix = '?';\n\n  for (key in obj) {\n    if (has.call(obj, key)) {\n      value = obj[key];\n\n      //\n      // Edge cases where we actually want to encode the value to an empty\n      // string instead of the stringified value.\n      //\n      if (!value && (value === null || value === undef || isNaN(value))) {\n        value = '';\n      }\n\n      key = encode(key);\n      value = encode(value);\n\n      //\n      // If we failed to encode the strings, we should bail out as we don't\n      // want to add invalid strings to the query.\n      //\n      if (key === null || value === null) continue;\n      pairs.push(key +'='+ value);\n    }\n  }\n\n  return pairs.length ? prefix + pairs.join('&') : '';\n}\n\n//\n// Expose the module.\n//\nexports.stringify = querystringify;\nexports.parse = querystring;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,GAAG,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;EACrCC,KAAK;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,KAAK,EAAE;EACrB,IAAI;IACF,OAAOC,kBAAkB,CAACD,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;EACtD,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACJ,KAAK,EAAE;EACrB,IAAI;IACF,OAAOK,kBAAkB,CAACL,KAAK,CAAC;EAClC,CAAC,CAAC,OAAOG,CAAC,EAAE;IACV,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIC,MAAM,GAAG,sBAAsB;IAC/BC,MAAM,GAAG,CAAC,CAAC;IACXC,IAAI;EAER,OAAOA,IAAI,GAAGF,MAAM,CAACG,IAAI,CAACJ,KAAK,CAAC,EAAE;IAChC,IAAIK,GAAG,GAAGb,MAAM,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC;MACrBG,KAAK,GAAGd,MAAM,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIE,GAAG,KAAK,IAAI,IAAIC,KAAK,KAAK,IAAI,IAAID,GAAG,IAAIH,MAAM,EAAE;IACrDA,MAAM,CAACG,GAAG,CAAC,GAAGC,KAAK;EACrB;EAEA,OAAOJ,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,cAAcA,CAACC,GAAG,EAAEC,MAAM,EAAE;EACnCA,MAAM,GAAGA,MAAM,IAAI,EAAE;EAErB,IAAIC,KAAK,GAAG,EAAE;IACVJ,KAAK;IACLD,GAAG;;EAEP;EACA;EACA;EACA,IAAI,QAAQ,KAAK,OAAOI,MAAM,EAAEA,MAAM,GAAG,GAAG;EAE5C,KAAKJ,GAAG,IAAIG,GAAG,EAAE;IACf,IAAIrB,GAAG,CAACwB,IAAI,CAACH,GAAG,EAAEH,GAAG,CAAC,EAAE;MACtBC,KAAK,GAAGE,GAAG,CAACH,GAAG,CAAC;;MAEhB;MACA;MACA;MACA;MACA,IAAI,CAACC,KAAK,KAAKA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKf,KAAK,IAAIqB,KAAK,CAACN,KAAK,CAAC,CAAC,EAAE;QACjEA,KAAK,GAAG,EAAE;MACZ;MAEAD,GAAG,GAAGR,MAAM,CAACQ,GAAG,CAAC;MACjBC,KAAK,GAAGT,MAAM,CAACS,KAAK,CAAC;;MAErB;MACA;MACA;MACA;MACA,IAAID,GAAG,KAAK,IAAI,IAAIC,KAAK,KAAK,IAAI,EAAE;MACpCI,KAAK,CAACG,IAAI,CAACR,GAAG,GAAE,GAAG,GAAEC,KAAK,CAAC;IAC7B;EACF;EAEA,OAAOI,KAAK,CAACI,MAAM,GAAGL,MAAM,GAAGC,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;AACrD;;AAEA;AACA;AACA;AACAC,OAAO,CAACC,SAAS,GAAGV,cAAc;AAClCS,OAAO,CAACE,KAAK,GAAGnB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}