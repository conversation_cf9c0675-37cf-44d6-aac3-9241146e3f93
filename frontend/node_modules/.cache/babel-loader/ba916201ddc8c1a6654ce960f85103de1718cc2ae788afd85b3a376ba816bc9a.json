{"ast": null, "code": "'use strict';\n\nvar EventEmitter = require('events').EventEmitter,\n  inherits = require('inherits'),\n  objectUtils = require('./utils/object');\nvar debug = function () {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-ajax');\n}\nfunction InfoAjax(url, AjaxObject) {\n  EventEmitter.call(this);\n  var self = this;\n  var t0 = +new Date();\n  this.xo = new AjaxObject('GET', url);\n  this.xo.once('finish', function (status, text) {\n    var info, rtt;\n    if (status === 200) {\n      rtt = +new Date() - t0;\n      if (text) {\n        try {\n          info = JSON.parse(text);\n        } catch (e) {\n          debug('bad json', text);\n        }\n      }\n      if (!objectUtils.isObject(info)) {\n        info = {};\n      }\n    }\n    self.emit('finish', info, rtt);\n    self.removeAllListeners();\n  });\n}\ninherits(InfoAjax, EventEmitter);\nInfoAjax.prototype.close = function () {\n  this.removeAllListeners();\n  this.xo.close();\n};\nmodule.exports = InfoAjax;", "map": {"version": 3, "names": ["EventEmitter", "require", "inherits", "objectUtils", "debug", "process", "env", "NODE_ENV", "InfoAjax", "url", "AjaxObject", "call", "self", "t0", "Date", "xo", "once", "status", "text", "info", "rtt", "JSON", "parse", "e", "isObject", "emit", "removeAllListeners", "prototype", "close", "module", "exports"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/sockjs-client/lib/info-ajax.js"], "sourcesContent": ["'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , objectUtils = require('./utils/object')\n  ;\n\nvar debug = function() {};\nif (process.env.NODE_ENV !== 'production') {\n  debug = require('debug')('sockjs-client:info-ajax');\n}\n\nfunction InfoAjax(url, AjaxObject) {\n  EventEmitter.call(this);\n\n  var self = this;\n  var t0 = +new Date();\n  this.xo = new AjaxObject('GET', url);\n\n  this.xo.once('finish', function(status, text) {\n    var info, rtt;\n    if (status === 200) {\n      rtt = (+new Date()) - t0;\n      if (text) {\n        try {\n          info = JSON.parse(text);\n        } catch (e) {\n          debug('bad json', text);\n        }\n      }\n\n      if (!objectUtils.isObject(info)) {\n        info = {};\n      }\n    }\n    self.emit('finish', info, rtt);\n    self.removeAllListeners();\n  });\n}\n\ninherits(InfoAjax, EventEmitter);\n\nInfoAjax.prototype.close = function() {\n  this.removeAllListeners();\n  this.xo.close();\n};\n\nmodule.exports = InfoAjax;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAQ,CAAC,CAACD,YAAY;EAC7CE,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;EAC9BE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AAG3C,IAAIG,KAAK,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,KAAK,GAAGH,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC;AACrD;AAEA,SAASO,QAAQA,CAACC,GAAG,EAAEC,UAAU,EAAE;EACjCV,YAAY,CAACW,IAAI,CAAC,IAAI,CAAC;EAEvB,IAAIC,IAAI,GAAG,IAAI;EACf,IAAIC,EAAE,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;EACpB,IAAI,CAACC,EAAE,GAAG,IAAIL,UAAU,CAAC,KAAK,EAAED,GAAG,CAAC;EAEpC,IAAI,CAACM,EAAE,CAACC,IAAI,CAAC,QAAQ,EAAE,UAASC,MAAM,EAAEC,IAAI,EAAE;IAC5C,IAAIC,IAAI,EAAEC,GAAG;IACb,IAAIH,MAAM,KAAK,GAAG,EAAE;MAClBG,GAAG,GAAI,CAAC,IAAIN,IAAI,CAAC,CAAC,GAAID,EAAE;MACxB,IAAIK,IAAI,EAAE;QACR,IAAI;UACFC,IAAI,GAAGE,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC;QACzB,CAAC,CAAC,OAAOK,CAAC,EAAE;UACVnB,KAAK,CAAC,UAAU,EAAEc,IAAI,CAAC;QACzB;MACF;MAEA,IAAI,CAACf,WAAW,CAACqB,QAAQ,CAACL,IAAI,CAAC,EAAE;QAC/BA,IAAI,GAAG,CAAC,CAAC;MACX;IACF;IACAP,IAAI,CAACa,IAAI,CAAC,QAAQ,EAAEN,IAAI,EAAEC,GAAG,CAAC;IAC9BR,IAAI,CAACc,kBAAkB,CAAC,CAAC;EAC3B,CAAC,CAAC;AACJ;AAEAxB,QAAQ,CAACM,QAAQ,EAAER,YAAY,CAAC;AAEhCQ,QAAQ,CAACmB,SAAS,CAACC,KAAK,GAAG,YAAW;EACpC,IAAI,CAACF,kBAAkB,CAAC,CAAC;EACzB,IAAI,CAACX,EAAE,CAACa,KAAK,CAAC,CAAC;AACjB,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGtB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}