{"ast": null, "code": "export { default } from './ClassNameGenerator';", "map": {"version": 3, "names": ["default"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@mui/utils/esm/ClassNameGenerator/index.js"], "sourcesContent": ["export { default } from './ClassNameGenerator';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}