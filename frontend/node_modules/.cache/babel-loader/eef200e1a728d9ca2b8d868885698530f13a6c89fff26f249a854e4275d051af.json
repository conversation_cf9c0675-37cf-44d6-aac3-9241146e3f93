{"ast": null, "code": "// src/hooks/useChain.ts\nimport { each, useIsomorphicLayoutEffect } from \"@react-spring/shared\";\n\n// src/helpers.ts\nimport { is, toArray, eachProp, getFluidValue, isAnimatedString, Globals as G } from \"@react-spring/shared\";\nfunction callProp(value, ...args) {\n  return is.fun(value) ? value(...args) : value;\n}\nvar matchProp = (value, key) => value === true || !!(key && value && (is.fun(value) ? value(key) : toArray(value).includes(key)));\nvar resolveProp = (prop, key) => is.obj(prop) ? key && prop[key] : prop;\nvar getDefaultProp = (props, key) => props.default === true ? props[key] : props.default ? props.default[key] : void 0;\nvar noopTransform = value => value;\nvar getDefaultProps = (props, transform = noopTransform) => {\n  let keys = DEFAULT_PROPS;\n  if (props.default && props.default !== true) {\n    props = props.default;\n    keys = Object.keys(props);\n  }\n  const defaults2 = {};\n  for (const key of keys) {\n    const value = transform(props[key], key);\n    if (!is.und(value)) {\n      defaults2[key] = value;\n    }\n  }\n  return defaults2;\n};\nvar DEFAULT_PROPS = [\"config\", \"onProps\", \"onStart\", \"onChange\", \"onPause\", \"onResume\", \"onRest\"];\nvar RESERVED_PROPS = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n  // Transition props\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n  // Internal props\n  keys: 1,\n  callId: 1,\n  parentId: 1\n};\nfunction getForwardProps(props) {\n  const forward = {};\n  let count = 0;\n  eachProp(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value;\n      count++;\n    }\n  });\n  if (count) {\n    return forward;\n  }\n}\nfunction inferTo(props) {\n  const to2 = getForwardProps(props);\n  if (to2) {\n    const out = {\n      to: to2\n    };\n    eachProp(props, (val, key) => key in to2 || (out[key] = val));\n    return out;\n  }\n  return {\n    ...props\n  };\n}\nfunction computeGoal(value) {\n  value = getFluidValue(value);\n  return is.arr(value) ? value.map(computeGoal) : isAnimatedString(value) ? G.createStringInterpolator({\n    range: [0, 1],\n    output: [value, value]\n  })(1) : value;\n}\nfunction hasProps(props) {\n  for (const _ in props) return true;\n  return false;\n}\nfunction isAsyncTo(to2) {\n  return is.fun(to2) || is.arr(to2) && is.obj(to2[0]);\n}\nfunction detachRefs(ctrl, ref) {\n  ctrl.ref?.delete(ctrl);\n  ref?.delete(ctrl);\n}\nfunction replaceRef(ctrl, ref) {\n  if (ref && ctrl.ref !== ref) {\n    ctrl.ref?.delete(ctrl);\n    ref.add(ctrl);\n    ctrl.ref = ref;\n  }\n}\n\n// src/hooks/useChain.ts\nfunction useChain(refs, timeSteps, timeFrame = 1e3) {\n  useIsomorphicLayoutEffect(() => {\n    if (timeSteps) {\n      let prevDelay = 0;\n      each(refs, (ref, i) => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i];\n          if (isNaN(delay)) delay = prevDelay;else prevDelay = delay;\n          each(controllers, ctrl => {\n            each(ctrl.queue, props => {\n              const memoizedDelayProp = props.delay;\n              props.delay = key => delay + callProp(memoizedDelayProp || 0, key);\n            });\n          });\n          ref.start();\n        }\n      });\n    } else {\n      let p = Promise.resolve();\n      each(refs, ref => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          const queues = controllers.map(ctrl => {\n            const q = ctrl.queue;\n            ctrl.queue = [];\n            return q;\n          });\n          p = p.then(() => {\n            each(controllers, (ctrl, i) => each(queues[i] || [], update2 => ctrl.queue.push(update2)));\n            return Promise.all(ref.start());\n          });\n        }\n      });\n    }\n  });\n}\n\n// src/hooks/useSpring.ts\nimport { is as is9 } from \"@react-spring/shared\";\n\n// src/hooks/useSprings.ts\nimport { useContext as useContext2, useMemo, useRef } from \"react\";\nimport { is as is8, each as each5, usePrev, useOnce, useForceUpdate, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect2 } from \"@react-spring/shared\";\n\n// src/SpringValue.ts\nimport { is as is5, raf as raf3, each as each2, isEqual, toArray as toArray2, eachProp as eachProp3, frameLoop as frameLoop2, flushCalls, getFluidValue as getFluidValue2, isAnimatedString as isAnimatedString2, Globals as G5, callFluidObservers as callFluidObservers2, hasFluidValue, addFluidObserver, removeFluidObserver, getFluidObservers } from \"@react-spring/shared\";\nimport { AnimatedValue, AnimatedString, getPayload, getAnimated as getAnimated2, setAnimated, getAnimatedType } from \"@react-spring/animated\";\n\n// src/AnimationConfig.ts\nimport { is as is2, easings } from \"@react-spring/shared\";\n\n// src/constants.ts\nvar config = {\n  default: {\n    tension: 170,\n    friction: 26\n  },\n  gentle: {\n    tension: 120,\n    friction: 14\n  },\n  wobbly: {\n    tension: 180,\n    friction: 12\n  },\n  stiff: {\n    tension: 210,\n    friction: 20\n  },\n  slow: {\n    tension: 280,\n    friction: 60\n  },\n  molasses: {\n    tension: 280,\n    friction: 120\n  }\n};\n\n// src/AnimationConfig.ts\nvar defaults = {\n  ...config.default,\n  mass: 1,\n  damping: 1,\n  easing: easings.linear,\n  clamp: false\n};\nvar AnimationConfig = class {\n  constructor() {\n    /**\n     * The initial velocity of one or more values.\n     *\n     * @default 0\n     */\n    this.velocity = 0;\n    Object.assign(this, defaults);\n  }\n};\nfunction mergeConfig(config2, newConfig, defaultConfig) {\n  if (defaultConfig) {\n    defaultConfig = {\n      ...defaultConfig\n    };\n    sanitizeConfig(defaultConfig, newConfig);\n    newConfig = {\n      ...defaultConfig,\n      ...newConfig\n    };\n  }\n  sanitizeConfig(config2, newConfig);\n  Object.assign(config2, newConfig);\n  for (const key in defaults) {\n    if (config2[key] == null) {\n      config2[key] = defaults[key];\n    }\n  }\n  let {\n    frequency,\n    damping\n  } = config2;\n  const {\n    mass\n  } = config2;\n  if (!is2.und(frequency)) {\n    if (frequency < 0.01) frequency = 0.01;\n    if (damping < 0) damping = 0;\n    config2.tension = Math.pow(2 * Math.PI / frequency, 2) * mass;\n    config2.friction = 4 * Math.PI * damping * mass / frequency;\n  }\n  return config2;\n}\nfunction sanitizeConfig(config2, props) {\n  if (!is2.und(props.decay)) {\n    config2.duration = void 0;\n  } else {\n    const isTensionConfig = !is2.und(props.tension) || !is2.und(props.friction);\n    if (isTensionConfig || !is2.und(props.frequency) || !is2.und(props.damping) || !is2.und(props.mass)) {\n      config2.duration = void 0;\n      config2.decay = void 0;\n    }\n    if (isTensionConfig) {\n      config2.frequency = void 0;\n    }\n  }\n}\n\n// src/Animation.ts\nvar emptyArray = [];\nvar Animation = class {\n  constructor() {\n    this.changed = false;\n    this.values = emptyArray;\n    this.toValues = null;\n    this.fromValues = emptyArray;\n    this.config = new AnimationConfig();\n    this.immediate = false;\n  }\n};\n\n// src/scheduleProps.ts\nimport { is as is3, raf, Globals as G2 } from \"@react-spring/shared\";\nfunction scheduleProps(callId, {\n  key,\n  props,\n  defaultProps,\n  state,\n  actions\n}) {\n  return new Promise((resolve, reject) => {\n    let delay;\n    let timeout;\n    let cancel = matchProp(props.cancel ?? defaultProps?.cancel, key);\n    if (cancel) {\n      onStart();\n    } else {\n      if (!is3.und(props.pause)) {\n        state.paused = matchProp(props.pause, key);\n      }\n      let pause = defaultProps?.pause;\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key);\n      }\n      delay = callProp(props.delay || 0, key);\n      if (pause) {\n        state.resumeQueue.add(onResume);\n        actions.pause();\n      } else {\n        actions.resume();\n        onResume();\n      }\n    }\n    function onPause() {\n      state.resumeQueue.add(onResume);\n      state.timeouts.delete(timeout);\n      timeout.cancel();\n      delay = timeout.time - raf.now();\n    }\n    function onResume() {\n      if (delay > 0 && !G2.skipAnimation) {\n        state.delayed = true;\n        timeout = raf.setTimeout(onStart, delay);\n        state.pauseQueue.add(onPause);\n        state.timeouts.add(timeout);\n      } else {\n        onStart();\n      }\n    }\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false;\n      }\n      state.pauseQueue.delete(onPause);\n      state.timeouts.delete(timeout);\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true;\n      }\n      try {\n        actions.start({\n          ...props,\n          callId,\n          cancel\n        }, resolve);\n      } catch (err) {\n        reject(err);\n      }\n    }\n  });\n}\n\n// src/runAsync.ts\nimport { is as is4, raf as raf2, flush, eachProp as eachProp2, Globals as G3 } from \"@react-spring/shared\";\n\n// src/AnimationResult.ts\nvar getCombinedResult = (target, results) => results.length == 1 ? results[0] : results.some(result => result.cancelled) ? getCancelledResult(target.get()) : results.every(result => result.noop) ? getNoopResult(target.get()) : getFinishedResult(target.get(), results.every(result => result.finished));\nvar getNoopResult = value => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false\n});\nvar getFinishedResult = (value, finished, cancelled = false) => ({\n  value,\n  finished,\n  cancelled\n});\nvar getCancelledResult = value => ({\n  value,\n  cancelled: true,\n  finished: false\n});\n\n// src/runAsync.ts\nfunction runAsync(to2, props, state, target) {\n  const {\n    callId,\n    parentId,\n    onRest\n  } = props;\n  const {\n    asyncTo: prevTo,\n    promise: prevPromise\n  } = state;\n  if (!parentId && to2 === prevTo && !props.reset) {\n    return prevPromise;\n  }\n  return state.promise = (async () => {\n    state.asyncId = callId;\n    state.asyncTo = to2;\n    const defaultProps = getDefaultProps(props, (value, key) =>\n    // The `onRest` prop is only called when the `runAsync` promise is resolved.\n    key === \"onRest\" ? void 0 : value);\n    let preventBail;\n    let bail;\n    const bailPromise = new Promise((resolve, reject) => (preventBail = resolve, bail = reject));\n    const bailIfEnded = bailSignal => {\n      const bailResult =\n      // The `cancel` prop or `stop` method was used.\n      callId <= (state.cancelId || 0) && getCancelledResult(target) ||\n      // The async `to` prop was replaced.\n      callId !== state.asyncId && getFinishedResult(target, false);\n      if (bailResult) {\n        bailSignal.result = bailResult;\n        bail(bailSignal);\n        throw bailSignal;\n      }\n    };\n    const animate = (arg1, arg2) => {\n      const bailSignal = new BailSignal();\n      const skipAnimationSignal = new SkipAnimationSignal();\n      return (async () => {\n        if (G3.skipAnimation) {\n          stopAsync(state);\n          skipAnimationSignal.result = getFinishedResult(target, false);\n          bail(skipAnimationSignal);\n          throw skipAnimationSignal;\n        }\n        bailIfEnded(bailSignal);\n        const props2 = is4.obj(arg1) ? {\n          ...arg1\n        } : {\n          ...arg2,\n          to: arg1\n        };\n        props2.parentId = callId;\n        eachProp2(defaultProps, (value, key) => {\n          if (is4.und(props2[key])) {\n            props2[key] = value;\n          }\n        });\n        const result2 = await target.start(props2);\n        bailIfEnded(bailSignal);\n        if (state.paused) {\n          await new Promise(resume => {\n            state.resumeQueue.add(resume);\n          });\n        }\n        return result2;\n      })();\n    };\n    let result;\n    if (G3.skipAnimation) {\n      stopAsync(state);\n      return getFinishedResult(target, false);\n    }\n    try {\n      let animating;\n      if (is4.arr(to2)) {\n        animating = (async queue => {\n          for (const props2 of queue) {\n            await animate(props2);\n          }\n        })(to2);\n      } else {\n        animating = Promise.resolve(to2(animate, target.stop.bind(target)));\n      }\n      await Promise.all([animating.then(preventBail), bailPromise]);\n      result = getFinishedResult(target.get(), true, false);\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result;\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result;\n      } else {\n        throw err;\n      }\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId;\n        state.asyncTo = parentId ? prevTo : void 0;\n        state.promise = parentId ? prevPromise : void 0;\n      }\n    }\n    if (is4.fun(onRest)) {\n      raf2.batchedUpdates(() => {\n        onRest(result, target, target.item);\n      });\n    }\n    return result;\n  })();\n}\nfunction stopAsync(state, cancelId) {\n  flush(state.timeouts, t => t.cancel());\n  state.pauseQueue.clear();\n  state.resumeQueue.clear();\n  state.asyncId = state.asyncTo = state.promise = void 0;\n  if (cancelId) state.cancelId = cancelId;\n}\nvar BailSignal = class extends Error {\n  constructor() {\n    super(\"An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.\");\n  }\n};\nvar SkipAnimationSignal = class extends Error {\n  constructor() {\n    super(\"SkipAnimationSignal\");\n  }\n};\n\n// src/FrameValue.ts\nimport { deprecateInterpolate, frameLoop, FluidValue as FluidValue2, Globals as G4, callFluidObservers } from \"@react-spring/shared\";\nimport { getAnimated } from \"@react-spring/animated\";\nvar isFrameValue = value => value instanceof FrameValue;\nvar nextId = 1;\nvar FrameValue = class extends FluidValue2 {\n  constructor() {\n    super(...arguments);\n    this.id = nextId++;\n    this._priority = 0;\n  }\n  get priority() {\n    return this._priority;\n  }\n  set priority(priority) {\n    if (this._priority != priority) {\n      this._priority = priority;\n      this._onPriorityChange(priority);\n    }\n  }\n  /** Get the current value */\n  get() {\n    const node = getAnimated(this);\n    return node && node.getValue();\n  }\n  /** Create a spring that maps our value to another value */\n  to(...args) {\n    return G4.to(this, args);\n  }\n  /** @deprecated Use the `to` method instead. */\n  interpolate(...args) {\n    deprecateInterpolate();\n    return G4.to(this, args);\n  }\n  toJSON() {\n    return this.get();\n  }\n  observerAdded(count) {\n    if (count == 1) this._attach();\n  }\n  observerRemoved(count) {\n    if (count == 0) this._detach();\n  }\n  /** Called when the first child is added. */\n  _attach() {}\n  /** Called when the last child is removed. */\n  _detach() {}\n  /** Tell our children about our new value */\n  _onChange(value, idle = false) {\n    callFluidObservers(this, {\n      type: \"change\",\n      parent: this,\n      value,\n      idle\n    });\n  }\n  /** Tell our children about our new priority */\n  _onPriorityChange(priority) {\n    if (!this.idle) {\n      frameLoop.sort(this);\n    }\n    callFluidObservers(this, {\n      type: \"priority\",\n      parent: this,\n      priority\n    });\n  }\n};\n\n// src/SpringPhase.ts\nvar $P = Symbol.for(\"SpringPhase\");\nvar HAS_ANIMATED = 1;\nvar IS_ANIMATING = 2;\nvar IS_PAUSED = 4;\nvar hasAnimated = target => (target[$P] & HAS_ANIMATED) > 0;\nvar isAnimating = target => (target[$P] & IS_ANIMATING) > 0;\nvar isPaused = target => (target[$P] & IS_PAUSED) > 0;\nvar setActiveBit = (target, active) => active ? target[$P] |= IS_ANIMATING | HAS_ANIMATED : target[$P] &= ~IS_ANIMATING;\nvar setPausedBit = (target, paused) => paused ? target[$P] |= IS_PAUSED : target[$P] &= ~IS_PAUSED;\n\n// src/SpringValue.ts\nvar SpringValue = class extends FrameValue {\n  constructor(arg1, arg2) {\n    super();\n    /** The animation state */\n    this.animation = new Animation();\n    /** Some props have customizable default values */\n    this.defaultProps = {};\n    /** The state for `runAsync` calls */\n    this._state = {\n      paused: false,\n      delayed: false,\n      pauseQueue: /* @__PURE__ */new Set(),\n      resumeQueue: /* @__PURE__ */new Set(),\n      timeouts: /* @__PURE__ */new Set()\n    };\n    /** The promise resolvers of pending `start` calls */\n    this._pendingCalls = /* @__PURE__ */new Set();\n    /** The counter for tracking `scheduleProps` calls */\n    this._lastCallId = 0;\n    /** The last `scheduleProps` call that changed the `to` prop */\n    this._lastToId = 0;\n    this._memoizedDuration = 0;\n    if (!is5.und(arg1) || !is5.und(arg2)) {\n      const props = is5.obj(arg1) ? {\n        ...arg1\n      } : {\n        ...arg2,\n        from: arg1\n      };\n      if (is5.und(props.default)) {\n        props.default = true;\n      }\n      this.start(props);\n    }\n  }\n  /** Equals true when not advancing on each frame. */\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this);\n  }\n  get goal() {\n    return getFluidValue2(this.animation.to);\n  }\n  get velocity() {\n    const node = getAnimated2(this);\n    return node instanceof AnimatedValue ? node.lastVelocity || 0 : node.getPayload().map(node2 => node2.lastVelocity || 0);\n  }\n  /**\n   * When true, this value has been animated at least once.\n   */\n  get hasAnimated() {\n    return hasAnimated(this);\n  }\n  /**\n   * When true, this value has an unfinished animation,\n   * which is either active or paused.\n   */\n  get isAnimating() {\n    return isAnimating(this);\n  }\n  /**\n   * When true, all current and future animations are paused.\n   */\n  get isPaused() {\n    return isPaused(this);\n  }\n  /**\n   *\n   *\n   */\n  get isDelayed() {\n    return this._state.delayed;\n  }\n  /** Advance the current animation by a number of milliseconds */\n  advance(dt) {\n    let idle = true;\n    let changed = false;\n    const anim = this.animation;\n    let {\n      toValues\n    } = anim;\n    const {\n      config: config2\n    } = anim;\n    const payload = getPayload(anim.to);\n    if (!payload && hasFluidValue(anim.to)) {\n      toValues = toArray2(getFluidValue2(anim.to));\n    }\n    anim.values.forEach((node2, i) => {\n      if (node2.done) return;\n      const to2 =\n      // Animated strings always go from 0 to 1.\n      node2.constructor == AnimatedString ? 1 : payload ? payload[i].lastPosition : toValues[i];\n      let finished = anim.immediate;\n      let position = to2;\n      if (!finished) {\n        position = node2.lastPosition;\n        if (config2.tension <= 0) {\n          node2.done = true;\n          return;\n        }\n        let elapsed = node2.elapsedTime += dt;\n        const from = anim.fromValues[i];\n        const v0 = node2.v0 != null ? node2.v0 : node2.v0 = is5.arr(config2.velocity) ? config2.velocity[i] : config2.velocity;\n        let velocity;\n        const precision = config2.precision || (from == to2 ? 5e-3 : Math.min(1, Math.abs(to2 - from) * 1e-3));\n        if (!is5.und(config2.duration)) {\n          let p = 1;\n          if (config2.duration > 0) {\n            if (this._memoizedDuration !== config2.duration) {\n              this._memoizedDuration = config2.duration;\n              if (node2.durationProgress > 0) {\n                node2.elapsedTime = config2.duration * node2.durationProgress;\n                elapsed = node2.elapsedTime += dt;\n              }\n            }\n            p = (config2.progress || 0) + elapsed / this._memoizedDuration;\n            p = p > 1 ? 1 : p < 0 ? 0 : p;\n            node2.durationProgress = p;\n          }\n          position = from + config2.easing(p) * (to2 - from);\n          velocity = (position - node2.lastPosition) / dt;\n          finished = p == 1;\n        } else if (config2.decay) {\n          const decay = config2.decay === true ? 0.998 : config2.decay;\n          const e = Math.exp(-(1 - decay) * elapsed);\n          position = from + v0 / (1 - decay) * (1 - e);\n          finished = Math.abs(node2.lastPosition - position) <= precision;\n          velocity = v0 * e;\n        } else {\n          velocity = node2.lastVelocity == null ? v0 : node2.lastVelocity;\n          const restVelocity = config2.restVelocity || precision / 10;\n          const bounceFactor = config2.clamp ? 0 : config2.bounce;\n          const canBounce = !is5.und(bounceFactor);\n          const isGrowing = from == to2 ? node2.v0 > 0 : from < to2;\n          let isMoving;\n          let isBouncing = false;\n          const step = 1;\n          const numSteps = Math.ceil(dt / step);\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity;\n            if (!isMoving) {\n              finished = Math.abs(to2 - position) <= precision;\n              if (finished) {\n                break;\n              }\n            }\n            if (canBounce) {\n              isBouncing = position == to2 || position > to2 == isGrowing;\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor;\n                position = to2;\n              }\n            }\n            const springForce = -config2.tension * 1e-6 * (position - to2);\n            const dampingForce = -config2.friction * 1e-3 * velocity;\n            const acceleration = (springForce + dampingForce) / config2.mass;\n            velocity = velocity + acceleration * step;\n            position = position + velocity * step;\n          }\n        }\n        node2.lastVelocity = velocity;\n        if (Number.isNaN(position)) {\n          console.warn(`Got NaN while animating:`, this);\n          finished = true;\n        }\n      }\n      if (payload && !payload[i].done) {\n        finished = false;\n      }\n      if (finished) {\n        node2.done = true;\n      } else {\n        idle = false;\n      }\n      if (node2.setValue(position, config2.round)) {\n        changed = true;\n      }\n    });\n    const node = getAnimated2(this);\n    const currVal = node.getValue();\n    if (idle) {\n      const finalVal = getFluidValue2(anim.to);\n      if ((currVal !== finalVal || changed) && !config2.decay) {\n        node.setValue(finalVal);\n        this._onChange(finalVal);\n      } else if (changed && config2.decay) {\n        this._onChange(currVal);\n      }\n      this._stop();\n    } else if (changed) {\n      this._onChange(currVal);\n    }\n  }\n  /** Set the current value, while stopping the current animation */\n  set(value) {\n    raf3.batchedUpdates(() => {\n      this._stop();\n      this._focus(value);\n      this._set(value);\n    });\n    return this;\n  }\n  /**\n   * Freeze the active animation in time, as well as any updates merged\n   * before `resume` is called.\n   */\n  pause() {\n    this._update({\n      pause: true\n    });\n  }\n  /** Resume the animation if paused. */\n  resume() {\n    this._update({\n      pause: false\n    });\n  }\n  /** Skip to the end of the current animation. */\n  finish() {\n    if (isAnimating(this)) {\n      const {\n        to: to2,\n        config: config2\n      } = this.animation;\n      raf3.batchedUpdates(() => {\n        this._onStart();\n        if (!config2.decay) {\n          this._set(to2, false);\n        }\n        this._stop();\n      });\n    }\n    return this;\n  }\n  /** Push props into the pending queue. */\n  update(props) {\n    const queue = this.queue || (this.queue = []);\n    queue.push(props);\n    return this;\n  }\n  start(to2, arg2) {\n    let queue;\n    if (!is5.und(to2)) {\n      queue = [is5.obj(to2) ? to2 : {\n        ...arg2,\n        to: to2\n      }];\n    } else {\n      queue = this.queue || [];\n      this.queue = [];\n    }\n    return Promise.all(queue.map(props => {\n      const up = this._update(props);\n      return up;\n    })).then(results => getCombinedResult(this, results));\n  }\n  /**\n   * Stop the current animation, and cancel any delayed updates.\n   *\n   * Pass `true` to call `onRest` with `cancelled: true`.\n   */\n  stop(cancel) {\n    const {\n      to: to2\n    } = this.animation;\n    this._focus(this.get());\n    stopAsync(this._state, cancel && this._lastCallId);\n    raf3.batchedUpdates(() => this._stop(to2, cancel));\n    return this;\n  }\n  /** Restart the animation. */\n  reset() {\n    this._update({\n      reset: true\n    });\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._start();\n    } else if (event.type == \"priority\") {\n      this.priority = event.priority + 1;\n    }\n  }\n  /**\n   * Parse the `to` and `from` range from the given `props` object.\n   *\n   * This also ensures the initial value is available to animated components\n   * during the render phase.\n   */\n  _prepareNode(props) {\n    const key = this.key || \"\";\n    let {\n      to: to2,\n      from\n    } = props;\n    to2 = is5.obj(to2) ? to2[key] : to2;\n    if (to2 == null || isAsyncTo(to2)) {\n      to2 = void 0;\n    }\n    from = is5.obj(from) ? from[key] : from;\n    if (from == null) {\n      from = void 0;\n    }\n    const range = {\n      to: to2,\n      from\n    };\n    if (!hasAnimated(this)) {\n      if (props.reverse) [to2, from] = [from, to2];\n      from = getFluidValue2(from);\n      if (!is5.und(from)) {\n        this._set(from);\n      } else if (!getAnimated2(this)) {\n        this._set(to2);\n      }\n    }\n    return range;\n  }\n  /** Every update is processed by this method before merging. */\n  _update({\n    ...props\n  }, isLoop) {\n    const {\n      key,\n      defaultProps\n    } = this;\n    if (props.default) Object.assign(defaultProps, getDefaultProps(props, (value, prop) => /^on/.test(prop) ? resolveProp(value, key) : value));\n    mergeActiveFn(this, props, \"onProps\");\n    sendEvent(this, \"onProps\", props, this);\n    const range = this._prepareNode(props);\n    if (Object.isFrozen(this)) {\n      throw Error(\"Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?\");\n    }\n    const state = this._state;\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true);\n            flushCalls(state.pauseQueue);\n            sendEvent(this, \"onPause\", getFinishedResult(this, checkFinished(this, this.animation.to)), this);\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false);\n            if (isAnimating(this)) {\n              this._resume();\n            }\n            flushCalls(state.resumeQueue);\n            sendEvent(this, \"onResume\", getFinishedResult(this, checkFinished(this, this.animation.to)), this);\n          }\n        },\n        start: this._merge.bind(this, range)\n      }\n    }).then(result => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props);\n        if (nextProps) {\n          return this._update(nextProps, true);\n        }\n      }\n      return result;\n    });\n  }\n  /** Merge props into the current animation */\n  _merge(range, props, resolve) {\n    if (props.cancel) {\n      this.stop(true);\n      return resolve(getCancelledResult(this));\n    }\n    const hasToProp = !is5.und(range.to);\n    const hasFromProp = !is5.und(range.from);\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId;\n      } else {\n        return resolve(getCancelledResult(this));\n      }\n    }\n    const {\n      key,\n      defaultProps,\n      animation: anim\n    } = this;\n    const {\n      to: prevTo,\n      from: prevFrom\n    } = anim;\n    let {\n      to: to2 = prevTo,\n      from = prevFrom\n    } = range;\n    if (hasFromProp && !hasToProp && (!props.default || is5.und(to2))) {\n      to2 = from;\n    }\n    if (props.reverse) [to2, from] = [from, to2];\n    const hasFromChanged = !isEqual(from, prevFrom);\n    if (hasFromChanged) {\n      anim.from = from;\n    }\n    from = getFluidValue2(from);\n    const hasToChanged = !isEqual(to2, prevTo);\n    if (hasToChanged) {\n      this._focus(to2);\n    }\n    const hasAsyncTo = isAsyncTo(props.to);\n    const {\n      config: config2\n    } = anim;\n    const {\n      decay,\n      velocity\n    } = config2;\n    if (hasToProp || hasFromProp) {\n      config2.velocity = 0;\n    }\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(config2, callProp(props.config, key),\n      // Avoid calling the same \"config\" prop twice.\n      props.config !== defaultProps.config ? callProp(defaultProps.config, key) : void 0);\n    }\n    let node = getAnimated2(this);\n    if (!node || is5.und(to2)) {\n      return resolve(getFinishedResult(this, true));\n    }\n    const reset =\n    // When `reset` is undefined, the `from` prop implies `reset: true`,\n    // except for declarative updates. When `reset` is defined, there\n    // must exist a value to animate from.\n    is5.und(props.reset) ? hasFromProp && !props.default : !is5.und(from) && matchProp(props.reset, key);\n    const value = reset ? from : this.get();\n    const goal = computeGoal(to2);\n    const isAnimatable = is5.num(goal) || is5.arr(goal) || isAnimatedString2(goal);\n    const immediate = !hasAsyncTo && (!isAnimatable || matchProp(defaultProps.immediate || props.immediate, key));\n    if (hasToChanged) {\n      const nodeType = getAnimatedType(to2);\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal);\n        } else throw Error(`Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the \"to\" prop suggests`);\n      }\n    }\n    const goalType = node.constructor;\n    let started = hasFluidValue(to2);\n    let finished = false;\n    if (!started) {\n      const hasValueChanged = reset || !hasAnimated(this) && hasFromChanged;\n      if (hasToChanged || hasValueChanged) {\n        finished = isEqual(computeGoal(value), goal);\n        started = !finished;\n      }\n      if (!isEqual(anim.immediate, immediate) && !immediate || !isEqual(config2.decay, decay) || !isEqual(config2.velocity, velocity)) {\n        started = true;\n      }\n    }\n    if (finished && isAnimating(this)) {\n      if (anim.changed && !reset) {\n        started = true;\n      } else if (!started) {\n        this._stop(prevTo);\n      }\n    }\n    if (!hasAsyncTo) {\n      if (started || hasFluidValue(prevTo)) {\n        anim.values = node.getPayload();\n        anim.toValues = hasFluidValue(to2) ? null : goalType == AnimatedString ? [1] : toArray2(goal);\n      }\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate;\n        if (!immediate && !reset) {\n          this._set(prevTo);\n        }\n      }\n      if (started) {\n        const {\n          onRest\n        } = anim;\n        each2(ACTIVE_EVENTS, type => mergeActiveFn(this, props, type));\n        const result = getFinishedResult(this, checkFinished(this, prevTo));\n        flushCalls(this._pendingCalls, result);\n        this._pendingCalls.add(resolve);\n        if (anim.changed) raf3.batchedUpdates(() => {\n          anim.changed = !reset;\n          onRest?.(result, this);\n          if (reset) {\n            callProp(defaultProps.onRest, result);\n          } else {\n            anim.onStart?.(result, this);\n          }\n        });\n      }\n    }\n    if (reset) {\n      this._set(value);\n    }\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this));\n    } else if (started) {\n      this._start();\n    } else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve);\n    } else {\n      resolve(getNoopResult(value));\n    }\n  }\n  /** Update the `animation.to` value, which might be a `FluidValue` */\n  _focus(value) {\n    const anim = this.animation;\n    if (value !== anim.to) {\n      if (getFluidObservers(this)) {\n        this._detach();\n      }\n      anim.to = value;\n      if (getFluidObservers(this)) {\n        this._attach();\n      }\n    }\n  }\n  _attach() {\n    let priority = 0;\n    const {\n      to: to2\n    } = this.animation;\n    if (hasFluidValue(to2)) {\n      addFluidObserver(to2, this);\n      if (isFrameValue(to2)) {\n        priority = to2.priority + 1;\n      }\n    }\n    this.priority = priority;\n  }\n  _detach() {\n    const {\n      to: to2\n    } = this.animation;\n    if (hasFluidValue(to2)) {\n      removeFluidObserver(to2, this);\n    }\n  }\n  /**\n   * Update the current value from outside the frameloop,\n   * and return the `Animated` node.\n   */\n  _set(arg, idle = true) {\n    const value = getFluidValue2(arg);\n    if (!is5.und(value)) {\n      const oldNode = getAnimated2(this);\n      if (!oldNode || !isEqual(value, oldNode.getValue())) {\n        const nodeType = getAnimatedType(value);\n        if (!oldNode || oldNode.constructor != nodeType) {\n          setAnimated(this, nodeType.create(value));\n        } else {\n          oldNode.setValue(value);\n        }\n        if (oldNode) {\n          raf3.batchedUpdates(() => {\n            this._onChange(value, idle);\n          });\n        }\n      }\n    }\n    return getAnimated2(this);\n  }\n  _onStart() {\n    const anim = this.animation;\n    if (!anim.changed) {\n      anim.changed = true;\n      sendEvent(this, \"onStart\", getFinishedResult(this, checkFinished(this, anim.to)), this);\n    }\n  }\n  _onChange(value, idle) {\n    if (!idle) {\n      this._onStart();\n      callProp(this.animation.onChange, value, this);\n    }\n    callProp(this.defaultProps.onChange, value, this);\n    super._onChange(value, idle);\n  }\n  // This method resets the animation state (even if already animating) to\n  // ensure the latest from/to range is used, and it also ensures this spring\n  // is added to the frameloop.\n  _start() {\n    const anim = this.animation;\n    getAnimated2(this).reset(getFluidValue2(anim.to));\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map(node => node.lastPosition);\n    }\n    if (!isAnimating(this)) {\n      setActiveBit(this, true);\n      if (!isPaused(this)) {\n        this._resume();\n      }\n    }\n  }\n  _resume() {\n    if (G5.skipAnimation) {\n      this.finish();\n    } else {\n      frameLoop2.start(this);\n    }\n  }\n  /**\n   * Exit the frameloop and notify `onRest` listeners.\n   *\n   * Always wrap `_stop` calls with `batchedUpdates`.\n   */\n  _stop(goal, cancel) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false);\n      const anim = this.animation;\n      each2(anim.values, node => {\n        node.done = true;\n      });\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = void 0;\n      }\n      callFluidObservers2(this, {\n        type: \"idle\",\n        parent: this\n      });\n      const result = cancel ? getCancelledResult(this.get()) : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to));\n      flushCalls(this._pendingCalls, result);\n      if (anim.changed) {\n        anim.changed = false;\n        sendEvent(this, \"onRest\", result, this);\n      }\n    }\n  }\n};\nfunction checkFinished(target, to2) {\n  const goal = computeGoal(to2);\n  const value = computeGoal(target.get());\n  return isEqual(value, goal);\n}\nfunction createLoopUpdate(props, loop = props.loop, to2 = props.to) {\n  const loopRet = callProp(loop);\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet);\n    const reverse = (overrides || props).reverse;\n    const reset = !overrides || overrides.reset;\n    return createUpdate({\n      ...props,\n      loop,\n      // Avoid updating default props when looping.\n      default: false,\n      // Never loop the `pause` prop.\n      pause: void 0,\n      // For the \"reverse\" prop to loop as expected, the \"to\" prop\n      // must be undefined. The \"reverse\" prop is ignored when the\n      // \"to\" prop is an array or function.\n      to: !reverse || isAsyncTo(to2) ? to2 : void 0,\n      // Ignore the \"from\" prop except on reset.\n      from: reset ? props.from : void 0,\n      reset,\n      // The \"loop\" prop can return a \"useSpring\" props object to\n      // override any of the original props.\n      ...overrides\n    });\n  }\n}\nfunction createUpdate(props) {\n  const {\n    to: to2,\n    from\n  } = props = inferTo(props);\n  const keys = /* @__PURE__ */new Set();\n  if (is5.obj(to2)) findDefined(to2, keys);\n  if (is5.obj(from)) findDefined(from, keys);\n  props.keys = keys.size ? Array.from(keys) : null;\n  return props;\n}\nfunction declareUpdate(props) {\n  const update2 = createUpdate(props);\n  if (is5.und(update2.default)) {\n    update2.default = getDefaultProps(update2);\n  }\n  return update2;\n}\nfunction findDefined(values, keys) {\n  eachProp3(values, (value, key) => value != null && keys.add(key));\n}\nvar ACTIVE_EVENTS = [\"onStart\", \"onRest\", \"onChange\", \"onPause\", \"onResume\"];\nfunction mergeActiveFn(target, props, type) {\n  target.animation[type] = props[type] !== getDefaultProp(props, type) ? resolveProp(props[type], target.key) : void 0;\n}\nfunction sendEvent(target, type, ...args) {\n  target.animation[type]?.(...args);\n  target.defaultProps[type]?.(...args);\n}\n\n// src/Controller.ts\nimport { is as is6, raf as raf4, each as each3, noop, flush as flush2, toArray as toArray3, eachProp as eachProp4, flushCalls as flushCalls2, addFluidObserver as addFluidObserver2 } from \"@react-spring/shared\";\nvar BATCHED_EVENTS = [\"onStart\", \"onChange\", \"onRest\"];\nvar nextId2 = 1;\nvar Controller = class {\n  constructor(props, flush3) {\n    this.id = nextId2++;\n    /** The animated values */\n    this.springs = {};\n    /** The queue of props passed to the `update` method. */\n    this.queue = [];\n    /** The counter for tracking `scheduleProps` calls */\n    this._lastAsyncId = 0;\n    /** The values currently being animated */\n    this._active = /* @__PURE__ */new Set();\n    /** The values that changed recently */\n    this._changed = /* @__PURE__ */new Set();\n    /** Equals false when `onStart` listeners can be called */\n    this._started = false;\n    /** State used by the `runAsync` function */\n    this._state = {\n      paused: false,\n      pauseQueue: /* @__PURE__ */new Set(),\n      resumeQueue: /* @__PURE__ */new Set(),\n      timeouts: /* @__PURE__ */new Set()\n    };\n    /** The event queues that are flushed once per frame maximum */\n    this._events = {\n      onStart: /* @__PURE__ */new Map(),\n      onChange: /* @__PURE__ */new Map(),\n      onRest: /* @__PURE__ */new Map()\n    };\n    this._onFrame = this._onFrame.bind(this);\n    if (flush3) {\n      this._flush = flush3;\n    }\n    if (props) {\n      this.start({\n        default: true,\n        ...props\n      });\n    }\n  }\n  /**\n   * Equals `true` when no spring values are in the frameloop, and\n   * no async animation is currently active.\n   */\n  get idle() {\n    return !this._state.asyncTo && Object.values(this.springs).every(spring => {\n      return spring.idle && !spring.isDelayed && !spring.isPaused;\n    });\n  }\n  get item() {\n    return this._item;\n  }\n  set item(item) {\n    this._item = item;\n  }\n  /** Get the current values of our springs */\n  get() {\n    const values = {};\n    this.each((spring, key) => values[key] = spring.get());\n    return values;\n  }\n  /** Set the current values without animating. */\n  set(values) {\n    for (const key in values) {\n      const value = values[key];\n      if (!is6.und(value)) {\n        this.springs[key].set(value);\n      }\n    }\n  }\n  /** Push an update onto the queue of each value. */\n  update(props) {\n    if (props) {\n      this.queue.push(createUpdate(props));\n    }\n    return this;\n  }\n  /**\n   * Start the queued animations for every spring, and resolve the returned\n   * promise once all queued animations have finished or been cancelled.\n   *\n   * When you pass a queue (instead of nothing), that queue is used instead of\n   * the queued animations added with the `update` method, which are left alone.\n   */\n  start(props) {\n    let {\n      queue\n    } = this;\n    if (props) {\n      queue = toArray3(props).map(createUpdate);\n    } else {\n      this.queue = [];\n    }\n    if (this._flush) {\n      return this._flush(this, queue);\n    }\n    prepareKeys(this, queue);\n    return flushUpdateQueue(this, queue);\n  }\n  /** @internal */\n  stop(arg, keys) {\n    if (arg !== !!arg) {\n      keys = arg;\n    }\n    if (keys) {\n      const springs = this.springs;\n      each3(toArray3(keys), key => springs[key].stop(!!arg));\n    } else {\n      stopAsync(this._state, this._lastAsyncId);\n      this.each(spring => spring.stop(!!arg));\n    }\n    return this;\n  }\n  /** Freeze the active animation in time */\n  pause(keys) {\n    if (is6.und(keys)) {\n      this.start({\n        pause: true\n      });\n    } else {\n      const springs = this.springs;\n      each3(toArray3(keys), key => springs[key].pause());\n    }\n    return this;\n  }\n  /** Resume the animation if paused. */\n  resume(keys) {\n    if (is6.und(keys)) {\n      this.start({\n        pause: false\n      });\n    } else {\n      const springs = this.springs;\n      each3(toArray3(keys), key => springs[key].resume());\n    }\n    return this;\n  }\n  /** Call a function once per spring value */\n  each(iterator) {\n    eachProp4(this.springs, iterator);\n  }\n  /** @internal Called at the end of every animation frame */\n  _onFrame() {\n    const {\n      onStart,\n      onChange,\n      onRest\n    } = this._events;\n    const active = this._active.size > 0;\n    const changed = this._changed.size > 0;\n    if (active && !this._started || changed && !this._started) {\n      this._started = true;\n      flush2(onStart, ([onStart2, result]) => {\n        result.value = this.get();\n        onStart2(result, this, this._item);\n      });\n    }\n    const idle = !active && this._started;\n    const values = changed || idle && onRest.size ? this.get() : null;\n    if (changed && onChange.size) {\n      flush2(onChange, ([onChange2, result]) => {\n        result.value = values;\n        onChange2(result, this, this._item);\n      });\n    }\n    if (idle) {\n      this._started = false;\n      flush2(onRest, ([onRest2, result]) => {\n        result.value = values;\n        onRest2(result, this, this._item);\n      });\n    }\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._changed.add(event.parent);\n      if (!event.idle) {\n        this._active.add(event.parent);\n      }\n    } else if (event.type == \"idle\") {\n      this._active.delete(event.parent);\n    } else return;\n    raf4.onFrame(this._onFrame);\n  }\n};\nfunction flushUpdateQueue(ctrl, queue) {\n  return Promise.all(queue.map(props => flushUpdate(ctrl, props))).then(results => getCombinedResult(ctrl, results));\n}\nasync function flushUpdate(ctrl, props, isLoop) {\n  const {\n    keys,\n    to: to2,\n    from,\n    loop,\n    onRest,\n    onResolve\n  } = props;\n  const defaults2 = is6.obj(props.default) && props.default;\n  if (loop) {\n    props.loop = false;\n  }\n  if (to2 === false) props.to = null;\n  if (from === false) props.from = null;\n  const asyncTo = is6.arr(to2) || is6.fun(to2) ? to2 : void 0;\n  if (asyncTo) {\n    props.to = void 0;\n    props.onRest = void 0;\n    if (defaults2) {\n      defaults2.onRest = void 0;\n    }\n  } else {\n    each3(BATCHED_EVENTS, key => {\n      const handler = props[key];\n      if (is6.fun(handler)) {\n        const queue = ctrl[\"_events\"][key];\n        props[key] = ({\n          finished,\n          cancelled\n        }) => {\n          const result2 = queue.get(handler);\n          if (result2) {\n            if (!finished) result2.finished = false;\n            if (cancelled) result2.cancelled = true;\n          } else {\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false\n            });\n          }\n        };\n        if (defaults2) {\n          defaults2[key] = props[key];\n        }\n      }\n    });\n  }\n  const state = ctrl[\"_state\"];\n  if (props.pause === !state.paused) {\n    state.paused = props.pause;\n    flushCalls2(props.pause ? state.pauseQueue : state.resumeQueue);\n  } else if (state.paused) {\n    props.pause = true;\n  }\n  const promises = (keys || Object.keys(ctrl.springs)).map(key => ctrl.springs[key].start(props));\n  const cancel = props.cancel === true || getDefaultProp(props, \"cancel\") === true;\n  if (asyncTo || cancel && state.asyncId) {\n    promises.push(scheduleProps(++ctrl[\"_lastAsyncId\"], {\n      props,\n      state,\n      actions: {\n        pause: noop,\n        resume: noop,\n        start(props2, resolve) {\n          if (cancel) {\n            stopAsync(state, ctrl[\"_lastAsyncId\"]);\n            resolve(getCancelledResult(ctrl));\n          } else {\n            props2.onRest = onRest;\n            resolve(runAsync(asyncTo, props2, state, ctrl));\n          }\n        }\n      }\n    }));\n  }\n  if (state.paused) {\n    await new Promise(resume => {\n      state.resumeQueue.add(resume);\n    });\n  }\n  const result = getCombinedResult(ctrl, await Promise.all(promises));\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to2);\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps]);\n      return flushUpdate(ctrl, nextProps, true);\n    }\n  }\n  if (onResolve) {\n    raf4.batchedUpdates(() => onResolve(result, ctrl, ctrl.item));\n  }\n  return result;\n}\nfunction getSprings(ctrl, props) {\n  const springs = {\n    ...ctrl.springs\n  };\n  if (props) {\n    each3(toArray3(props), props2 => {\n      if (is6.und(props2.keys)) {\n        props2 = createUpdate(props2);\n      }\n      if (!is6.obj(props2.to)) {\n        props2 = {\n          ...props2,\n          to: void 0\n        };\n      }\n      prepareSprings(springs, props2, key => {\n        return createSpring(key);\n      });\n    });\n  }\n  setSprings(ctrl, springs);\n  return springs;\n}\nfunction setSprings(ctrl, springs) {\n  eachProp4(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring;\n      addFluidObserver2(spring, ctrl);\n    }\n  });\n}\nfunction createSpring(key, observer) {\n  const spring = new SpringValue();\n  spring.key = key;\n  if (observer) {\n    addFluidObserver2(spring, observer);\n  }\n  return spring;\n}\nfunction prepareSprings(springs, props, create) {\n  if (props.keys) {\n    each3(props.keys, key => {\n      const spring = springs[key] || (springs[key] = create(key));\n      spring[\"_prepareNode\"](props);\n    });\n  }\n}\nfunction prepareKeys(ctrl, queue) {\n  each3(queue, props => {\n    prepareSprings(ctrl.springs, props, key => {\n      return createSpring(key, ctrl);\n    });\n  });\n}\n\n// src/SpringContext.tsx\nimport * as React from \"react\";\nimport { useContext } from \"react\";\nimport { useMemoOne } from \"@react-spring/shared\";\nvar SpringContext = ({\n  children,\n  ...props\n}) => {\n  const inherited = useContext(ctx);\n  const pause = props.pause || !!inherited.pause,\n    immediate = props.immediate || !!inherited.immediate;\n  props = useMemoOne(() => ({\n    pause,\n    immediate\n  }), [pause, immediate]);\n  const {\n    Provider\n  } = ctx;\n  return /* @__PURE__ */React.createElement(Provider, {\n    value: props\n  }, children);\n};\nvar ctx = makeContext(SpringContext, {});\nSpringContext.Provider = ctx.Provider;\nSpringContext.Consumer = ctx.Consumer;\nfunction makeContext(target, init) {\n  Object.assign(target, React.createContext(init));\n  target.Provider._context = target;\n  target.Consumer._context = target;\n  return target;\n}\n\n// src/SpringRef.ts\nimport { each as each4, is as is7, deprecateDirectCall } from \"@react-spring/shared\";\nvar SpringRef = () => {\n  const current = [];\n  const SpringRef2 = function (props) {\n    deprecateDirectCall();\n    const results = [];\n    each4(current, (ctrl, i) => {\n      if (is7.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update2 = _getProps(props, ctrl, i);\n        if (update2) {\n          results.push(ctrl.start(update2));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef2.current = current;\n  SpringRef2.add = function (ctrl) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl);\n    }\n  };\n  SpringRef2.delete = function (ctrl) {\n    const i = current.indexOf(ctrl);\n    if (~i) current.splice(i, 1);\n  };\n  SpringRef2.pause = function () {\n    each4(current, ctrl => ctrl.pause(...arguments));\n    return this;\n  };\n  SpringRef2.resume = function () {\n    each4(current, ctrl => ctrl.resume(...arguments));\n    return this;\n  };\n  SpringRef2.set = function (values) {\n    each4(current, (ctrl, i) => {\n      const update2 = is7.fun(values) ? values(i, ctrl) : values;\n      if (update2) {\n        ctrl.set(update2);\n      }\n    });\n  };\n  SpringRef2.start = function (props) {\n    const results = [];\n    each4(current, (ctrl, i) => {\n      if (is7.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update2 = this._getProps(props, ctrl, i);\n        if (update2) {\n          results.push(ctrl.start(update2));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef2.stop = function () {\n    each4(current, ctrl => ctrl.stop(...arguments));\n    return this;\n  };\n  SpringRef2.update = function (props) {\n    each4(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)));\n    return this;\n  };\n  const _getProps = function (arg, ctrl, index) {\n    return is7.fun(arg) ? arg(index, ctrl) : arg;\n  };\n  SpringRef2._getProps = _getProps;\n  return SpringRef2;\n};\n\n// src/hooks/useSprings.ts\nfunction useSprings(length, props, deps) {\n  const propsFn = is8.fun(props) && props;\n  if (propsFn && !deps) deps = [];\n  const ref = useMemo(() => propsFn || arguments.length == 3 ? SpringRef() : void 0, []);\n  const layoutId = useRef(0);\n  const forceUpdate = useForceUpdate();\n  const state = useMemo(() => ({\n    ctrls: [],\n    queue: [],\n    flush(ctrl, updates2) {\n      const springs2 = getSprings(ctrl, updates2);\n      const canFlushSync = layoutId.current > 0 && !state.queue.length && !Object.keys(springs2).some(key => !ctrl.springs[key]);\n      return canFlushSync ? flushUpdateQueue(ctrl, updates2) : new Promise(resolve => {\n        setSprings(ctrl, springs2);\n        state.queue.push(() => {\n          resolve(flushUpdateQueue(ctrl, updates2));\n        });\n        forceUpdate();\n      });\n    }\n  }), []);\n  const ctrls = useRef([...state.ctrls]);\n  const updates = [];\n  const prevLength = usePrev(length) || 0;\n  useMemo(() => {\n    each5(ctrls.current.slice(length, prevLength), ctrl => {\n      detachRefs(ctrl, ref);\n      ctrl.stop(true);\n    });\n    ctrls.current.length = length;\n    declareUpdates(prevLength, length);\n  }, [length]);\n  useMemo(() => {\n    declareUpdates(0, Math.min(prevLength, length));\n  }, deps);\n  function declareUpdates(startIndex, endIndex) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl = ctrls.current[i] || (ctrls.current[i] = new Controller(null, state.flush));\n      const update2 = propsFn ? propsFn(i, ctrl) : props[i];\n      if (update2) {\n        updates[i] = declareUpdate(update2);\n      }\n    }\n  }\n  const springs = ctrls.current.map((ctrl, i) => getSprings(ctrl, updates[i]));\n  const context = useContext2(SpringContext);\n  const prevContext = usePrev(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  useIsomorphicLayoutEffect2(() => {\n    layoutId.current++;\n    state.ctrls = ctrls.current;\n    const {\n      queue\n    } = state;\n    if (queue.length) {\n      state.queue = [];\n      each5(queue, cb => cb());\n    }\n    each5(ctrls.current, (ctrl, i) => {\n      ref?.add(ctrl);\n      if (hasContext) {\n        ctrl.start({\n          default: context\n        });\n      }\n      const update2 = updates[i];\n      if (update2) {\n        replaceRef(ctrl, update2.ref);\n        if (ctrl.ref) {\n          ctrl.queue.push(update2);\n        } else {\n          ctrl.start(update2);\n        }\n      }\n    });\n  });\n  useOnce(() => () => {\n    each5(state.ctrls, ctrl => ctrl.stop(true));\n  });\n  const values = springs.map(x => ({\n    ...x\n  }));\n  return ref ? [values, ref] : values;\n}\n\n// src/hooks/useSpring.ts\nfunction useSpring(props, deps) {\n  const isFn = is9.fun(props);\n  const [[values], ref] = useSprings(1, isFn ? props : [props], isFn ? deps || [] : deps);\n  return isFn || arguments.length == 2 ? [values, ref] : values;\n}\n\n// src/hooks/useSpringRef.ts\nimport { useState } from \"react\";\nvar initSpringRef = () => SpringRef();\nvar useSpringRef = () => useState(initSpringRef)[0];\n\n// src/hooks/useSpringValue.ts\nimport { useConstant, useOnce as useOnce2 } from \"@react-spring/shared\";\nvar useSpringValue = (initial, props) => {\n  const springValue = useConstant(() => new SpringValue(initial, props));\n  useOnce2(() => () => {\n    springValue.stop();\n  });\n  return springValue;\n};\n\n// src/hooks/useTrail.ts\nimport { each as each6, is as is10, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect3 } from \"@react-spring/shared\";\nfunction useTrail(length, propsArg, deps) {\n  const propsFn = is10.fun(propsArg) && propsArg;\n  if (propsFn && !deps) deps = [];\n  let reverse = true;\n  let passedRef = void 0;\n  const result = useSprings(length, (i, ctrl) => {\n    const props = propsFn ? propsFn(i, ctrl) : propsArg;\n    passedRef = props.ref;\n    reverse = reverse && props.reverse;\n    return props;\n  },\n  // Ensure the props function is called when no deps exist.\n  // This works around the 3 argument rule.\n  deps || [{}]);\n  useIsomorphicLayoutEffect3(() => {\n    each6(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)];\n      replaceRef(ctrl, passedRef);\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({\n            to: parent.springs\n          });\n        }\n        return;\n      }\n      if (parent) {\n        ctrl.start({\n          to: parent.springs\n        });\n      } else {\n        ctrl.start();\n      }\n    });\n  }, deps);\n  if (propsFn || arguments.length == 3) {\n    const ref = passedRef ?? result[1];\n    ref[\"_getProps\"] = (propsArg2, ctrl, i) => {\n      const props = is10.fun(propsArg2) ? propsArg2(i, ctrl) : propsArg2;\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)];\n        if (parent) props.to = parent.springs;\n        return props;\n      }\n    };\n    return result;\n  }\n  return result[0];\n}\n\n// src/hooks/useTransition.tsx\nimport * as React2 from \"react\";\nimport { useContext as useContext3, useRef as useRef2, useMemo as useMemo2 } from \"react\";\nimport { is as is11, toArray as toArray4, useForceUpdate as useForceUpdate2, useOnce as useOnce3, usePrev as usePrev2, each as each7, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect4 } from \"@react-spring/shared\";\nfunction useTransition(data, props, deps) {\n  const propsFn = is11.fun(props) && props;\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig\n  } = propsFn ? propsFn() : props;\n  const ref = useMemo2(() => propsFn || arguments.length == 3 ? SpringRef() : void 0, []);\n  const items = toArray4(data);\n  const transitions = [];\n  const usedTransitions = useRef2(null);\n  const prevTransitions = reset ? null : usedTransitions.current;\n  useIsomorphicLayoutEffect4(() => {\n    usedTransitions.current = transitions;\n  });\n  useOnce3(() => {\n    each7(transitions, t => {\n      ref?.add(t.ctrl);\n      t.ctrl.ref = ref;\n    });\n    return () => {\n      each7(usedTransitions.current, t => {\n        if (t.expired) {\n          clearTimeout(t.expirationId);\n        }\n        detachRefs(t.ctrl, ref);\n        t.ctrl.stop(true);\n      });\n    };\n  });\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions);\n  const expired = reset && usedTransitions.current || [];\n  useIsomorphicLayoutEffect4(() => each7(expired, ({\n    ctrl,\n    item,\n    key\n  }) => {\n    detachRefs(ctrl, ref);\n    callProp(onDestroyed, item, key);\n  }));\n  const reused = [];\n  if (prevTransitions) each7(prevTransitions, (t, i) => {\n    if (t.expired) {\n      clearTimeout(t.expirationId);\n      expired.push(t);\n    } else {\n      i = reused[i] = keys.indexOf(t.key);\n      if (~i) transitions[i] = t;\n    }\n  });\n  each7(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: \"mount\" /* MOUNT */,\n        ctrl: new Controller()\n      };\n      transitions[i].ctrl.item = item;\n    }\n  });\n  if (reused.length) {\n    let i = -1;\n    const {\n      leave\n    } = propsFn ? propsFn() : props;\n    each7(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions[prevIndex];\n      if (~keyIndex) {\n        i = transitions.indexOf(t);\n        transitions[i] = {\n          ...t,\n          item: items[keyIndex]\n        };\n      } else if (leave) {\n        transitions.splice(++i, 0, t);\n      }\n    });\n  }\n  if (is11.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item));\n  }\n  let delay = -trail;\n  const forceUpdate = useForceUpdate2();\n  const defaultProps = getDefaultProps(props);\n  const changes = /* @__PURE__ */new Map();\n  const exitingTransitions = useRef2(/* @__PURE__ */new Map());\n  const forceChange = useRef2(false);\n  each7(transitions, (t, i) => {\n    const key = t.key;\n    const prevPhase = t.phase;\n    const p = propsFn ? propsFn() : props;\n    let to2;\n    let phase;\n    const propsDelay = callProp(p.delay || 0, key);\n    if (prevPhase == \"mount\" /* MOUNT */) {\n      to2 = p.enter;\n      phase = \"enter\" /* ENTER */;\n    } else {\n      const isLeave = keys.indexOf(key) < 0;\n      if (prevPhase != \"leave\" /* LEAVE */) {\n        if (isLeave) {\n          to2 = p.leave;\n          phase = \"leave\" /* LEAVE */;\n        } else if (to2 = p.update) {\n          phase = \"update\" /* UPDATE */;\n        } else return;\n      } else if (!isLeave) {\n        to2 = p.enter;\n        phase = \"enter\" /* ENTER */;\n      } else return;\n    }\n    to2 = callProp(to2, t.item, i);\n    to2 = is11.obj(to2) ? inferTo(to2) : {\n      to: to2\n    };\n    if (!to2.config) {\n      const config2 = propsConfig || defaultProps.config;\n      to2.config = callProp(config2, t.item, i, phase);\n    }\n    delay += trail;\n    const payload = {\n      ...defaultProps,\n      // we need to add our props.delay value you here.\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      // This prevents implied resets.\n      reset: false,\n      // Merge any phase-specific props.\n      ...to2\n    };\n    if (phase == \"enter\" /* ENTER */ && is11.und(payload.from)) {\n      const p2 = propsFn ? propsFn() : props;\n      const from = is11.und(p2.initial) || prevTransitions ? p2.from : p2.initial;\n      payload.from = callProp(from, t.item, i);\n    }\n    const {\n      onResolve\n    } = payload;\n    payload.onResolve = result => {\n      callProp(onResolve, result);\n      const transitions2 = usedTransitions.current;\n      const t2 = transitions2.find(t3 => t3.key === key);\n      if (!t2) return;\n      if (result.cancelled && t2.phase != \"update\" /* UPDATE */) {\n        return;\n      }\n      if (t2.ctrl.idle) {\n        const idle = transitions2.every(t3 => t3.ctrl.idle);\n        if (t2.phase == \"leave\" /* LEAVE */) {\n          const expiry = callProp(expires, t2.item);\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry;\n            t2.expired = true;\n            if (!idle && expiryMs > 0) {\n              if (expiryMs <= 2147483647) t2.expirationId = setTimeout(forceUpdate, expiryMs);\n              return;\n            }\n          }\n        }\n        if (idle && transitions2.some(t3 => t3.expired)) {\n          exitingTransitions.current.delete(t2);\n          if (exitBeforeEnter) {\n            forceChange.current = true;\n          }\n          forceUpdate();\n        }\n      }\n    };\n    const springs = getSprings(t.ctrl, payload);\n    if (phase === \"leave\" /* LEAVE */ && exitBeforeEnter) {\n      exitingTransitions.current.set(t, {\n        phase,\n        springs,\n        payload\n      });\n    } else {\n      changes.set(t, {\n        phase,\n        springs,\n        payload\n      });\n    }\n  });\n  const context = useContext3(SpringContext);\n  const prevContext = usePrev2(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  useIsomorphicLayoutEffect4(() => {\n    if (hasContext) {\n      each7(transitions, t => {\n        t.ctrl.start({\n          default: context\n        });\n      });\n    }\n  }, [context]);\n  each7(changes, (_, t) => {\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex(state => state.key === t.key);\n      transitions.splice(ind, 1);\n    }\n  });\n  useIsomorphicLayoutEffect4(() => {\n    each7(exitingTransitions.current.size ? exitingTransitions.current : changes, ({\n      phase,\n      payload\n    }, t) => {\n      const {\n        ctrl\n      } = t;\n      t.phase = phase;\n      ref?.add(ctrl);\n      if (hasContext && phase == \"enter\" /* ENTER */) {\n        ctrl.start({\n          default: context\n        });\n      }\n      if (payload) {\n        replaceRef(ctrl, payload.ref);\n        if ((ctrl.ref || ref) && !forceChange.current) {\n          ctrl.update(payload);\n        } else {\n          ctrl.start(payload);\n          if (forceChange.current) {\n            forceChange.current = false;\n          }\n        }\n      }\n    });\n  }, reset ? void 0 : deps);\n  const renderTransitions = render => /* @__PURE__ */React2.createElement(React2.Fragment, null, transitions.map((t, i) => {\n    const {\n      springs\n    } = changes.get(t) || t.ctrl;\n    const elem = render({\n      ...springs\n    }, t.item, t, i);\n    return elem && elem.type ? /* @__PURE__ */React2.createElement(elem.type, {\n      ...elem.props,\n      key: is11.str(t.key) || is11.num(t.key) ? t.key : t.ctrl.id,\n      ref: elem.ref\n    }) : elem;\n  }));\n  return ref ? [renderTransitions, ref] : renderTransitions;\n}\nvar nextKey = 1;\nfunction getKeys(items, {\n  key,\n  keys = key\n}, prevTransitions) {\n  if (keys === null) {\n    const reused = /* @__PURE__ */new Set();\n    return items.map(item => {\n      const t = prevTransitions && prevTransitions.find(t2 => t2.item === item && t2.phase !== \"leave\" /* LEAVE */ && !reused.has(t2));\n      if (t) {\n        reused.add(t);\n        return t.key;\n      }\n      return nextKey++;\n    });\n  }\n  return is11.und(keys) ? items : is11.fun(keys) ? items.map(keys) : toArray4(keys);\n}\n\n// src/hooks/useScroll.ts\nimport { each as each8, onScroll, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect5 } from \"@react-spring/shared\";\nvar useScroll = ({\n  container,\n  ...springOptions\n} = {}) => {\n  const [scrollValues, api] = useSpring(() => ({\n    scrollX: 0,\n    scrollY: 0,\n    scrollXProgress: 0,\n    scrollYProgress: 0,\n    ...springOptions\n  }), []);\n  useIsomorphicLayoutEffect5(() => {\n    const cleanupScroll = onScroll(({\n      x,\n      y\n    }) => {\n      api.start({\n        scrollX: x.current,\n        scrollXProgress: x.progress,\n        scrollY: y.current,\n        scrollYProgress: y.progress\n      });\n    }, {\n      container: container?.current || void 0\n    });\n    return () => {\n      each8(Object.values(scrollValues), value => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return scrollValues;\n};\n\n// src/hooks/useResize.ts\nimport { onResize, each as each9, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect6 } from \"@react-spring/shared\";\nvar useResize = ({\n  container,\n  ...springOptions\n}) => {\n  const [sizeValues, api] = useSpring(() => ({\n    width: 0,\n    height: 0,\n    ...springOptions\n  }), []);\n  useIsomorphicLayoutEffect6(() => {\n    const cleanupScroll = onResize(({\n      width,\n      height\n    }) => {\n      api.start({\n        width,\n        height,\n        immediate: sizeValues.width.get() === 0 || sizeValues.height.get() === 0\n      });\n    }, {\n      container: container?.current || void 0\n    });\n    return () => {\n      each9(Object.values(sizeValues), value => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return sizeValues;\n};\n\n// src/hooks/useInView.ts\nimport { useRef as useRef3, useState as useState2 } from \"react\";\nimport { is as is12, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect7 } from \"@react-spring/shared\";\nvar defaultThresholdOptions = {\n  any: 0,\n  all: 1\n};\nfunction useInView(props, args) {\n  const [isInView, setIsInView] = useState2(false);\n  const ref = useRef3();\n  const propsFn = is12.fun(props) && props;\n  const springsProps = propsFn ? propsFn() : {};\n  const {\n    to: to2 = {},\n    from = {},\n    ...restSpringProps\n  } = springsProps;\n  const intersectionArguments = propsFn ? args : props;\n  const [springs, api] = useSpring(() => ({\n    from,\n    ...restSpringProps\n  }), []);\n  useIsomorphicLayoutEffect7(() => {\n    const element = ref.current;\n    const {\n      root,\n      once,\n      amount = \"any\",\n      ...restArgs\n    } = intersectionArguments ?? {};\n    if (!element || once && isInView || typeof IntersectionObserver === \"undefined\") return;\n    const activeIntersections = /* @__PURE__ */new WeakMap();\n    const onEnter = () => {\n      if (to2) {\n        api.start(to2);\n      }\n      setIsInView(true);\n      const cleanup = () => {\n        if (from) {\n          api.start(from);\n        }\n        setIsInView(false);\n      };\n      return once ? void 0 : cleanup;\n    };\n    const handleIntersection = entries => {\n      entries.forEach(entry => {\n        const onLeave = activeIntersections.get(entry.target);\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return;\n        }\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter();\n          if (is12.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave);\n          } else {\n            observer.unobserve(entry.target);\n          }\n        } else if (onLeave) {\n          onLeave();\n          activeIntersections.delete(entry.target);\n        }\n      });\n    };\n    const observer = new IntersectionObserver(handleIntersection, {\n      root: root && root.current || void 0,\n      threshold: typeof amount === \"number\" || Array.isArray(amount) ? amount : defaultThresholdOptions[amount],\n      ...restArgs\n    });\n    observer.observe(element);\n    return () => observer.unobserve(element);\n  }, [intersectionArguments]);\n  if (propsFn) {\n    return [ref, springs];\n  }\n  return [ref, isInView];\n}\n\n// src/components/Spring.tsx\nfunction Spring({\n  children,\n  ...props\n}) {\n  return children(useSpring(props));\n}\n\n// src/components/Trail.tsx\nimport { is as is13 } from \"@react-spring/shared\";\nfunction Trail({\n  items,\n  children,\n  ...props\n}) {\n  const trails = useTrail(items.length, props);\n  return items.map((item, index) => {\n    const result = children(item, index);\n    return is13.fun(result) ? result(trails[index]) : result;\n  });\n}\n\n// src/components/Transition.tsx\nfunction Transition({\n  items,\n  children,\n  ...props\n}) {\n  return useTransition(items, props)(children);\n}\n\n// src/interpolate.ts\nimport { deprecateInterpolate as deprecateInterpolate2 } from \"@react-spring/shared\";\n\n// src/Interpolation.ts\nimport { is as is14, raf as raf5, each as each10, isEqual as isEqual2, toArray as toArray5, frameLoop as frameLoop3, getFluidValue as getFluidValue3, createInterpolator, Globals as G6, callFluidObservers as callFluidObservers3, addFluidObserver as addFluidObserver3, removeFluidObserver as removeFluidObserver2, hasFluidValue as hasFluidValue2 } from \"@react-spring/shared\";\nimport { getAnimated as getAnimated3, setAnimated as setAnimated2, getAnimatedType as getAnimatedType2, getPayload as getPayload2 } from \"@react-spring/animated\";\nvar Interpolation = class extends FrameValue {\n  constructor(source, args) {\n    super();\n    this.source = source;\n    /** Equals false when in the frameloop */\n    this.idle = true;\n    /** The inputs which are currently animating */\n    this._active = /* @__PURE__ */new Set();\n    this.calc = createInterpolator(...args);\n    const value = this._get();\n    const nodeType = getAnimatedType2(value);\n    setAnimated2(this, nodeType.create(value));\n  }\n  advance(_dt) {\n    const value = this._get();\n    const oldValue = this.get();\n    if (!isEqual2(value, oldValue)) {\n      getAnimated3(this).setValue(value);\n      this._onChange(value, this.idle);\n    }\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this);\n    }\n  }\n  _get() {\n    const inputs = is14.arr(this.source) ? this.source.map(getFluidValue3) : toArray5(getFluidValue3(this.source));\n    return this.calc(...inputs);\n  }\n  _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false;\n      each10(getPayload2(this), node => {\n        node.done = false;\n      });\n      if (G6.skipAnimation) {\n        raf5.batchedUpdates(() => this.advance());\n        becomeIdle(this);\n      } else {\n        frameLoop3.start(this);\n      }\n    }\n  }\n  // Observe our sources only when we're observed.\n  _attach() {\n    let priority = 1;\n    each10(toArray5(this.source), source => {\n      if (hasFluidValue2(source)) {\n        addFluidObserver3(source, this);\n      }\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source);\n        }\n        priority = Math.max(priority, source.priority + 1);\n      }\n    });\n    this.priority = priority;\n    this._start();\n  }\n  // Stop observing our sources once we have no observers.\n  _detach() {\n    each10(toArray5(this.source), source => {\n      if (hasFluidValue2(source)) {\n        removeFluidObserver2(source, this);\n      }\n    });\n    this._active.clear();\n    becomeIdle(this);\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      if (event.idle) {\n        this.advance();\n      } else {\n        this._active.add(event.parent);\n        this._start();\n      }\n    } else if (event.type == \"idle\") {\n      this._active.delete(event.parent);\n    } else if (event.type == \"priority\") {\n      this.priority = toArray5(this.source).reduce((highest, parent) => Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1), 0);\n    }\n  }\n};\nfunction isIdle(source) {\n  return source.idle !== false;\n}\nfunction checkIdle(active) {\n  return !active.size || Array.from(active).every(isIdle);\n}\nfunction becomeIdle(self) {\n  if (!self.idle) {\n    self.idle = true;\n    each10(getPayload2(self), node => {\n      node.done = true;\n    });\n    callFluidObservers3(self, {\n      type: \"idle\",\n      parent: self\n    });\n  }\n}\n\n// src/interpolate.ts\nvar to = (source, ...args) => new Interpolation(source, args);\nvar interpolate = (source, ...args) => (deprecateInterpolate2(), new Interpolation(source, args));\n\n// src/globals.ts\nimport { Globals, frameLoop as frameLoop4, createStringInterpolator } from \"@react-spring/shared\";\nGlobals.assign({\n  createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args)\n});\nvar update = frameLoop4.advance;\n\n// src/index.ts\nimport { createInterpolator as createInterpolator2, useIsomorphicLayoutEffect as useIsomorphicLayoutEffect8, useReducedMotion, easings as easings2 } from \"@react-spring/shared\";\nexport * from \"@react-spring/types\";\nexport { BailSignal, Controller, FrameValue, Globals, Interpolation, Spring, SpringContext, SpringRef, SpringValue, Trail, Transition, config, createInterpolator2 as createInterpolator, easings2 as easings, inferTo, interpolate, to, update, useChain, useInView, useIsomorphicLayoutEffect8 as useIsomorphicLayoutEffect, useReducedMotion, useResize, useScroll, useSpring, useSpringRef, useSpringValue, useSprings, useTrail, useTransition };", "map": {"version": 3, "names": ["each", "useIsomorphicLayoutEffect", "is", "toArray", "eachProp", "getFluidValue", "isAnimatedString", "Globals", "G", "callProp", "value", "args", "fun", "matchProp", "key", "includes", "resolveProp", "prop", "obj", "getDefaultProp", "props", "default", "noopTransform", "getDefaultProps", "transform", "keys", "DEFAULT_PROPS", "Object", "defaults2", "und", "RESERVED_PROPS", "config", "from", "to", "ref", "loop", "reset", "pause", "cancel", "reverse", "immediate", "delay", "onProps", "onStart", "onChange", "onPause", "onResume", "onRest", "onResolve", "items", "trail", "sort", "expires", "initial", "enter", "update", "leave", "children", "onDestroyed", "callId", "parentId", "getForwardProps", "forward", "count", "inferTo", "to2", "out", "val", "computeGoal", "arr", "map", "createStringInterpolator", "range", "output", "hasProps", "_", "isAsyncTo", "detachRefs", "ctrl", "delete", "replaceRef", "add", "<PERSON><PERSON><PERSON>n", "refs", "timeSteps", "timeFrame", "prev<PERSON><PERSON><PERSON>", "i", "controllers", "current", "length", "isNaN", "queue", "memoizedDelayProp", "start", "p", "Promise", "resolve", "queues", "q", "then", "update2", "push", "all", "is9", "useContext", "useContext2", "useMemo", "useRef", "is8", "each5", "usePrev", "useOnce", "useForceUpdate", "useIsomorphicLayoutEffect2", "is5", "raf", "raf3", "each2", "isEqual", "toArray2", "eachProp3", "frameLoop", "frameLoop2", "flushCalls", "getFluidValue2", "isAnimatedString2", "G5", "callFluidObservers", "callFluidObservers2", "hasFluidValue", "addFluidObserver", "removeFluidObserver", "getFluidObservers", "AnimatedValue", "AnimatedString", "getPayload", "getAnimated", "getAnimated2", "setAnimated", "getAnimatedType", "is2", "easings", "tension", "friction", "gentle", "wobbly", "stiff", "slow", "molasses", "defaults", "mass", "damping", "easing", "linear", "clamp", "AnimationConfig", "constructor", "velocity", "assign", "mergeConfig", "config2", "newConfig", "defaultConfig", "sanitizeConfig", "frequency", "Math", "pow", "PI", "decay", "duration", "isTensionConfig", "emptyArray", "Animation", "changed", "values", "to<PERSON><PERSON><PERSON>", "fromValues", "is3", "G2", "scheduleProps", "defaultProps", "state", "actions", "reject", "timeout", "paused", "resumeQueue", "resume", "timeouts", "time", "now", "skipAnimation", "delayed", "setTimeout", "pauseQueue", "cancelId", "err", "is4", "raf2", "flush", "eachProp2", "G3", "getCombinedResult", "target", "results", "some", "result", "cancelled", "getCancelledResult", "get", "every", "noop", "getNoopResult", "getFinishedResult", "finished", "runAsync", "asyncTo", "prevTo", "promise", "prevPromise", "asyncId", "preventBail", "bail", "bailPromise", "bailIfEnded", "bailSignal", "bailResult", "animate", "arg1", "arg2", "BailSignal", "skipAnimationSignal", "SkipAnimationSignal", "stopAsync", "props2", "result2", "animating", "stop", "bind", "batchedUpdates", "item", "t", "clear", "Error", "deprecateInterpolate", "FluidValue", "FluidValue2", "G4", "isFrameValue", "FrameValue", "nextId", "arguments", "id", "_priority", "priority", "_onPriorityChange", "node", "getValue", "interpolate", "toJSON", "observerAdded", "_attach", "observerRemoved", "_detach", "_onChange", "idle", "type", "parent", "$P", "Symbol", "for", "HAS_ANIMATED", "IS_ANIMATING", "IS_PAUSED", "hasAnimated", "isAnimating", "isPaused", "setActiveBit", "active", "setPausedBit", "SpringValue", "animation", "_state", "Set", "_pendingCalls", "_lastCallId", "_lastToId", "_memoizedDuration", "goal", "lastVelocity", "node2", "<PERSON><PERSON><PERSON><PERSON>", "advance", "dt", "anim", "payload", "for<PERSON>ach", "done", "lastPosition", "position", "elapsed", "elapsedTime", "v0", "precision", "min", "abs", "durationProgress", "progress", "e", "exp", "restVelocity", "bounceFactor", "bounce", "canBounce", "isGrowing", "isMoving", "isBouncing", "step", "numSteps", "ceil", "n", "springForce", "dampingForce", "acceleration", "Number", "console", "warn", "setValue", "round", "currVal", "finalVal", "_stop", "set", "_focus", "_set", "_update", "finish", "_onStart", "up", "eventObserved", "event", "_start", "_prepareNode", "isLoop", "test", "mergeActiveFn", "sendEvent", "isFrozen", "checkFinished", "_resume", "_merge", "nextProps", "createLoopUpdate", "hasToProp", "hasFromProp", "prevFrom", "has<PERSON>romC<PERSON>ed", "hasToChanged", "hasAsyncTo", "isAnimatable", "num", "nodeType", "name", "goalType", "started", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ACTIVE_EVENTS", "arg", "oldNode", "create", "loopRet", "overrides", "createUpdate", "findDefined", "size", "Array", "declareUpdate", "is6", "raf4", "each3", "flush2", "toArray3", "eachProp4", "flushCalls2", "addFluidObserver2", "BATCHED_EVENTS", "nextId2", "Controller", "flush3", "springs", "_lastAsyncId", "_active", "_changed", "_started", "_events", "Map", "_onFrame", "_flush", "spring", "_item", "prepare<PERSON>eys", "flushUpdateQueue", "iterator", "onStart2", "onChange2", "onRest2", "onFrame", "flushUpdate", "handler", "promises", "getSprings", "prepareSprings", "createSpring", "setSprings", "observer", "React", "useMemoOne", "SpringContext", "inherited", "ctx", "Provider", "createElement", "makeContext", "Consumer", "init", "createContext", "_context", "each4", "is7", "deprecateDirectCall", "SpringRef", "SpringRef2", "_getProps", "indexOf", "splice", "index", "useSprings", "deps", "propsFn", "layoutId", "forceUpdate", "ctrls", "updates2", "springs2", "canFlushSync", "updates", "prevLength", "slice", "declareUpdates", "startIndex", "endIndex", "context", "prevContext", "hasContext", "cb", "x", "useSpring", "isFn", "useState", "initSpringRef", "useSpringRef", "useConstant", "useOnce2", "useSpringValue", "springValue", "each6", "is10", "useIsomorphicLayoutEffect3", "useTrail", "propsArg", "passedRef", "propsArg2", "React2", "useContext3", "useRef2", "useMemo2", "is11", "toArray4", "useForceUpdate2", "useOnce3", "usePrev2", "each7", "useIsomorphicLayoutEffect4", "useTransition", "data", "exitBeforeEnter", "propsRef", "propsConfig", "transitions", "usedTransitions", "prevTransitions", "expired", "clearTimeout", "expirationId", "get<PERSON><PERSON><PERSON>", "reused", "phase", "keyIndex", "prevIndex", "a", "b", "changes", "exitingTransitions", "forceChange", "prevPhase", "props<PERSON><PERSON><PERSON>", "isLeave", "p2", "transitions2", "t2", "find", "t3", "expiry", "expiryMs", "ind", "findIndex", "renderTransitions", "render", "Fragment", "elem", "str", "<PERSON><PERSON><PERSON>", "has", "each8", "onScroll", "useIsomorphicLayoutEffect5", "useScroll", "container", "springOptions", "scrollValues", "api", "scrollX", "scrollY", "scrollXProgress", "scrollYProgress", "cleanupScroll", "y", "onResize", "each9", "useIsomorphicLayoutEffect6", "useResize", "sizeValues", "width", "height", "useRef3", "useState2", "is12", "useIsomorphicLayoutEffect7", "defaultThresholdOptions", "any", "useInView", "isInView", "setIsInView", "springsProps", "restSpringProps", "intersectionArguments", "element", "root", "once", "amount", "restArgs", "IntersectionObserver", "activeIntersections", "WeakMap", "onEnter", "cleanup", "handleIntersection", "entries", "entry", "onLeave", "isIntersecting", "Boolean", "newOnLeave", "unobserve", "threshold", "isArray", "observe", "Spring", "is13", "Trail", "trails", "Transition", "deprecateInterpolate2", "is14", "raf5", "each10", "isEqual2", "toArray5", "frameLoop3", "getFluidValue3", "createInterpolator", "G6", "callFluidObservers3", "addFluidObserver3", "removeFluidObserver2", "hasFluidValue2", "getAnimated3", "setAnimated2", "getAnimatedType2", "getPayload2", "Interpolation", "source", "calc", "_get", "_dt", "oldValue", "checkIdle", "becomeIdle", "inputs", "max", "reduce", "highest", "isIdle", "self", "frameLoop4", "createInterpolator2", "useIsomorphicLayoutEffect8", "useReducedMotion", "easings2"], "sources": ["/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useChain.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/helpers.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useSpring.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useSprings.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/SpringValue.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/AnimationConfig.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/constants.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/Animation.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/scheduleProps.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/runAsync.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/AnimationResult.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/FrameValue.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/SpringPhase.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/Controller.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/SpringContext.tsx", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/SpringRef.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useSpringRef.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useSpringValue.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useTrail.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useTransition.tsx", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useScroll.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useResize.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/hooks/useInView.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/components/Spring.tsx", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/components/Trail.tsx", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/components/Transition.tsx", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/interpolate.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/Interpolation.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/globals.ts", "/Users/<USER>/Downloads/Flood copy/frontend/node_modules/@react-spring/core/src/index.ts"], "sourcesContent": ["import { each, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { SpringRef } from '../SpringRef'\nimport { callProp } from '../helpers'\n\n/**\n * Used to orchestrate animation hooks in sequence with one another.\n * This is best used when you specifically want to orchestrate different\n * types of animation hook e.g. `useSpring` & `useTransition` in\n * sequence as opposed to multiple `useSpring` hooks.\n *\n *\n * ```jsx\n * export const MyComponent = () => {\n *  //...\n *  useChain([springRef, transitionRef])\n *  //...\n * }\n * ```\n *\n * @param refs – An array of `SpringRef`s.\n * @param timeSteps – Optional array of numbers that define the\n * delay between each animation from 0-1. The length should correlate\n * to the length of `refs`.\n * @param timeFrame – Optional number that defines the total duration\n *\n * @public\n */\nexport function useChain(\n  refs: ReadonlyArray<SpringRef>,\n  timeSteps?: number[],\n  timeFrame = 1000\n) {\n  useIsomorphicLayoutEffect(() => {\n    if (timeSteps) {\n      let prevDelay = 0\n      each(refs, (ref, i) => {\n        const controllers = ref.current\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i]\n\n          // Use the previous delay if none exists.\n          if (isNaN(delay)) delay = prevDelay\n          else prevDelay = delay\n\n          each(controllers, ctrl => {\n            each(ctrl.queue, props => {\n              // memoizing stops recursion https://github.com/pmndrs/react-spring/issues/1367\n              const memoizedDelayProp = props.delay\n              props.delay = key => delay + callProp(memoizedDelayProp || 0, key)\n            })\n          })\n\n          ref.start()\n        }\n      })\n    } else {\n      let p: Promise<any> = Promise.resolve()\n      each(refs, ref => {\n        const controllers = ref.current\n        if (controllers.length) {\n          // Take the queue of each controller\n          const queues = controllers.map(ctrl => {\n            const q = ctrl.queue\n            ctrl.queue = []\n            return q\n          })\n\n          // Apply the queue when the previous ref stops animating\n          p = p.then(() => {\n            each(controllers, (ctrl, i) =>\n              each(queues[i] || [], update => ctrl.queue.push(update))\n            )\n            return Promise.all(ref.start())\n          })\n        }\n      })\n    }\n  })\n}\n", "import {\n  is,\n  toArray,\n  eachProp,\n  getFluidValue,\n  isAnimatedString,\n  FluidValue,\n  Globals as G,\n} from '@react-spring/shared'\nimport { AnyFn, OneOrMore, Lookup } from '@react-spring/types'\nimport { ReservedProps, ForwardProps, InferTo } from './types'\nimport type { Controller } from './Controller'\nimport type { SpringRef } from './SpringRef'\n\nexport function callProp<T>(\n  value: T,\n  ...args: T extends AnyFn ? Parameters<T> : unknown[]\n): T extends AnyFn<any, infer U> ? U : T {\n  return is.fun(value) ? value(...args) : value\n}\n\n/** Try to coerce the given value into a boolean using the given key */\nexport const matchProp = (\n  value: boolean | OneOrMore<string> | ((key: any) => boolean) | undefined,\n  key: string | undefined\n) =>\n  value === true ||\n  !!(\n    key &&\n    value &&\n    (is.fun(value) ? value(key) : toArray(value).includes(key))\n  )\n\nexport const resolveProp = <T>(\n  prop: T | Lookup<T> | undefined,\n  key: string | undefined\n) => (is.obj(prop) ? key && (prop as any)[key] : prop)\n\nexport const concatFn = <T extends AnyFn>(first: T | undefined, last: T) =>\n  first ? (...args: Parameters<T>) => (first(...args), last(...args)) : last\n\n/** Returns `true` if the given prop is having its default value set. */\nexport const hasDefaultProp = <T extends Lookup>(props: T, key: keyof T) =>\n  !is.und(getDefaultProp(props, key))\n\n/** Get the default value being set for the given `key` */\nexport const getDefaultProp = <T extends Lookup, P extends keyof T>(\n  props: T,\n  key: P\n): T[P] =>\n  props.default === true\n    ? props[key]\n    : props.default\n      ? props.default[key]\n      : undefined\n\nconst noopTransform = (value: any) => value\n\n/**\n * Extract the default props from an update.\n *\n * When the `default` prop is falsy, this function still behaves as if\n * `default: true` was used. The `default` prop is always respected when\n * truthy.\n */\nexport const getDefaultProps = <T extends Lookup>(\n  props: Lookup,\n  transform: (value: any, key: string) => any = noopTransform\n): T => {\n  let keys: readonly string[] = DEFAULT_PROPS\n  if (props.default && props.default !== true) {\n    props = props.default\n    keys = Object.keys(props)\n  }\n  const defaults: any = {}\n  for (const key of keys) {\n    const value = transform(props[key], key)\n    if (!is.und(value)) {\n      defaults[key] = value\n    }\n  }\n  return defaults\n}\n\n/**\n * These props are implicitly used as defaults when defined in a\n * declarative update (eg: render-based) or any update with `default: true`.\n *\n * Use `default: {}` or `default: false` to opt-out of these implicit defaults\n * for any given update.\n *\n * Note: These are not the only props with default values. For example, the\n * `pause`, `cancel`, and `immediate` props. But those must be updated with\n * the object syntax (eg: `default: { immediate: true }`).\n */\nexport const DEFAULT_PROPS = [\n  'config',\n  'onProps',\n  'onStart',\n  'onChange',\n  'onPause',\n  'onResume',\n  'onRest',\n] as const\n\nconst RESERVED_PROPS: {\n  [key: string]: 1 | undefined\n} = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n\n  // Transition props\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n\n  // Internal props\n  keys: 1,\n  callId: 1,\n  parentId: 1,\n}\n\n/**\n * Extract any properties whose keys are *not* reserved for customizing your\n * animations. All hooks use this function, which means `useTransition` props\n * are reserved for `useSpring` calls, etc.\n */\nfunction getForwardProps<Props extends ReservedProps>(\n  props: Props\n): ForwardProps<Props> | undefined {\n  const forward: any = {}\n\n  let count = 0\n  eachProp(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value\n      count++\n    }\n  })\n\n  if (count) {\n    return forward\n  }\n}\n\n/**\n * Clone the given `props` and move all non-reserved props\n * into the `to` prop.\n */\nexport function inferTo<T extends object>(props: T): InferTo<T> {\n  const to = getForwardProps(props)\n  if (to) {\n    const out: any = { to }\n    eachProp(props, (val, key) => key in to || (out[key] = val))\n    return out\n  }\n  return { ...props } as any\n}\n\n// Compute the goal value, converting \"red\" to \"rgba(255, 0, 0, 1)\" in the process\nexport function computeGoal<T>(value: T | FluidValue<T>): T {\n  value = getFluidValue(value)\n  return is.arr(value)\n    ? value.map(computeGoal)\n    : isAnimatedString(value)\n      ? (G.createStringInterpolator({\n          range: [0, 1],\n          output: [value, value] as any,\n        })(1) as any)\n      : value\n}\n\nexport function hasProps(props: object) {\n  for (const _ in props) return true\n  return false\n}\n\nexport function isAsyncTo(to: any) {\n  return is.fun(to) || (is.arr(to) && is.obj(to[0]))\n}\n\n/** Detach `ctrl` from `ctrl.ref` and (optionally) the given `ref` */\nexport function detachRefs(ctrl: Controller, ref?: SpringRef) {\n  ctrl.ref?.delete(ctrl)\n  ref?.delete(ctrl)\n}\n\n/** Replace `ctrl.ref` with the given `ref` (if defined) */\nexport function replaceRef(ctrl: Controller, ref?: SpringRef) {\n  if (ref && ctrl.ref !== ref) {\n    ctrl.ref?.delete(ctrl)\n    ref.add(ctrl)\n    ctrl.ref = ref\n  }\n}\n", "import { Lookup, Remap } from '@react-spring/types'\nimport { is } from '@react-spring/shared'\n\nimport { ControllerUpdate, PickAnimated, SpringValues } from '../types'\nimport { Valid } from '../types/common'\nimport { SpringRef } from '../SpringRef'\nimport { useSprings } from './useSprings'\n\n/**\n * The props that `useSpring` recognizes.\n */\nexport type UseSpringProps<Props extends object = any> = unknown &\n  PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? Remap<\n        ControllerUpdate<State> & {\n          /**\n           * Used to access the imperative API.\n           *\n           * When defined, the render animation won't auto-start.\n           */\n          ref?: SpringRef<State>\n        }\n      >\n    : never\n  : never\n\n/**\n * The `props` function is only called on the first render, unless\n * `deps` change (when defined). State is inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props:\n    | Function\n    | (() => (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps),\n  deps?: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [SpringValues<State>, SpringRef<State>]\n    : never\n  : never\n\n/**\n * Updated on every render, with state inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props: (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps\n): SpringValues<PickAnimated<Props>>\n\n/**\n * Updated only when `deps` change, with state inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props: (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps,\n  deps: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [SpringValues<State>, SpringRef<State>]\n    : never\n  : never\n\n/** @internal */\nexport function useSpring(props: any, deps?: readonly any[]) {\n  const isFn = is.fun(props)\n  const [[values], ref] = useSprings(\n    1,\n    isFn ? props : [props],\n    isFn ? deps || [] : deps\n  )\n  return isFn || arguments.length == 2 ? [values, ref] : values\n}\n", "import { useContext, useMemo, useRef } from 'react'\nimport { Lookup } from '@react-spring/types'\nimport {\n  is,\n  each,\n  usePrev,\n  useOnce,\n  useForceUpdate,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\n\nimport {\n  ControllerFlushFn,\n  ControllerUpdate,\n  PickAnimated,\n  SpringValues,\n} from '../types'\nimport { UseSpringProps } from './useSpring'\nimport { declareUpdate } from '../SpringValue'\nimport {\n  Controller,\n  getSprings,\n  flushUpdateQueue,\n  setSprings,\n} from '../Controller'\nimport { hasProps, detachRefs, replaceRef } from '../helpers'\nimport { SpringContext } from '../SpringContext'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\n\nexport type UseSpringsProps<State extends Lookup = Lookup> = unknown &\n  ControllerUpdate<State> & {\n    ref?: SpringRefType<State>\n  }\n\n/**\n * When the `deps` argument exists, the `props` function is called whenever\n * the `deps` change on re-render.\n *\n * Without the `deps` argument, the `props` function is only called once.\n */\nexport function useSprings<Props extends UseSpringProps>(\n  length: number,\n  props: (i: number, ctrl: Controller) => Props,\n  deps?: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRefType<State>]\n    : never\n  : never\n\n/**\n * Animations are updated on re-render.\n */\nexport function useSprings<Props extends UseSpringsProps>(\n  length: number,\n  props: Props[] & UseSpringsProps<PickAnimated<Props>>[]\n): SpringValues<PickAnimated<Props>>[]\n\n/**\n * When the `deps` argument exists, you get the `update` and `stop` function.\n */\nexport function useSprings<Props extends UseSpringsProps>(\n  length: number,\n  props: Props[] & UseSpringsProps<PickAnimated<Props>>[],\n  deps: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRefType<State>]\n    : never\n  : never\n\n/** @internal */\nexport function useSprings(\n  length: number,\n  props: any[] | ((i: number, ctrl: Controller) => any),\n  deps?: readonly any[]\n): any {\n  const propsFn = is.fun(props) && props\n  if (propsFn && !deps) deps = []\n\n  // Create a local ref if a props function or deps array is ever passed.\n  const ref = useMemo(\n    () => (propsFn || arguments.length == 3 ? SpringRef() : void 0),\n    []\n  )\n\n  interface State {\n    // The controllers used for applying updates.\n    ctrls: Controller[]\n    // The queue of changes to make on commit.\n    queue: Array<() => void>\n    // The flush function used by controllers.\n    flush: ControllerFlushFn\n  }\n\n  // Set to 0 to prevent sync flush.\n  const layoutId = useRef(0)\n  const forceUpdate = useForceUpdate()\n\n  // State is updated on commit.\n  const state = useMemo(\n    (): State => ({\n      ctrls: [],\n      queue: [],\n      flush(ctrl, updates) {\n        const springs = getSprings(ctrl, updates)\n\n        // Flushing is postponed until the component's commit phase\n        // if a spring was created since the last commit.\n        const canFlushSync =\n          layoutId.current > 0 &&\n          !state.queue.length &&\n          !Object.keys(springs).some(key => !ctrl.springs[key])\n\n        return canFlushSync\n          ? flushUpdateQueue(ctrl, updates)\n          : new Promise<any>(resolve => {\n              setSprings(ctrl, springs)\n              state.queue.push(() => {\n                resolve(flushUpdateQueue(ctrl, updates))\n              })\n              forceUpdate()\n            })\n      },\n    }),\n    []\n  )\n\n  const ctrls = useRef([...state.ctrls])\n  const updates: any[] = []\n\n  // Cache old controllers to dispose in the commit phase.\n  const prevLength = usePrev(length) || 0\n\n  // Create new controllers when \"length\" increases, and destroy\n  // the affected controllers when \"length\" decreases.\n  useMemo(() => {\n    // Clean up any unused controllers\n    each(ctrls.current.slice(length, prevLength), ctrl => {\n      detachRefs(ctrl, ref)\n      ctrl.stop(true)\n    })\n    ctrls.current.length = length\n\n    declareUpdates(prevLength, length)\n  }, [length])\n\n  // Update existing controllers when \"deps\" are changed.\n  useMemo(() => {\n    declareUpdates(0, Math.min(prevLength, length))\n  }, deps)\n\n  /** Fill the `updates` array with declarative updates for the given index range. */\n  function declareUpdates(startIndex: number, endIndex: number) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl =\n        ctrls.current[i] ||\n        (ctrls.current[i] = new Controller(null, state.flush))\n\n      const update: UseSpringProps<any> = propsFn\n        ? propsFn(i, ctrl)\n        : (props as any)[i]\n\n      if (update) {\n        updates[i] = declareUpdate(update)\n      }\n    }\n  }\n\n  // New springs are created during render so users can pass them to\n  // their animated components, but new springs aren't cached until the\n  // commit phase (see the `useIsomorphicLayoutEffect` callback below).\n  const springs = ctrls.current.map((ctrl, i) => getSprings(ctrl, updates[i]))\n\n  const context = useContext(SpringContext)\n  const prevContext = usePrev(context)\n  const hasContext = context !== prevContext && hasProps(context)\n\n  useIsomorphicLayoutEffect(() => {\n    layoutId.current++\n\n    // Replace the cached controllers.\n    state.ctrls = ctrls.current\n\n    // Flush the commit queue.\n    const { queue } = state\n    if (queue.length) {\n      state.queue = []\n      each(queue, cb => cb())\n    }\n\n    // Update existing controllers.\n    each(ctrls.current, (ctrl, i) => {\n      // Attach the controller to the local ref.\n      ref?.add(ctrl)\n\n      // Update the default props.\n      if (hasContext) {\n        ctrl.start({ default: context })\n      }\n\n      // Apply updates created during render.\n      const update = updates[i]\n      if (update) {\n        // Update the injected ref if needed.\n        replaceRef(ctrl, update.ref)\n\n        // When an injected ref exists, the update is postponed\n        // until the ref has its `start` method called.\n        if (ctrl.ref) {\n          ctrl.queue.push(update)\n        } else {\n          ctrl.start(update)\n        }\n      }\n    })\n  })\n\n  // Cancel the animations of all controllers on unmount.\n  useOnce(() => () => {\n    each(state.ctrls, ctrl => ctrl.stop(true))\n  })\n\n  // Return a deep copy of the `springs` array so the caller can\n  // safely mutate it during render.\n  const values = springs.map(x => ({ ...x }))\n\n  return ref ? [values, ref] : values\n}\n", "import {\n  is,\n  raf,\n  each,\n  isEqual,\n  toArray,\n  eachProp,\n  frameLoop,\n  flushCalls,\n  getFluidValue,\n  isAnimatedString,\n  FluidValue,\n  Globals as G,\n  callFluidObservers,\n  hasFluidValue,\n  addFluidObserver,\n  removeFluidObserver,\n  getFluidObservers,\n} from '@react-spring/shared'\nimport {\n  Animated,\n  AnimatedValue,\n  AnimatedString,\n  getPayload,\n  getAnimated,\n  setAnimated,\n  getAnimatedType,\n} from '@react-spring/animated'\nimport { Lookup } from '@react-spring/types'\n\nimport { Animation } from './Animation'\nimport { mergeConfig } from './AnimationConfig'\nimport { scheduleProps } from './scheduleProps'\nimport { runAsync, RunAsyncState, RunAsyncProps, stopAsync } from './runAsync'\nimport {\n  callProp,\n  computeGoal,\n  matchProp,\n  inferTo,\n  getDefaultProps,\n  getDefaultProp,\n  isAsyncTo,\n  resolveProp,\n} from './helpers'\nimport { FrameValue, isFrameValue } from './FrameValue'\nimport {\n  isAnimating,\n  isPaused,\n  setPausedBit,\n  hasAnimated,\n  setActiveBit,\n} from './SpringPhase'\nimport {\n  AnimationRange,\n  AnimationResolver,\n  EventKey,\n  PickEventFns,\n} from './types/internal'\nimport { AsyncResult, SpringUpdate, VelocityProp, SpringProps } from './types'\nimport {\n  getCombinedResult,\n  getCancelledResult,\n  getFinishedResult,\n  getNoopResult,\n} from './AnimationResult'\n\ndeclare const console: any\n\ninterface DefaultSpringProps<T>\n  extends Pick<SpringProps<T>, 'pause' | 'cancel' | 'immediate' | 'config'>,\n    PickEventFns<SpringProps<T>> {}\n\n/**\n * Only numbers, strings, and arrays of numbers/strings are supported.\n * Non-animatable strings are also supported.\n */\nexport class SpringValue<T = any> extends FrameValue<T> {\n  /** The property name used when `to` or `from` is an object. Useful when debugging too. */\n  key?: string\n\n  /** The animation state */\n  animation = new Animation<T>()\n\n  /** The queue of pending props */\n  queue?: SpringUpdate<T>[]\n\n  /** Some props have customizable default values */\n  defaultProps: DefaultSpringProps<T> = {}\n\n  /** The state for `runAsync` calls */\n  protected _state: RunAsyncState<SpringValue<T>> = {\n    paused: false,\n    delayed: false,\n    pauseQueue: new Set(),\n    resumeQueue: new Set(),\n    timeouts: new Set(),\n  }\n\n  /** The promise resolvers of pending `start` calls */\n  protected _pendingCalls = new Set<AnimationResolver<this>>()\n\n  /** The counter for tracking `scheduleProps` calls */\n  protected _lastCallId = 0\n\n  /** The last `scheduleProps` call that changed the `to` prop */\n  protected _lastToId = 0\n\n  protected _memoizedDuration = 0\n\n  constructor(from: Exclude<T, object>, props?: SpringUpdate<T>)\n  constructor(props?: SpringUpdate<T>)\n  constructor(arg1?: any, arg2?: any) {\n    super()\n    if (!is.und(arg1) || !is.und(arg2)) {\n      const props = is.obj(arg1) ? { ...arg1 } : { ...arg2, from: arg1 }\n      if (is.und(props.default)) {\n        props.default = true\n      }\n      this.start(props)\n    }\n  }\n\n  /** Equals true when not advancing on each frame. */\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this)\n  }\n\n  get goal() {\n    return getFluidValue(this.animation.to) as T\n  }\n\n  get velocity(): VelocityProp<T> {\n    const node = getAnimated(this)!\n    return (\n      node instanceof AnimatedValue\n        ? node.lastVelocity || 0\n        : node.getPayload().map(node => node.lastVelocity || 0)\n    ) as any\n  }\n\n  /**\n   * When true, this value has been animated at least once.\n   */\n  get hasAnimated() {\n    return hasAnimated(this)\n  }\n\n  /**\n   * When true, this value has an unfinished animation,\n   * which is either active or paused.\n   */\n  get isAnimating() {\n    return isAnimating(this)\n  }\n\n  /**\n   * When true, all current and future animations are paused.\n   */\n  get isPaused() {\n    return isPaused(this)\n  }\n\n  /**\n   *\n   *\n   */\n  get isDelayed() {\n    return this._state.delayed\n  }\n\n  /** Advance the current animation by a number of milliseconds */\n  advance(dt: number) {\n    let idle = true\n    let changed = false\n\n    const anim = this.animation\n    let { toValues } = anim\n    const { config } = anim\n\n    const payload = getPayload(anim.to)\n    if (!payload && hasFluidValue(anim.to)) {\n      toValues = toArray(getFluidValue(anim.to)) as any\n    }\n\n    anim.values.forEach((node, i) => {\n      if (node.done) return\n\n      const to =\n        // Animated strings always go from 0 to 1.\n        node.constructor == AnimatedString\n          ? 1\n          : payload\n            ? payload[i].lastPosition\n            : toValues![i]\n\n      let finished = anim.immediate\n      let position = to\n\n      if (!finished) {\n        position = node.lastPosition\n\n        // Loose springs never move.\n        if (config.tension <= 0) {\n          node.done = true\n          return\n        }\n\n        let elapsed = (node.elapsedTime += dt)\n        const from = anim.fromValues[i]\n\n        const v0 =\n          node.v0 != null\n            ? node.v0\n            : (node.v0 = is.arr(config.velocity)\n                ? config.velocity[i]\n                : config.velocity)\n\n        let velocity: number\n\n        /** The smallest distance from a value before being treated like said value. */\n        /**\n         * TODO: make this value ~0.0001 by default in next breaking change\n         * for more info see – https://github.com/pmndrs/react-spring/issues/1389\n         */\n        const precision =\n          config.precision ||\n          (from == to ? 0.005 : Math.min(1, Math.abs(to - from) * 0.001))\n\n        // Duration easing\n        if (!is.und(config.duration)) {\n          let p = 1\n          if (config.duration > 0) {\n            /**\n             * Here we check if the duration has changed in the config\n             * and if so update the elapsed time to the percentage\n             * of completition so there is no jank in the animation\n             * https://github.com/pmndrs/react-spring/issues/1163\n             */\n            if (this._memoizedDuration !== config.duration) {\n              // update the memoized version to the new duration\n              this._memoizedDuration = config.duration\n\n              // if the value has started animating we need to update it\n              if (node.durationProgress > 0) {\n                // set elapsed time to be the same percentage of progress as the previous duration\n                node.elapsedTime = config.duration * node.durationProgress\n                // add the delta so the below updates work as expected\n                elapsed = node.elapsedTime += dt\n              }\n            }\n\n            // calculate the new progress\n            p = (config.progress || 0) + elapsed / this._memoizedDuration\n            // p is clamped between 0-1\n            p = p > 1 ? 1 : p < 0 ? 0 : p\n            // store our new progress\n            node.durationProgress = p\n          }\n\n          position = from + config.easing(p) * (to - from)\n          velocity = (position - node.lastPosition) / dt\n\n          finished = p == 1\n        }\n\n        // Decay easing\n        else if (config.decay) {\n          const decay = config.decay === true ? 0.998 : config.decay\n          const e = Math.exp(-(1 - decay) * elapsed)\n\n          position = from + (v0 / (1 - decay)) * (1 - e)\n          finished = Math.abs(node.lastPosition - position) <= precision\n\n          // derivative of position\n          velocity = v0 * e\n        }\n\n        // Spring easing\n        else {\n          velocity = node.lastVelocity == null ? v0 : node.lastVelocity\n\n          /** The velocity at which movement is essentially none */\n          const restVelocity = config.restVelocity || precision / 10\n\n          // Bouncing is opt-in (not to be confused with overshooting)\n          const bounceFactor = config.clamp ? 0 : config.bounce!\n          const canBounce = !is.und(bounceFactor)\n\n          /** When `true`, the value is increasing over time */\n          const isGrowing = from == to ? node.v0 > 0 : from < to\n\n          /** When `true`, the velocity is considered moving */\n          let isMoving!: boolean\n\n          /** When `true`, the velocity is being deflected or clamped */\n          let isBouncing = false\n\n          const step = 1 // 1ms\n          const numSteps = Math.ceil(dt / step)\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity\n\n            if (!isMoving) {\n              finished = Math.abs(to - position) <= precision\n              if (finished) {\n                break\n              }\n            }\n\n            if (canBounce) {\n              isBouncing = position == to || position > to == isGrowing\n\n              // Invert the velocity with a magnitude, or clamp it.\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor\n                position = to\n              }\n            }\n\n            const springForce = -config.tension * 0.000001 * (position - to)\n            const dampingForce = -config.friction * 0.001 * velocity\n            const acceleration = (springForce + dampingForce) / config.mass // pt/ms^2\n\n            velocity = velocity + acceleration * step // pt/ms\n            position = position + velocity * step\n          }\n        }\n\n        node.lastVelocity = velocity\n\n        if (Number.isNaN(position)) {\n          console.warn(`Got NaN while animating:`, this)\n          finished = true\n        }\n      }\n\n      // Parent springs must finish before their children can.\n      if (payload && !payload[i].done) {\n        finished = false\n      }\n\n      if (finished) {\n        node.done = true\n      } else {\n        idle = false\n      }\n\n      if (node.setValue(position, config.round)) {\n        changed = true\n      }\n    })\n\n    const node = getAnimated(this)!\n    /**\n     * Get the node's current value, this will be different\n     * to anim.to when config.decay is true\n     */\n    const currVal = node.getValue()\n    if (idle) {\n      // get our final fluid val from the anim.to\n      const finalVal = getFluidValue(anim.to)\n      /**\n       * check if they're not equal, or if they're\n       * change and if there's no config.decay set\n       */\n      if ((currVal !== finalVal || changed) && !config.decay) {\n        // set the value to anim.to\n        node.setValue(finalVal)\n        this._onChange(finalVal)\n      } else if (changed && config.decay) {\n        /**\n         * if it's changed but there is a config.decay,\n         * just call _onChange with currrent value\n         */\n        this._onChange(currVal)\n      }\n      // call stop because the spring has stopped.\n      this._stop()\n    } else if (changed) {\n      /**\n       * if the spring has changed, but is not idle,\n       * just call the _onChange handler\n       */\n      this._onChange(currVal)\n    }\n  }\n\n  /** Set the current value, while stopping the current animation */\n  set(value: T | FluidValue<T>) {\n    raf.batchedUpdates(() => {\n      this._stop()\n\n      // These override the current value and goal value that may have\n      // been updated by `onRest` handlers in the `_stop` call above.\n      this._focus(value)\n      this._set(value)\n    })\n    return this\n  }\n\n  /**\n   * Freeze the active animation in time, as well as any updates merged\n   * before `resume` is called.\n   */\n  pause() {\n    this._update({ pause: true })\n  }\n\n  /** Resume the animation if paused. */\n  resume() {\n    this._update({ pause: false })\n  }\n\n  /** Skip to the end of the current animation. */\n  finish() {\n    if (isAnimating(this)) {\n      const { to, config } = this.animation\n      raf.batchedUpdates(() => {\n        // Ensure the \"onStart\" and \"onRest\" props are called.\n        this._onStart()\n\n        // Jump to the goal value, except for decay animations\n        // which have an undefined goal value.\n        if (!config.decay) {\n          this._set(to, false)\n        }\n\n        this._stop()\n      })\n    }\n    return this\n  }\n\n  /** Push props into the pending queue. */\n  update(props: SpringUpdate<T>) {\n    const queue = this.queue || (this.queue = [])\n    queue.push(props)\n    return this\n  }\n\n  /**\n   * Update this value's animation using the queue of pending props,\n   * and unpause the current animation (if one is frozen).\n   *\n   * When arguments are passed, a new animation is created, and the\n   * queued animations are left alone.\n   */\n  start(): AsyncResult<this>\n\n  start(props: SpringUpdate<T>): AsyncResult<this>\n\n  start(to: T, props?: SpringProps<T>): AsyncResult<this>\n\n  start(to?: any, arg2?: any) {\n    let queue: SpringUpdate<T>[]\n    if (!is.und(to)) {\n      queue = [is.obj(to) ? to : { ...arg2, to }]\n    } else {\n      queue = this.queue || []\n      this.queue = []\n    }\n\n    return Promise.all(\n      queue.map(props => {\n        const up = this._update(props)\n        return up\n      })\n    ).then(results => getCombinedResult(this, results))\n  }\n\n  /**\n   * Stop the current animation, and cancel any delayed updates.\n   *\n   * Pass `true` to call `onRest` with `cancelled: true`.\n   */\n  stop(cancel?: boolean) {\n    const { to } = this.animation\n\n    // The current value becomes the goal value.\n    this._focus(this.get())\n\n    stopAsync(this._state, cancel && this._lastCallId)\n    raf.batchedUpdates(() => this._stop(to, cancel))\n\n    return this\n  }\n\n  /** Restart the animation. */\n  reset() {\n    this._update({ reset: true })\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    if (event.type == 'change') {\n      this._start()\n    } else if (event.type == 'priority') {\n      this.priority = event.priority + 1\n    }\n  }\n\n  /**\n   * Parse the `to` and `from` range from the given `props` object.\n   *\n   * This also ensures the initial value is available to animated components\n   * during the render phase.\n   */\n  protected _prepareNode(props: {\n    to?: any\n    from?: any\n    reverse?: boolean\n    default?: any\n  }) {\n    const key = this.key || ''\n\n    let { to, from } = props\n\n    to = is.obj(to) ? to[key] : to\n    if (to == null || isAsyncTo(to)) {\n      to = undefined\n    }\n\n    from = is.obj(from) ? from[key] : from\n    if (from == null) {\n      from = undefined\n    }\n\n    // Create the range now to avoid \"reverse\" logic.\n    const range = { to, from }\n\n    // Before ever animating, this method ensures an `Animated` node\n    // exists and keeps its value in sync with the \"from\" prop.\n    if (!hasAnimated(this)) {\n      if (props.reverse) [to, from] = [from, to]\n\n      from = getFluidValue(from)\n      if (!is.und(from)) {\n        this._set(from)\n      }\n      // Use the \"to\" value if our node is undefined.\n      else if (!getAnimated(this)) {\n        this._set(to)\n      }\n    }\n\n    return range\n  }\n\n  /** Every update is processed by this method before merging. */\n  protected _update(\n    { ...props }: SpringProps<T>,\n    isLoop?: boolean\n  ): AsyncResult<SpringValue<T>> {\n    const { key, defaultProps } = this\n\n    // Update the default props immediately.\n    if (props.default)\n      Object.assign(\n        defaultProps,\n        getDefaultProps(props, (value, prop) =>\n          /^on/.test(prop) ? resolveProp(value, key) : value\n        )\n      )\n\n    mergeActiveFn(this, props, 'onProps')\n    sendEvent(this, 'onProps', props, this)\n\n    // Ensure the initial value can be accessed by animated components.\n    const range = this._prepareNode(props)\n\n    if (Object.isFrozen(this)) {\n      throw Error(\n        'Cannot animate a `SpringValue` object that is frozen. ' +\n          'Did you forget to pass your component to `animated(...)` before animating its props?'\n      )\n    }\n\n    const state = this._state\n\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true)\n            flushCalls(state.pauseQueue)\n            sendEvent(\n              this,\n              'onPause',\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            )\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false)\n            if (isAnimating(this)) {\n              this._resume()\n            }\n            flushCalls(state.resumeQueue)\n            sendEvent(\n              this,\n              'onResume',\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            )\n          }\n        },\n        start: this._merge.bind(this, range),\n      },\n    }).then(result => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props)\n        if (nextProps) {\n          return this._update(nextProps, true)\n        }\n      }\n      return result\n    })\n  }\n\n  /** Merge props into the current animation */\n  protected _merge(\n    range: AnimationRange<T>,\n    props: RunAsyncProps<SpringValue<T>>,\n    resolve: AnimationResolver<SpringValue<T>>\n  ): void {\n    // The \"cancel\" prop cancels all pending delays and it forces the\n    // active animation to stop where it is.\n    if (props.cancel) {\n      this.stop(true)\n      return resolve(getCancelledResult(this))\n    }\n\n    /** The \"to\" prop is defined. */\n    const hasToProp = !is.und(range.to)\n\n    /** The \"from\" prop is defined. */\n    const hasFromProp = !is.und(range.from)\n\n    // Avoid merging other props if implicitly prevented, except\n    // when both the \"to\" and \"from\" props are undefined.\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId\n      } else {\n        return resolve(getCancelledResult(this))\n      }\n    }\n\n    const { key, defaultProps, animation: anim } = this\n    const { to: prevTo, from: prevFrom } = anim\n    let { to = prevTo, from = prevFrom } = range\n\n    // Focus the \"from\" value if changing without a \"to\" value.\n    // For default updates, do this only if no \"to\" value exists.\n    if (hasFromProp && !hasToProp && (!props.default || is.und(to))) {\n      to = from\n    }\n\n    // Flip the current range if \"reverse\" is true.\n    if (props.reverse) [to, from] = [from, to]\n\n    /** The \"from\" value is changing. */\n    const hasFromChanged = !isEqual(from, prevFrom)\n\n    if (hasFromChanged) {\n      anim.from = from\n    }\n\n    // Coerce \"from\" into a static value.\n    from = getFluidValue(from)\n\n    /** The \"to\" value is changing. */\n    const hasToChanged = !isEqual(to, prevTo)\n\n    if (hasToChanged) {\n      this._focus(to)\n    }\n\n    /** The \"to\" prop is async. */\n    const hasAsyncTo = isAsyncTo(props.to)\n\n    const { config } = anim\n    const { decay, velocity } = config\n\n    // Reset to default velocity when goal values are defined.\n    if (hasToProp || hasFromProp) {\n      config.velocity = 0\n    }\n\n    // The \"runAsync\" function treats the \"config\" prop as a default,\n    // so we must avoid merging it when the \"to\" prop is async.\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(\n        config,\n        callProp(props.config, key!),\n        // Avoid calling the same \"config\" prop twice.\n        props.config !== defaultProps.config\n          ? callProp(defaultProps.config, key!)\n          : void 0\n      )\n    }\n\n    // This instance might not have its Animated node yet. For example,\n    // the constructor can be given props without a \"to\" or \"from\" value.\n    let node = getAnimated(this)\n    if (!node || is.und(to)) {\n      return resolve(getFinishedResult(this, true))\n    }\n\n    /** When true, start at the \"from\" value. */\n    const reset =\n      // When `reset` is undefined, the `from` prop implies `reset: true`,\n      // except for declarative updates. When `reset` is defined, there\n      // must exist a value to animate from.\n      is.und(props.reset)\n        ? hasFromProp && !props.default\n        : !is.und(from) && matchProp(props.reset, key)\n\n    // The current value, where the animation starts from.\n    const value = reset ? (from as T) : this.get()\n\n    // The animation ends at this value, unless \"to\" is fluid.\n    const goal = computeGoal<any>(to)\n\n    // Only specific types can be animated to/from.\n    const isAnimatable = is.num(goal) || is.arr(goal) || isAnimatedString(goal)\n\n    // When true, the value changes instantly on the next frame.\n    const immediate =\n      !hasAsyncTo &&\n      (!isAnimatable ||\n        matchProp(defaultProps.immediate || props.immediate, key))\n\n    if (hasToChanged) {\n      const nodeType = getAnimatedType(to)\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal)!\n        } else\n          throw Error(\n            `Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the \"to\" prop suggests`\n          )\n      }\n    }\n\n    // The type of Animated node for the goal value.\n    const goalType = node.constructor\n\n    // When the goal value is fluid, we don't know if its value\n    // will change before the next animation frame, so it always\n    // starts the animation to be safe.\n    let started = hasFluidValue(to)\n    let finished = false\n\n    if (!started) {\n      // When true, the current value has probably changed.\n      const hasValueChanged = reset || (!hasAnimated(this) && hasFromChanged)\n\n      // When the \"to\" value or current value are changed,\n      // start animating if not already finished.\n      if (hasToChanged || hasValueChanged) {\n        finished = isEqual(computeGoal(value), goal)\n        started = !finished\n      }\n\n      // Changing \"decay\" or \"velocity\" starts the animation.\n      if (\n        (!isEqual(anim.immediate, immediate) && !immediate) ||\n        !isEqual(config.decay, decay) ||\n        !isEqual(config.velocity, velocity)\n      ) {\n        started = true\n      }\n    }\n\n    // Was the goal value set to the current value while animating?\n    if (finished && isAnimating(this)) {\n      // If the first frame has passed, allow the animation to\n      // overshoot instead of stopping abruptly.\n      if (anim.changed && !reset) {\n        started = true\n      }\n      // Stop the animation before its first frame.\n      else if (!started) {\n        this._stop(prevTo)\n      }\n    }\n\n    if (!hasAsyncTo) {\n      // Make sure our \"toValues\" are updated even if our previous\n      // \"to\" prop is a fluid value whose current value is also ours.\n      if (started || hasFluidValue(prevTo)) {\n        anim.values = node.getPayload()\n        anim.toValues = hasFluidValue(to)\n          ? null\n          : goalType == AnimatedString\n            ? [1]\n            : toArray(goal)\n      }\n\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate\n\n        // Ensure the immediate goal is used as from value.\n        if (!immediate && !reset) {\n          this._set(prevTo)\n        }\n      }\n\n      if (started) {\n        const { onRest } = anim\n\n        // Set the active handlers when an animation starts.\n        each(ACTIVE_EVENTS, type => mergeActiveFn(this, props, type))\n\n        const result = getFinishedResult(this, checkFinished(this, prevTo))\n        flushCalls(this._pendingCalls, result)\n        this._pendingCalls.add(resolve)\n\n        if (anim.changed)\n          raf.batchedUpdates(() => {\n            // Ensure `onStart` can be called after a reset.\n            anim.changed = !reset\n\n            // Call the active `onRest` handler from the interrupted animation.\n            onRest?.(result, this)\n\n            // Notify the default `onRest` of the reset, but wait for the\n            // first frame to pass before sending an `onStart` event.\n            if (reset) {\n              callProp(defaultProps.onRest, result)\n            }\n            // Call the active `onStart` handler here since the first frame\n            // has already passed, which means this is a goal update and not\n            // an entirely new animation.\n            else {\n              anim.onStart?.(result, this)\n            }\n          })\n      }\n    }\n\n    if (reset) {\n      this._set(value)\n    }\n\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this))\n    }\n\n    // Start an animation\n    else if (started) {\n      this._start()\n    }\n\n    // Postpone promise resolution until the animation is finished,\n    // so that no-op updates still resolve at the expected time.\n    else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve)\n    }\n\n    // Resolve our promise immediately.\n    else {\n      resolve(getNoopResult(value))\n    }\n  }\n\n  /** Update the `animation.to` value, which might be a `FluidValue` */\n  protected _focus(value: T | FluidValue<T>) {\n    const anim = this.animation\n    if (value !== anim.to) {\n      if (getFluidObservers(this)) {\n        this._detach()\n      }\n      anim.to = value\n      if (getFluidObservers(this)) {\n        this._attach()\n      }\n    }\n  }\n\n  protected _attach() {\n    let priority = 0\n\n    const { to } = this.animation\n    if (hasFluidValue(to)) {\n      addFluidObserver(to, this)\n      if (isFrameValue(to)) {\n        priority = to.priority + 1\n      }\n    }\n\n    this.priority = priority\n  }\n\n  protected _detach() {\n    const { to } = this.animation\n    if (hasFluidValue(to)) {\n      removeFluidObserver(to, this)\n    }\n  }\n\n  /**\n   * Update the current value from outside the frameloop,\n   * and return the `Animated` node.\n   */\n  protected _set(arg: T | FluidValue<T>, idle = true): Animated | undefined {\n    const value = getFluidValue(arg)\n    if (!is.und(value)) {\n      const oldNode = getAnimated(this)\n      if (!oldNode || !isEqual(value, oldNode.getValue())) {\n        // Create a new node or update the existing node.\n        const nodeType = getAnimatedType(value)\n        if (!oldNode || oldNode.constructor != nodeType) {\n          setAnimated(this, nodeType.create(value))\n        } else {\n          oldNode.setValue(value)\n        }\n        // Never emit a \"change\" event for the initial value.\n        if (oldNode) {\n          raf.batchedUpdates(() => {\n            this._onChange(value, idle)\n          })\n        }\n      }\n    }\n    return getAnimated(this)\n  }\n\n  protected _onStart() {\n    const anim = this.animation\n    if (!anim.changed) {\n      anim.changed = true\n      sendEvent(\n        this,\n        'onStart',\n        getFinishedResult(this, checkFinished(this, anim.to)),\n        this\n      )\n    }\n  }\n\n  protected _onChange(value: T, idle?: boolean) {\n    if (!idle) {\n      this._onStart()\n      callProp(this.animation.onChange, value, this)\n    }\n    callProp(this.defaultProps.onChange, value, this)\n    super._onChange(value, idle)\n  }\n\n  // This method resets the animation state (even if already animating) to\n  // ensure the latest from/to range is used, and it also ensures this spring\n  // is added to the frameloop.\n  protected _start() {\n    const anim = this.animation\n\n    // Reset the state of each Animated node.\n    getAnimated(this)!.reset(getFluidValue(anim.to))\n\n    // Use the current values as the from values.\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map(node => node.lastPosition)\n    }\n\n    if (!isAnimating(this)) {\n      setActiveBit(this, true)\n      if (!isPaused(this)) {\n        this._resume()\n      }\n    }\n  }\n\n  protected _resume() {\n    // The \"skipAnimation\" global avoids the frameloop.\n    if (G.skipAnimation) {\n      this.finish()\n    } else {\n      frameLoop.start(this)\n    }\n  }\n\n  /**\n   * Exit the frameloop and notify `onRest` listeners.\n   *\n   * Always wrap `_stop` calls with `batchedUpdates`.\n   */\n  protected _stop(goal?: any, cancel?: boolean) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false)\n\n      const anim = this.animation\n      each(anim.values, node => {\n        node.done = true\n      })\n\n      // These active handlers must be reset to undefined or else\n      // they could be called while idle. But keep them defined\n      // when the goal value is dynamic.\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = undefined\n      }\n\n      callFluidObservers(this, {\n        type: 'idle',\n        parent: this,\n      })\n\n      const result = cancel\n        ? getCancelledResult(this.get())\n        : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to))\n\n      flushCalls(this._pendingCalls, result)\n      if (anim.changed) {\n        anim.changed = false\n        sendEvent(this, 'onRest', result, this)\n      }\n    }\n  }\n}\n\n/** Returns true when the current value and goal value are equal. */\nfunction checkFinished<T>(target: SpringValue<T>, to: T | FluidValue<T>) {\n  const goal = computeGoal(to)\n  const value = computeGoal(target.get())\n  return isEqual(value, goal)\n}\n\nexport function createLoopUpdate<T>(\n  props: T & { loop?: any; to?: any; from?: any; reverse?: any },\n  loop = props.loop,\n  to = props.to\n): T | undefined {\n  const loopRet = callProp(loop)\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet)\n    const reverse = (overrides || props).reverse\n    const reset = !overrides || overrides.reset\n    return createUpdate({\n      ...props,\n      loop,\n\n      // Avoid updating default props when looping.\n      default: false,\n\n      // Never loop the `pause` prop.\n      pause: undefined,\n\n      // For the \"reverse\" prop to loop as expected, the \"to\" prop\n      // must be undefined. The \"reverse\" prop is ignored when the\n      // \"to\" prop is an array or function.\n      to: !reverse || isAsyncTo(to) ? to : undefined,\n\n      // Ignore the \"from\" prop except on reset.\n      from: reset ? props.from : undefined,\n      reset,\n\n      // The \"loop\" prop can return a \"useSpring\" props object to\n      // override any of the original props.\n      ...overrides,\n    })\n  }\n}\n\n/**\n * Return a new object based on the given `props`.\n *\n * - All non-reserved props are moved into the `to` prop object.\n * - The `keys` prop is set to an array of affected keys,\n *   or `null` if all keys are affected.\n */\nexport function createUpdate(props: any) {\n  const { to, from } = (props = inferTo(props))\n\n  // Collect the keys affected by this update.\n  const keys = new Set<string>()\n\n  if (is.obj(to)) findDefined(to, keys)\n  if (is.obj(from)) findDefined(from, keys)\n\n  // The \"keys\" prop helps in applying updates to affected keys only.\n  props.keys = keys.size ? Array.from(keys) : null\n\n  return props\n}\n\n/**\n * A modified version of `createUpdate` meant for declarative APIs.\n */\nexport function declareUpdate(props: any) {\n  const update = createUpdate(props)\n  if (is.und(update.default)) {\n    update.default = getDefaultProps(update)\n  }\n  return update\n}\n\n/** Find keys with defined values */\nfunction findDefined(values: Lookup, keys: Set<string>) {\n  eachProp(values, (value, key) => value != null && keys.add(key as any))\n}\n\n/** Event props with \"active handler\" support */\nconst ACTIVE_EVENTS = [\n  'onStart',\n  'onRest',\n  'onChange',\n  'onPause',\n  'onResume',\n] as const\n\nfunction mergeActiveFn<T, P extends EventKey>(\n  target: SpringValue<T>,\n  props: SpringProps<T>,\n  type: P\n) {\n  target.animation[type] =\n    props[type] !== getDefaultProp(props, type)\n      ? resolveProp<any>(props[type], target.key)\n      : undefined\n}\n\ntype EventArgs<T, P extends EventKey> = Parameters<\n  Extract<SpringProps<T>[P], Function>\n>\n\n/** Call the active handler first, then the default handler. */\nfunction sendEvent<T, P extends EventKey>(\n  target: SpringValue<T>,\n  type: P,\n  ...args: EventArgs<T, P>\n) {\n  target.animation[type]?.(...(args as [any, any]))\n  target.defaultProps[type]?.(...(args as [any, any]))\n}\n", "import { is, easings } from '@react-spring/shared'\nimport { EasingFunction } from '@react-spring/types'\nimport { config as configs } from './constants'\n\nconst defaults: any = {\n  ...configs.default,\n  mass: 1,\n  damping: 1,\n  easing: easings.linear,\n  clamp: false,\n}\n\nexport class AnimationConfig {\n  /**\n   * With higher tension, the spring will resist bouncing and try harder to stop at its end value.\n   *\n   * When tension is zero, no animation occurs.\n   *\n   * @default 170\n   */\n  tension!: number\n\n  /**\n   * The damping ratio coefficient, or just the damping ratio when `speed` is defined.\n   *\n   * When `speed` is defined, this value should be between 0 and 1.\n   *\n   * Higher friction means the spring will slow down faster.\n   *\n   * @default 26\n   */\n  friction!: number\n\n  /**\n   * The natural frequency (in seconds), which dictates the number of bounces\n   * per second when no damping exists.\n   *\n   * When defined, `tension` is derived from this, and `friction` is derived\n   * from `tension` and `damping`.\n   */\n  frequency?: number\n\n  /**\n   * The damping ratio, which dictates how the spring slows down.\n   *\n   * Set to `0` to never slow down. Set to `1` to slow down without bouncing.\n   * Between `0` and `1` is for you to explore.\n   *\n   * Only works when `frequency` is defined.\n   *\n   * @default 1\n   */\n  damping!: number\n\n  /**\n   * Higher mass means more friction is required to slow down.\n   *\n   * Defaults to 1, which works fine most of the time.\n   *\n   * @default 1\n   */\n  mass!: number\n\n  /**\n   * The initial velocity of one or more values.\n   *\n   * @default 0\n   */\n  velocity: number | number[] = 0\n\n  /**\n   * The smallest velocity before the animation is considered \"not moving\".\n   *\n   * When undefined, `precision` is used instead.\n   */\n  restVelocity?: number\n\n  /**\n   * The smallest distance from a value before that distance is essentially zero.\n   *\n   * This helps in deciding when a spring is \"at rest\". The spring must be within\n   * this distance from its final value, and its velocity must be lower than this\n   * value too (unless `restVelocity` is defined).\n   *\n   * @default 0.01\n   */\n  precision?: number\n\n  /**\n   * For `duration` animations only. Note: The `duration` is not affected\n   * by this property.\n   *\n   * Defaults to `0`, which means \"start from the beginning\".\n   *\n   * Setting to `1+` makes an immediate animation.\n   *\n   * Setting to `0.5` means \"start from the middle of the easing function\".\n   *\n   * Any number `>= 0` and `<= 1` makes sense here.\n   */\n  progress?: number\n\n  /**\n   * Animation length in number of milliseconds.\n   */\n  duration?: number\n\n  /**\n   * The animation curve. Only used when `duration` is defined.\n   *\n   * Defaults to quadratic ease-in-out.\n   */\n  easing!: EasingFunction\n\n  /**\n   * Avoid overshooting by ending abruptly at the goal value.\n   *\n   * @default false\n   */\n  clamp!: boolean\n\n  /**\n   * When above zero, the spring will bounce instead of overshooting when\n   * exceeding its goal value. Its velocity is multiplied by `-1 + bounce`\n   * whenever its current value equals or exceeds its goal. For example,\n   * setting `bounce` to `0.5` chops the velocity in half on each bounce,\n   * in addition to any friction.\n   */\n  bounce?: number\n\n  /**\n   * \"Decay animations\" decelerate without an explicit goal value.\n   * Useful for scrolling animations.\n   *\n   * Use `true` for the default exponential decay factor (`0.998`).\n   *\n   * When a `number` between `0` and `1` is given, a lower number makes the\n   * animation slow down faster. And setting to `1` would make an unending\n   * animation.\n   *\n   * @default false\n   */\n  decay?: boolean | number\n\n  /**\n   * While animating, round to the nearest multiple of this number.\n   * The `from` and `to` values are never rounded, as well as any value\n   * passed to the `set` method of an animated value.\n   */\n  round?: number\n\n  constructor() {\n    Object.assign(this, defaults)\n  }\n}\n\nexport function mergeConfig(\n  config: AnimationConfig,\n  newConfig: Partial<AnimationConfig>,\n  defaultConfig?: Partial<AnimationConfig>\n): typeof config\n\nexport function mergeConfig(\n  config: any,\n  newConfig: object,\n  defaultConfig?: object\n) {\n  if (defaultConfig) {\n    defaultConfig = { ...defaultConfig }\n    sanitizeConfig(defaultConfig, newConfig)\n    newConfig = { ...defaultConfig, ...newConfig }\n  }\n\n  sanitizeConfig(config, newConfig)\n  Object.assign(config, newConfig)\n\n  for (const key in defaults) {\n    if (config[key] == null) {\n      config[key] = defaults[key]\n    }\n  }\n\n  let { frequency, damping } = config\n  const { mass } = config\n  if (!is.und(frequency)) {\n    if (frequency < 0.01) frequency = 0.01\n    if (damping < 0) damping = 0\n    config.tension = Math.pow((2 * Math.PI) / frequency, 2) * mass\n    config.friction = (4 * Math.PI * damping * mass) / frequency\n  }\n\n  return config\n}\n\n// Prevent a config from accidentally overriding new props.\n// This depends on which \"config\" props take precedence when defined.\nfunction sanitizeConfig(\n  config: Partial<AnimationConfig>,\n  props: Partial<AnimationConfig>\n) {\n  if (!is.und(props.decay)) {\n    config.duration = undefined\n  } else {\n    const isTensionConfig = !is.und(props.tension) || !is.und(props.friction)\n    if (\n      isTensionConfig ||\n      !is.und(props.frequency) ||\n      !is.und(props.damping) ||\n      !is.und(props.mass)\n    ) {\n      config.duration = undefined\n      config.decay = undefined\n    }\n    if (isTensionConfig) {\n      config.frequency = undefined\n    }\n  }\n}\n", "// The `mass` prop defaults to 1\nexport const config = {\n  default: { tension: 170, friction: 26 },\n  gentle: { tension: 120, friction: 14 },\n  wobbly: { tension: 180, friction: 12 },\n  stiff: { tension: 210, friction: 20 },\n  slow: { tension: 280, friction: 60 },\n  molasses: { tension: 280, friction: 120 },\n} as const\n", "import { AnimatedValue } from '@react-spring/animated'\nimport { FluidValue } from '@react-spring/shared'\nimport { AnimationConfig } from './AnimationConfig'\nimport { PickEventFns } from './types/internal'\nimport { SpringProps } from './types'\n\nconst emptyArray: readonly any[] = []\n\n/** An animation being executed by the frameloop */\n// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging\nexport class Animation<T = any> {\n  changed = false\n  values: readonly AnimatedValue[] = emptyArray\n  toValues: readonly number[] | null = null\n  fromValues: readonly number[] = emptyArray\n\n  to!: T | FluidValue<T>\n  from!: T | FluidValue<T>\n  config = new AnimationConfig()\n  immediate = false\n}\n\nexport interface Animation<T> extends PickEventFns<SpringProps<T>> {}\n", "import { Timeout, is, raf, Globals as G } from '@react-spring/shared'\nimport { matchProp, callProp } from './helpers'\nimport { AsyncResult, MatchProp } from './types'\nimport { RunAsyncState, RunAsyncProps } from './runAsync'\nimport {\n  AnimationResolver,\n  AnimationTarget,\n  InferProps,\n  InferState,\n} from './types/internal'\n\n// The `scheduleProps` function only handles these defaults.\ntype DefaultProps<T> = { cancel?: MatchProp<T>; pause?: MatchProp<T> }\n\ninterface ScheduledProps<T extends AnimationTarget> {\n  key?: string\n  props: InferProps<T>\n  defaultProps?: DefaultProps<InferState<T>>\n  state: RunAsyncState<T>\n  actions: {\n    pause: () => void\n    resume: () => void\n    start: (props: RunAsyncProps<T>, resolve: AnimationResolver<T>) => void\n  }\n}\n\n/**\n * This function sets a timeout if both the `delay` prop exists and\n * the `cancel` prop is not `true`.\n *\n * The `actions.start` function must handle the `cancel` prop itself,\n * but the `pause` prop is taken care of.\n */\nexport function scheduleProps<T extends AnimationTarget>(\n  callId: number,\n  { key, props, defaultProps, state, actions }: ScheduledProps<T>\n): AsyncResult<T> {\n  return new Promise((resolve, reject) => {\n    let delay: number\n    let timeout: Timeout\n\n    let cancel = matchProp(props.cancel ?? defaultProps?.cancel, key)\n    if (cancel) {\n      onStart()\n    } else {\n      // The `pause` prop updates the paused flag.\n      if (!is.und(props.pause)) {\n        state.paused = matchProp(props.pause, key)\n      }\n      // The default `pause` takes precedence when true,\n      // which allows `SpringContext` to work as expected.\n      let pause = defaultProps?.pause\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key)\n      }\n\n      delay = callProp(props.delay || 0, key)\n      if (pause) {\n        state.resumeQueue.add(onResume)\n        actions.pause()\n      } else {\n        actions.resume()\n        onResume()\n      }\n    }\n\n    function onPause() {\n      state.resumeQueue.add(onResume)\n      state.timeouts.delete(timeout)\n      timeout.cancel()\n      // Cache the remaining delay.\n      delay = timeout.time - raf.now()\n    }\n\n    function onResume() {\n      if (delay > 0 && !G.skipAnimation) {\n        state.delayed = true\n        timeout = raf.setTimeout(onStart, delay)\n        state.pauseQueue.add(onPause)\n        state.timeouts.add(timeout)\n      } else {\n        onStart()\n      }\n    }\n\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false\n      }\n\n      state.pauseQueue.delete(onPause)\n      state.timeouts.delete(timeout)\n\n      // Maybe cancelled during its delay.\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true\n      }\n\n      try {\n        actions.start({ ...props, callId, cancel }, resolve)\n      } catch (err) {\n        reject(err)\n      }\n    }\n  })\n}\n", "import {\n  is,\n  raf,\n  flush,\n  eachProp,\n  Timeout,\n  Globals as G,\n} from '@react-spring/shared'\nimport { Falsy } from '@react-spring/types'\n\nimport { getDefaultProps } from './helpers'\nimport { AnimationTarget, InferState, InferProps } from './types/internal'\nimport { AnimationResult, AsyncResult, SpringChain, SpringToFn } from './types'\nimport { getCancelledResult, getFinishedResult } from './AnimationResult'\n\ntype AsyncTo<T> = SpringChain<T> | SpringToFn<T>\n\n/** @internal */\nexport type RunAsyncProps<T extends AnimationTarget = any> = InferProps<T> & {\n  callId: number\n  parentId?: number\n  cancel: boolean\n  to?: any\n}\n\n/** @internal */\nexport interface RunAsyncState<T extends AnimationTarget = any> {\n  paused: boolean\n  pauseQueue: Set<() => void>\n  resumeQueue: Set<() => void>\n  timeouts: Set<Timeout>\n  delayed?: boolean\n  asyncId?: number\n  asyncTo?: AsyncTo<InferState<T>>\n  promise?: AsyncResult<T>\n  cancelId?: number\n}\n\n/**\n * Start an async chain or an async script.\n *\n * Always call `runAsync` in the action callback of a `scheduleProps` call.\n *\n * The `T` parameter can be a set of animated values (as an object type)\n * or a primitive type for a single animated value.\n */\nexport function runAsync<T extends AnimationTarget>(\n  to: AsyncTo<InferState<T>>,\n  props: RunAsyncProps<T>,\n  state: RunAsyncState<T>,\n  target: T\n): AsyncResult<T> {\n  const { callId, parentId, onRest } = props\n  const { asyncTo: prevTo, promise: prevPromise } = state\n\n  if (!parentId && to === prevTo && !props.reset) {\n    return prevPromise!\n  }\n\n  return (state.promise = (async () => {\n    state.asyncId = callId\n    state.asyncTo = to\n\n    // The default props of any `animate` calls.\n    const defaultProps = getDefaultProps<InferProps<T>>(props, (value, key) =>\n      // The `onRest` prop is only called when the `runAsync` promise is resolved.\n      key === 'onRest' ? undefined : value\n    )\n\n    let preventBail!: () => void\n    let bail: (error: any) => void\n\n    // This promise is rejected when the animation is interrupted.\n    const bailPromise = new Promise<void>(\n      (resolve, reject) => ((preventBail = resolve), (bail = reject))\n    )\n\n    const bailIfEnded = (bailSignal: BailSignal) => {\n      const bailResult =\n        // The `cancel` prop or `stop` method was used.\n        (callId <= (state.cancelId || 0) && getCancelledResult(target)) ||\n        // The async `to` prop was replaced.\n        (callId !== state.asyncId && getFinishedResult(target, false))\n\n      if (bailResult) {\n        bailSignal.result = bailResult\n\n        // Reject the `bailPromise` to ensure the `runAsync` promise\n        // is not relying on the caller to rethrow the error for us.\n        bail(bailSignal)\n        throw bailSignal\n      }\n    }\n\n    const animate: any = (arg1: any, arg2?: any) => {\n      // Create the bail signal outside the returned promise,\n      // so the generated stack trace is relevant.\n      const bailSignal = new BailSignal()\n      const skipAnimationSignal = new SkipAnimationSignal()\n\n      return (async () => {\n        if (G.skipAnimation) {\n          /**\n           * We need to stop animations if `skipAnimation`\n           * is set in the Globals\n           *\n           */\n          stopAsync(state)\n\n          // create the rejection error that's handled gracefully\n          skipAnimationSignal.result = getFinishedResult(target, false)\n          bail(skipAnimationSignal)\n          throw skipAnimationSignal\n        }\n\n        bailIfEnded(bailSignal)\n\n        const props: any = is.obj(arg1) ? { ...arg1 } : { ...arg2, to: arg1 }\n        props.parentId = callId\n\n        eachProp(defaultProps, (value, key) => {\n          if (is.und(props[key])) {\n            props[key] = value\n          }\n        })\n\n        const result = await target.start(props)\n        bailIfEnded(bailSignal)\n\n        if (state.paused) {\n          await new Promise<void>(resume => {\n            state.resumeQueue.add(resume)\n          })\n        }\n\n        return result\n      })()\n    }\n\n    let result!: AnimationResult<T>\n\n    if (G.skipAnimation) {\n      /**\n       * We need to stop animations if `skipAnimation`\n       * is set in the Globals\n       */\n      stopAsync(state)\n      return getFinishedResult(target, false)\n    }\n\n    try {\n      let animating!: Promise<void>\n\n      // Async sequence\n      if (is.arr(to)) {\n        animating = (async (queue: any[]) => {\n          for (const props of queue) {\n            await animate(props)\n          }\n        })(to)\n      }\n\n      // Async script\n      else {\n        animating = Promise.resolve(to(animate, target.stop.bind(target)))\n      }\n\n      await Promise.all([animating.then(preventBail), bailPromise])\n      result = getFinishedResult(target.get(), true, false)\n\n      // Bail handling\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result\n      } else {\n        throw err\n      }\n\n      // Reset the async state.\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId\n        state.asyncTo = parentId ? prevTo : undefined\n        state.promise = parentId ? prevPromise : undefined\n      }\n    }\n\n    if (is.fun(onRest)) {\n      raf.batchedUpdates(() => {\n        onRest(result, target, target.item)\n      })\n    }\n\n    return result\n  })())\n}\n\n/** Stop the current `runAsync` call with `finished: false` (or with `cancelled: true` when `cancelId` is defined) */\nexport function stopAsync(state: RunAsyncState, cancelId?: number | Falsy) {\n  flush(state.timeouts, t => t.cancel())\n  state.pauseQueue.clear()\n  state.resumeQueue.clear()\n  state.asyncId = state.asyncTo = state.promise = undefined\n  if (cancelId) state.cancelId = cancelId\n}\n\n/** This error is thrown to signal an interrupted async animation. */\nexport class BailSignal extends Error {\n  result!: AnimationResult\n  constructor() {\n    super(\n      'An async animation has been interrupted. You see this error because you ' +\n        'forgot to use `await` or `.catch(...)` on its returned promise.'\n    )\n  }\n}\n\nexport class SkipAnimationSignal extends Error {\n  result!: AnimationResult\n\n  constructor() {\n    super('SkipAnimationSignal')\n  }\n}\n", "import { AnimationResult } from './types'\nimport { Readable } from './types/internal'\n\n/** @internal */\nexport const getCombinedResult = <T extends Readable>(\n  target: T,\n  results: AnimationResult<T>[]\n): AnimationResult<T> =>\n  results.length == 1\n    ? results[0]\n    : results.some(result => result.cancelled)\n      ? getCancelledResult(target.get())\n      : results.every(result => result.noop)\n        ? getNoopResult(target.get())\n        : getFinishedResult(\n            target.get(),\n            results.every(result => result.finished)\n          )\n\n/** No-op results are for updates that never start an animation. */\nexport const getNoopResult = (value: any) => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false,\n})\n\nexport const getFinishedResult = (\n  value: any,\n  finished: boolean,\n  cancelled = false\n) => ({\n  value,\n  finished,\n  cancelled,\n})\n\nexport const getCancelledResult = (value: any) => ({\n  value,\n  cancelled: true,\n  finished: false,\n})\n", "import {\n  deprecateInterpolate,\n  frameLoop,\n  FluidValue,\n  Globals as G,\n  callFluidObservers,\n} from '@react-spring/shared'\nimport { InterpolatorArgs } from '@react-spring/types'\nimport { getAnimated } from '@react-spring/animated'\n\nimport { Interpolation } from './Interpolation'\n\nexport const isFrameValue = (value: any): value is FrameValue =>\n  value instanceof FrameValue\n\nlet nextId = 1\n\n/**\n * A kind of `FluidValue` that manages an `AnimatedValue` node.\n *\n * Its underlying value can be accessed and even observed.\n */\nexport abstract class FrameValue<T = any> extends FluidValue<\n  T,\n  FrameValue.Event<T>\n> {\n  readonly id = nextId++\n\n  abstract key?: string\n  abstract get idle(): boolean\n\n  protected _priority = 0\n\n  get priority() {\n    return this._priority\n  }\n  set priority(priority: number) {\n    if (this._priority != priority) {\n      this._priority = priority\n      this._onPriorityChange(priority)\n    }\n  }\n\n  /** Get the current value */\n  get(): T {\n    const node = getAnimated(this)\n    return node && node.getValue()\n  }\n\n  /** Create a spring that maps our value to another value */\n  to<Out>(...args: InterpolatorArgs<T, Out>) {\n    return G.to(this, args) as Interpolation<T, Out>\n  }\n\n  /** @deprecated Use the `to` method instead. */\n  interpolate<Out>(...args: InterpolatorArgs<T, Out>) {\n    deprecateInterpolate()\n    return G.to(this, args) as Interpolation<T, Out>\n  }\n\n  toJSON() {\n    return this.get()\n  }\n\n  protected observerAdded(count: number) {\n    if (count == 1) this._attach()\n  }\n\n  protected observerRemoved(count: number) {\n    if (count == 0) this._detach()\n  }\n\n  /** @internal */\n  abstract advance(dt: number): void\n\n  /** @internal */\n  abstract eventObserved(_event: FrameValue.Event): void\n\n  /** Called when the first child is added. */\n  protected _attach() {}\n\n  /** Called when the last child is removed. */\n  protected _detach() {}\n\n  /** Tell our children about our new value */\n  protected _onChange(value: T, idle = false) {\n    callFluidObservers(this, {\n      type: 'change',\n      parent: this,\n      value,\n      idle,\n    })\n  }\n\n  /** Tell our children about our new priority */\n  protected _onPriorityChange(priority: number) {\n    if (!this.idle) {\n      frameLoop.sort(this)\n    }\n    callFluidObservers(this, {\n      type: 'priority',\n      parent: this,\n      priority,\n    })\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport declare namespace FrameValue {\n  /** A parent changed its value */\n  interface ChangeEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'change'\n    value: T\n    idle: boolean\n  }\n\n  /** A parent changed its priority */\n  interface PriorityEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'priority'\n    priority: number\n  }\n\n  /** A parent is done animating */\n  interface IdleEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'idle'\n  }\n\n  /** Events sent to children of `FrameValue` objects */\n  export type Event<T = any> = ChangeEvent<T> | PriorityEvent<T> | IdleEvent<T>\n}\n", "/** The property symbol of the current animation phase. */\nconst $P = Symbol.for('SpringPhase')\n\nconst HAS_ANIMATED = 1\nconst IS_ANIMATING = 2\nconst IS_PAUSED = 4\n\n/** Returns true if the `target` has ever animated. */\nexport const hasAnimated = (target: any) => (target[$P] & HAS_ANIMATED) > 0\n\n/** Returns true if the `target` is animating (even if paused). */\nexport const isAnimating = (target: any) => (target[$P] & IS_ANIMATING) > 0\n\n/** Returns true if the `target` is paused (even if idle). */\nexport const isPaused = (target: any) => (target[$P] & IS_PAUSED) > 0\n\n/** Set the active bit of the `target` phase. */\nexport const setActiveBit = (target: any, active: boolean) =>\n  active\n    ? (target[$P] |= IS_ANIMATING | HAS_ANIMATED)\n    : (target[$P] &= ~IS_ANIMATING)\n\nexport const setPausedBit = (target: any, paused: boolean) =>\n  paused ? (target[$P] |= IS_PAUSED) : (target[$P] &= ~IS_PAUSED)\n", "import { <PERSON><PERSON>r<PERSON>ore, UnknownProps, Lookup, Falsy } from '@react-spring/types'\nimport {\n  is,\n  raf,\n  each,\n  noop,\n  flush,\n  toArray,\n  eachProp,\n  flushCalls,\n  addFluidObserver,\n  FluidObserver,\n} from '@react-spring/shared'\n\nimport { getDefaultProp } from './helpers'\nimport { FrameValue } from './FrameValue'\nimport type { SpringRef } from './SpringRef'\nimport { SpringValue, createLoopUpdate, createUpdate } from './SpringValue'\nimport { getCancelledResult, getCombinedResult } from './AnimationResult'\nimport { runAsync, RunAsyncState, stopAsync } from './runAsync'\nimport { scheduleProps } from './scheduleProps'\nimport {\n  AnimationResult,\n  AsyncResult,\n  ControllerFlushFn,\n  ControllerUpdate,\n  OnChange,\n  OnRest,\n  OnStart,\n  SpringChain,\n  SpringToFn,\n  SpringValues,\n} from './types'\n\n/** Events batched by the `Controller` class */\nconst BATCHED_EVENTS = ['onStart', 'onChange', 'onRest'] as const\n\nlet nextId = 1\n\n/** Queue of pending updates for a `Controller` instance. */\nexport interface ControllerQueue<State extends Lookup = Lookup>\n  extends Array<\n    ControllerUpdate<State, any> & {\n      /** The keys affected by this update. When null, all keys are affected. */\n      keys: string[] | null\n    }\n  > {}\n\nexport class Controller<State extends Lookup = Lookup> {\n  readonly id = nextId++\n\n  /** The animated values */\n  springs: SpringValues<State> = {} as any\n\n  /** The queue of props passed to the `update` method. */\n  queue: ControllerQueue<State> = []\n\n  /**\n   * The injected ref. When defined, render-based updates are pushed\n   * onto the `queue` instead of being auto-started.\n   */\n  ref?: SpringRef<State>\n\n  /** Custom handler for flushing update queues */\n  protected _flush?: ControllerFlushFn<this>\n\n  /** These props are used by all future spring values */\n  protected _initialProps?: Lookup\n\n  /** The counter for tracking `scheduleProps` calls */\n  protected _lastAsyncId = 0\n\n  /** The values currently being animated */\n  protected _active = new Set<FrameValue>()\n\n  /** The values that changed recently */\n  protected _changed = new Set<FrameValue>()\n\n  /** Equals false when `onStart` listeners can be called */\n  protected _started = false\n\n  private _item?: any\n\n  /** State used by the `runAsync` function */\n  protected _state: RunAsyncState<this> = {\n    paused: false,\n    pauseQueue: new Set(),\n    resumeQueue: new Set(),\n    timeouts: new Set(),\n  }\n\n  /** The event queues that are flushed once per frame maximum */\n  protected _events = {\n    onStart: new Map<\n      OnStart<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n    onChange: new Map<\n      OnChange<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n    onRest: new Map<\n      OnRest<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n  }\n\n  constructor(\n    props?: ControllerUpdate<State> | null,\n    flush?: ControllerFlushFn<any>\n  ) {\n    this._onFrame = this._onFrame.bind(this)\n    if (flush) {\n      this._flush = flush\n    }\n    if (props) {\n      this.start({ default: true, ...props })\n    }\n  }\n\n  /**\n   * Equals `true` when no spring values are in the frameloop, and\n   * no async animation is currently active.\n   */\n  get idle() {\n    return (\n      !this._state.asyncTo &&\n      Object.values(this.springs as Lookup<SpringValue>).every(spring => {\n        return spring.idle && !spring.isDelayed && !spring.isPaused\n      })\n    )\n  }\n\n  get item() {\n    return this._item\n  }\n\n  set item(item) {\n    this._item = item\n  }\n\n  /** Get the current values of our springs */\n  get(): State & UnknownProps {\n    const values: any = {}\n    this.each((spring, key) => (values[key] = spring.get()))\n    return values\n  }\n\n  /** Set the current values without animating. */\n  set(values: Partial<State>) {\n    for (const key in values) {\n      const value = values[key]\n      if (!is.und(value)) {\n        this.springs[key].set(value)\n      }\n    }\n  }\n\n  /** Push an update onto the queue of each value. */\n  update(props: ControllerUpdate<State> | Falsy) {\n    if (props) {\n      this.queue.push(createUpdate(props))\n    }\n    return this\n  }\n\n  /**\n   * Start the queued animations for every spring, and resolve the returned\n   * promise once all queued animations have finished or been cancelled.\n   *\n   * When you pass a queue (instead of nothing), that queue is used instead of\n   * the queued animations added with the `update` method, which are left alone.\n   */\n  start(props?: OneOrMore<ControllerUpdate<State>> | null): AsyncResult<this> {\n    let { queue } = this as any\n    if (props) {\n      queue = toArray<any>(props).map(createUpdate)\n    } else {\n      this.queue = []\n    }\n\n    if (this._flush) {\n      return this._flush(this, queue)\n    }\n\n    prepareKeys(this, queue)\n    return flushUpdateQueue(this, queue)\n  }\n\n  /** Stop all animations. */\n  stop(): this\n  /** Stop animations for the given keys. */\n  stop(keys: OneOrMore<string>): this\n  /** Cancel all animations. */\n  stop(cancel: boolean): this\n  /** Cancel animations for the given keys. */\n  stop(cancel: boolean, keys: OneOrMore<string>): this\n  /** Stop some or all animations. */\n  stop(keys?: OneOrMore<string>): this\n  /** Cancel some or all animations. */\n  stop(cancel: boolean, keys?: OneOrMore<string>): this\n  /** @internal */\n  stop(arg?: boolean | OneOrMore<string>, keys?: OneOrMore<string>) {\n    if (arg !== !!arg) {\n      keys = arg as OneOrMore<string>\n    }\n    if (keys) {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].stop(!!arg))\n    } else {\n      stopAsync(this._state, this._lastAsyncId)\n      this.each(spring => spring.stop(!!arg))\n    }\n    return this\n  }\n\n  /** Freeze the active animation in time */\n  pause(keys?: OneOrMore<string>) {\n    if (is.und(keys)) {\n      this.start({ pause: true })\n    } else {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].pause())\n    }\n    return this\n  }\n\n  /** Resume the animation if paused. */\n  resume(keys?: OneOrMore<string>) {\n    if (is.und(keys)) {\n      this.start({ pause: false })\n    } else {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].resume())\n    }\n    return this\n  }\n\n  /** Call a function once per spring value */\n  each(iterator: (spring: SpringValue, key: string) => void) {\n    eachProp(this.springs, iterator as any)\n  }\n\n  /** @internal Called at the end of every animation frame */\n  protected _onFrame() {\n    const { onStart, onChange, onRest } = this._events\n\n    const active = this._active.size > 0\n    const changed = this._changed.size > 0\n\n    if ((active && !this._started) || (changed && !this._started)) {\n      this._started = true\n      flush(onStart, ([onStart, result]) => {\n        result.value = this.get()\n        onStart(result, this, this._item)\n      })\n    }\n\n    const idle = !active && this._started\n    const values = changed || (idle && onRest.size) ? this.get() : null\n\n    if (changed && onChange.size) {\n      flush(onChange, ([onChange, result]) => {\n        result.value = values\n        onChange(result, this, this._item)\n      })\n    }\n\n    // The \"onRest\" queue is only flushed when all springs are idle.\n    if (idle) {\n      this._started = false\n      flush(onRest, ([onRest, result]) => {\n        result.value = values\n        onRest(result, this, this._item)\n      })\n    }\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    if (event.type == 'change') {\n      this._changed.add(event.parent)\n      if (!event.idle) {\n        this._active.add(event.parent)\n      }\n    } else if (event.type == 'idle') {\n      this._active.delete(event.parent)\n    }\n    // The `onFrame` handler runs when a parent is changed or idle.\n    else return\n    raf.onFrame(this._onFrame)\n  }\n}\n\n/**\n * Warning: Props might be mutated.\n */\nexport function flushUpdateQueue(\n  ctrl: Controller<any>,\n  queue: ControllerQueue\n) {\n  return Promise.all(queue.map(props => flushUpdate(ctrl, props))).then(\n    results => getCombinedResult(ctrl, results)\n  )\n}\n\n/**\n * Warning: Props might be mutated.\n *\n * Process a single set of props using the given controller.\n *\n * The returned promise resolves to `true` once the update is\n * applied and any animations it starts are finished without being\n * stopped or cancelled.\n */\nexport async function flushUpdate(\n  ctrl: Controller<any>,\n  props: ControllerQueue[number],\n  isLoop?: boolean\n): AsyncResult {\n  const { keys, to, from, loop, onRest, onResolve } = props\n  const defaults = is.obj(props.default) && props.default\n\n  // Looping must be handled in this function, or else the values\n  // would end up looping out-of-sync in many common cases.\n  if (loop) {\n    props.loop = false\n  }\n\n  // Treat false like null, which gets ignored.\n  if (to === false) props.to = null\n  if (from === false) props.from = null\n\n  const asyncTo = is.arr(to) || is.fun(to) ? to : undefined\n  if (asyncTo) {\n    props.to = undefined\n    props.onRest = undefined\n    if (defaults) {\n      defaults.onRest = undefined\n    }\n  }\n  // For certain events, use batching to prevent multiple calls per frame.\n  // However, batching is avoided when the `to` prop is async, because any\n  // event props are used as default props instead.\n  else {\n    each(BATCHED_EVENTS, key => {\n      const handler: any = props[key]\n      if (is.fun(handler)) {\n        const queue = ctrl['_events'][key]\n        props[key] = (({ finished, cancelled }: AnimationResult) => {\n          const result = queue.get(handler)\n          if (result) {\n            if (!finished) result.finished = false\n            if (cancelled) result.cancelled = true\n          } else {\n            // The \"value\" is set before the \"handler\" is called.\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false,\n            })\n          }\n        }) as any\n\n        // Avoid using a batched `handler` as a default prop.\n        if (defaults) {\n          defaults[key] = props[key] as any\n        }\n      }\n    })\n  }\n\n  const state = ctrl['_state']\n\n  // Pause/resume the `asyncTo` when `props.pause` is true/false.\n  if (props.pause === !state.paused) {\n    state.paused = props.pause\n    flushCalls(props.pause ? state.pauseQueue : state.resumeQueue)\n  }\n  // When a controller is paused, its values are also paused.\n  else if (state.paused) {\n    props.pause = true\n  }\n\n  const promises: AsyncResult[] = (keys || Object.keys(ctrl.springs)).map(key =>\n    ctrl.springs[key]!.start(props as any)\n  )\n\n  const cancel =\n    props.cancel === true || getDefaultProp(props, 'cancel') === true\n\n  if (asyncTo || (cancel && state.asyncId)) {\n    promises.push(\n      scheduleProps(++ctrl['_lastAsyncId'], {\n        props,\n        state,\n        actions: {\n          pause: noop,\n          resume: noop,\n          start(props, resolve) {\n            if (cancel) {\n              stopAsync(state, ctrl['_lastAsyncId'])\n              resolve(getCancelledResult(ctrl))\n            } else {\n              props.onRest = onRest\n              resolve(\n                runAsync(\n                  asyncTo as SpringChain | SpringToFn,\n                  props,\n                  state,\n                  ctrl\n                )\n              )\n            }\n          },\n        },\n      })\n    )\n  }\n\n  // Pause after updating each spring, so they can be resumed separately\n  // and so their default `pause` and `cancel` props are updated.\n  if (state.paused) {\n    // Ensure `this` must be resumed before the returned promise\n    // is resolved and before starting the next `loop` repetition.\n    await new Promise<void>(resume => {\n      state.resumeQueue.add(resume)\n    })\n  }\n\n  const result = getCombinedResult<any>(ctrl, await Promise.all(promises))\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to)\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps])\n      return flushUpdate(ctrl, nextProps, true)\n    }\n  }\n  if (onResolve) {\n    raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item))\n  }\n  return result\n}\n\n/**\n * From an array of updates, get the map of `SpringValue` objects\n * by their keys. Springs are created when any update wants to\n * animate a new key.\n *\n * Springs created by `getSprings` are neither cached nor observed\n * until they're given to `setSprings`.\n */\nexport function getSprings<State extends Lookup>(\n  ctrl: Controller<Lookup<any>>,\n  props?: OneOrMore<ControllerUpdate<State>>\n) {\n  const springs = { ...ctrl.springs }\n  if (props) {\n    each(toArray(props), (props: any) => {\n      if (is.und(props.keys)) {\n        props = createUpdate(props)\n      }\n      if (!is.obj(props.to)) {\n        // Avoid passing array/function to each spring.\n        props = { ...props, to: undefined }\n      }\n      prepareSprings(springs as any, props, key => {\n        return createSpring(key)\n      })\n    })\n  }\n  setSprings(ctrl, springs)\n  return springs\n}\n\n/**\n * Tell a controller to manage the given `SpringValue` objects\n * whose key is not already in use.\n */\nexport function setSprings(\n  ctrl: Controller<Lookup<any>>,\n  springs: SpringValues<UnknownProps>\n) {\n  eachProp(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring\n      addFluidObserver(spring, ctrl)\n    }\n  })\n}\n\nfunction createSpring(key: string, observer?: FluidObserver<FrameValue.Event>) {\n  const spring = new SpringValue()\n  spring.key = key\n  if (observer) {\n    addFluidObserver(spring, observer)\n  }\n  return spring\n}\n\n/**\n * Ensure spring objects exist for each defined key.\n *\n * Using the `props`, the `Animated` node of each `SpringValue` may\n * be created or updated.\n */\nfunction prepareSprings(\n  springs: SpringValues,\n  props: ControllerQueue[number],\n  create: (key: string) => SpringValue\n) {\n  if (props.keys) {\n    each(props.keys, key => {\n      const spring = springs[key] || (springs[key] = create(key))\n      spring['_prepareNode'](props)\n    })\n  }\n}\n\n/**\n * Ensure spring objects exist for each defined key, and attach the\n * `ctrl` to them for observation.\n *\n * The queue is expected to contain `createUpdate` results.\n */\nfunction prepareKeys(ctrl: Controller<any>, queue: ControllerQueue[number][]) {\n  each(queue, props => {\n    prepareSprings(ctrl.springs, props, key => {\n      return createSpring(key, ctrl)\n    })\n  })\n}\n", "import * as React from 'react'\nimport { useContext, PropsWithChildren } from 'react'\nimport { useMemoOne } from '@react-spring/shared'\n\n/**\n * This context affects all new and existing `SpringValue` objects\n * created with the hook API or the renderprops API.\n */\nexport interface SpringContext {\n  /** Pause all new and existing animations. */\n  pause?: boolean\n  /** Force all new and existing animations to be immediate. */\n  immediate?: boolean\n}\n\nexport const SpringContext = ({\n  children,\n  ...props\n}: PropsWithChildren<SpringContext>) => {\n  const inherited = useContext(ctx)\n\n  // Inherited values are dominant when truthy.\n  const pause = props.pause || !!inherited.pause,\n    immediate = props.immediate || !!inherited.immediate\n\n  // Memoize the context to avoid unwanted renders.\n  props = useMemoOne(() => ({ pause, immediate }), [pause, immediate])\n\n  const { Provider } = ctx\n  return <Provider value={props}>{children}</Provider>\n}\n\nconst ctx = makeContext(SpringContext, {} as SpringContext)\n\n// Allow `useContext(SpringContext)` in TypeScript.\nSpringContext.Provider = ctx.Provider\nSpringContext.Consumer = ctx.Consumer\n\n/** Make the `target` compatible with `useContext` */\nfunction makeContext<T>(target: any, init: T): React.Context<T> {\n  Object.assign(target, React.createContext(init))\n  target.Provider._context = target\n  target.Consumer._context = target\n  return target\n}\n", "import { each, is, deprecateDirect<PERSON>all } from '@react-spring/shared'\nimport { Lookup, Falsy, OneOrMore } from '@react-spring/types'\nimport { AsyncResult, ControllerUpdate } from './types'\nimport { Controller } from './Controller'\n\nexport interface ControllerUpdateFn<State extends Lookup = Lookup> {\n  (i: number, ctrl: Controller<State>): ControllerUpdate<State> | Falsy\n}\n\nexport interface SpringRef<State extends Lookup = Lookup> {\n  (\n    props?: ControllerUpdate<State> | ControllerUpdateFn<State>\n  ): AsyncResult<Controller<State>>[]\n  current: Controller<State>[]\n\n  /** Add a controller to this ref */\n  add(ctrl: Controller<State>): void\n\n  /** Remove a controller from this ref */\n  delete(ctrl: Controller<State>): void\n\n  /** Pause all animations. */\n  pause(): this\n  /** Pause animations for the given keys. */\n  pause(keys: OneOrMore<string>): this\n  /** Pause some or all animations. */\n  pause(keys?: OneOrMore<string>): this\n\n  /** Resume all animations. */\n  resume(): this\n  /** Resume animations for the given keys. */\n  resume(keys: OneOrMore<string>): this\n  /** Resume some or all animations. */\n  resume(keys?: OneOrMore<string>): this\n\n  /** Update the state of each controller without animating. */\n  set(values: Partial<State>): void\n  /** Update the state of each controller without animating based on their passed state. */\n  set(values: (index: number, ctrl: Controller<State>) => Partial<State>): void\n\n  /** Start the queued animations of each controller. */\n  start(): AsyncResult<Controller<State>>[]\n  /** Update every controller with the same props. */\n  start(props: ControllerUpdate<State>): AsyncResult<Controller<State>>[]\n  /** Update controllers based on their state. */\n  start(props: ControllerUpdateFn<State>): AsyncResult<Controller<State>>[]\n  /** Start animating each controller. */\n  start(\n    props?: ControllerUpdate<State> | ControllerUpdateFn<State>\n  ): AsyncResult<Controller<State>>[]\n\n  /** Stop all animations. */\n  stop(): this\n  /** Stop animations for the given keys. */\n  stop(keys: OneOrMore<string>): this\n  /** Cancel all animations. */\n  stop(cancel: boolean): this\n  /** Cancel animations for the given keys. */\n  stop(cancel: boolean, keys: OneOrMore<string>): this\n  /** Stop some or all animations. */\n  stop(keys?: OneOrMore<string>): this\n  /** Cancel some or all animations. */\n  stop(cancel: boolean, keys?: OneOrMore<string>): this\n\n  /** Add the same props to each controller's update queue. */\n  update(props: ControllerUpdate<State>): this\n  /** Generate separate props for each controller's update queue. */\n  update(props: ControllerUpdateFn<State>): this\n  /** Add props to each controller's update queue. */\n  update(props: ControllerUpdate<State> | ControllerUpdateFn<State>): this\n\n  _getProps(\n    arg: ControllerUpdate<State> | ControllerUpdateFn<State>,\n    ctrl: Controller<State>,\n    index: number\n  ): ControllerUpdate<State> | Falsy\n}\n\nexport const SpringRef = <\n  State extends Lookup = Lookup,\n>(): SpringRef<State> => {\n  const current: Controller<State>[] = []\n\n  const SpringRef: SpringRef<State> = function (props) {\n    deprecateDirectCall()\n\n    const results: AsyncResult[] = []\n\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start())\n      } else {\n        const update = _getProps(props, ctrl, i)\n        if (update) {\n          results.push(ctrl.start(update))\n        }\n      }\n    })\n\n    return results\n  }\n\n  SpringRef.current = current\n\n  /** Add a controller to this ref */\n  SpringRef.add = function (ctrl: Controller<State>) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl)\n    }\n  }\n\n  /** Remove a controller from this ref */\n  SpringRef.delete = function (ctrl: Controller<State>) {\n    const i = current.indexOf(ctrl)\n    if (~i) current.splice(i, 1)\n  }\n\n  /** Pause all animations. */\n  SpringRef.pause = function () {\n    each(current, ctrl => ctrl.pause(...arguments))\n    return this\n  }\n\n  /** Resume all animations. */\n  SpringRef.resume = function () {\n    each(current, ctrl => ctrl.resume(...arguments))\n    return this\n  }\n\n  /** Update the state of each controller without animating. */\n  SpringRef.set = function (\n    values:\n      | Partial<State>\n      | ((i: number, ctrl: Controller<State>) => Partial<State>)\n  ) {\n    each(current, (ctrl, i) => {\n      const update = is.fun(values) ? values(i, ctrl) : values\n      if (update) {\n        ctrl.set(update)\n      }\n    })\n  }\n\n  SpringRef.start = function (props?: object | ControllerUpdateFn<State>) {\n    const results: AsyncResult[] = []\n\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start())\n      } else {\n        const update = this._getProps(props, ctrl, i)\n        if (update) {\n          results.push(ctrl.start(update))\n        }\n      }\n    })\n\n    return results\n  }\n\n  /** Stop all animations. */\n  SpringRef.stop = function () {\n    each(current, ctrl => ctrl.stop(...arguments))\n    return this\n  }\n\n  SpringRef.update = function (props: object | ControllerUpdateFn<State>) {\n    each(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)))\n    return this\n  }\n\n  /** Overridden by `useTrail` to manipulate props */\n  const _getProps = function (\n    arg: ControllerUpdate<State> | ControllerUpdateFn<State>,\n    ctrl: Controller<State>,\n    index: number\n  ) {\n    return is.fun(arg) ? arg(index, ctrl) : arg\n  }\n\n  SpringRef._getProps = _getProps\n\n  return SpringRef\n}\n", "import { useState } from 'react'\nimport { Lookup } from '@react-spring/types'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\n\nconst initSpringRef = () => SpringRef<any>()\n\nexport const useSpringRef = <State extends Lookup = Lookup>() =>\n  useState(initSpringRef)[0] as SpringRefType<State>\n", "import { useConstant, useOnce } from '@react-spring/shared'\n\nimport { SpringValue } from '../SpringValue'\nimport { SpringUpdate } from '../types'\n\n/**\n * Creates a constant single `SpringValue` that can be interacted\n * with imperatively. This is an advanced API and does not react\n * to updates from the parent component e.g. passing a new initial value\n *\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const opacity = useSpringValue(1)\n *\n *   return <animated.div style={{ opacity }} />\n * }\n * ```\n *\n * @param initial – The initial value of the `SpringValue`.\n * @param props – Typically the same props as `useSpring` e.g. `config`, `loop` etc.\n *\n * @public\n */\nexport const useSpringValue = <T>(\n  initial: Exclude<T, object>,\n  props?: SpringUpdate<T>\n) => {\n  const springValue = useConstant(() => new SpringValue(initial, props))\n\n  useOnce(() => () => {\n    springValue.stop()\n  })\n\n  return springValue\n}\n", "import { each, is, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { Lookup } from '@react-spring/types'\n\nimport { Valid } from '../types/common'\nimport { PickAnimated, SpringValues } from '../types'\n\nimport { SpringRef } from '../SpringRef'\nimport { Controller } from '../Controller'\n\nimport { UseSpringProps } from './useSpring'\nimport { useSprings } from './useSprings'\nimport { replaceRef } from '../helpers'\n\nexport type UseTrailProps<Props extends object = any> = UseSpringProps<Props>\n\nexport function useTrail<Props extends object>(\n  length: number,\n  props: (\n    i: number,\n    ctrl: Controller\n  ) => UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>),\n  deps?: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRef<State>]\n    : never\n  : never\n\n/**\n * This hook is an abstraction around `useSprings` and is designed to\n * automatically orchestrate the springs to stagger one after the other\n *\n * ```jsx\n * export const MyComponent = () => {\n *  const trails = useTrail(3, {opacity: 0})\n *\n *  return trails.map(styles => <animated.div style={styles} />)\n * }\n * ```\n *\n * @param length – The number of springs you want to create\n * @param propsArg – The props to pass to the internal `useSprings` hook,\n * therefore is the same as `useSprings`.\n *\n * @public\n */\nexport function useTrail<Props extends object>(\n  length: number,\n  props: UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>)\n): SpringValues<PickAnimated<Props>>[]\n\n/**\n * This hook is an abstraction around `useSprings` and is designed to\n * automatically orchestrate the springs to stagger one after the other\n *\n * ```jsx\n * export const MyComponent = () => {\n *  const trails = useTrail(3, {opacity: 0}, [])\n *\n *  return trails.map(styles => <animated.div style={styles} />)\n * }\n * ```\n *\n * @param length – The number of springs you want to create\n * @param propsArg – The props to pass to the internal `useSprings` hook,\n * therefore is the same as `useSprings`.\n * @param deps – The optional array of dependencies to pass to the internal\n * `useSprings` hook, therefore is the same as `useSprings`.\n *\n * @public\n */\nexport function useTrail<Props extends object>(\n  length: number,\n  props: UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>),\n  deps: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRef<State>]\n    : never\n  : never\n\nexport function useTrail(\n  length: number,\n  propsArg: unknown,\n  deps?: readonly any[]\n) {\n  const propsFn = is.fun(propsArg) && propsArg\n  if (propsFn && !deps) deps = []\n\n  // The trail is reversed when every render-based update is reversed.\n  let reverse = true\n  let passedRef: SpringRef | undefined = undefined\n\n  const result = useSprings(\n    length,\n    (i, ctrl) => {\n      const props = propsFn ? propsFn(i, ctrl) : propsArg\n      passedRef = props.ref\n      reverse = reverse && props.reverse\n\n      return props\n    },\n    // Ensure the props function is called when no deps exist.\n    // This works around the 3 argument rule.\n    deps || [{}]\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    /**\n     * Run through the ref passed by the `useSprings` hook.\n     */\n    each(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)]\n\n      /**\n       * If there's a passed ref then we replace the ctrl ref with it\n       */\n      replaceRef(ctrl, passedRef)\n\n      /**\n       * And if there's a ctrl ref then we update instead of start\n       * which means nothing is fired until the start method\n       * of said passedRef is called.\n       */\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({ to: parent.springs })\n        }\n\n        return\n      }\n\n      if (parent) {\n        ctrl.start({ to: parent.springs })\n      } else {\n        ctrl.start()\n      }\n    })\n  }, deps)\n\n  if (propsFn || arguments.length == 3) {\n    const ref = passedRef ?? result[1]\n\n    ref['_getProps'] = (propsArg, ctrl, i) => {\n      const props = is.fun(propsArg) ? propsArg(i, ctrl) : propsArg\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)]\n        if (parent) props.to = parent.springs\n        return props\n      }\n    }\n    return result\n  }\n\n  return result[0]\n}\n", "import * as React from 'react'\nimport { useContext, useRef, useMemo } from 'react'\nimport { Lookup, OneOrMore, UnknownProps } from '@react-spring/types'\nimport {\n  is,\n  toArray,\n  useForceUpdate,\n  useOnce,\n  usePrev,\n  each,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\n\nimport {\n  Change,\n  ControllerUpdate,\n  ItemKeys,\n  PickAnimated,\n  TransitionFn,\n  TransitionState,\n  TransitionTo,\n  UseTransitionProps,\n} from '../types'\nimport { Valid } from '../types/common'\nimport {\n  callProp,\n  detachRefs,\n  getDefaultProps,\n  hasProps,\n  inferTo,\n  replaceRef,\n} from '../helpers'\nimport { Controller, getSprings } from '../Controller'\nimport { SpringContext } from '../SpringContext'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\nimport { TransitionPhase } from '../TransitionPhase'\n\ndeclare function setTimeout(handler: Function, timeout?: number): number\ndeclare function clearTimeout(timeoutId: number): void\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props: () =>\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>),\n  deps?: any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [TransitionFn<Item, PickAnimated<Props>>, SpringRefType<State>]\n    : never\n  : never\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props:\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>)\n): TransitionFn<Item, PickAnimated<Props>>\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props:\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>),\n  deps: any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [TransitionFn<Item, State>, SpringRefType<State>]\n    : never\n  : never\n\nexport function useTransition(\n  data: unknown,\n  props: UseTransitionProps | (() => any),\n  deps?: any[]\n): any {\n  const propsFn = is.fun(props) && props\n\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig,\n  }: UseTransitionProps<any> = propsFn ? propsFn() : props\n\n  // Return a `SpringRef` if a deps array was passed.\n  const ref = useMemo(\n    () => (propsFn || arguments.length == 3 ? SpringRef() : void 0),\n    []\n  )\n\n  // Every item has its own transition.\n  const items = toArray(data)\n  const transitions: TransitionState[] = []\n\n  // The \"onRest\" callbacks need a ref to the latest transitions.\n  const usedTransitions = useRef<TransitionState[] | null>(null)\n  const prevTransitions = reset ? null : usedTransitions.current\n\n  useIsomorphicLayoutEffect(() => {\n    usedTransitions.current = transitions\n  })\n\n  useOnce(() => {\n    /**\n     * If transitions exist on mount of the component\n     * then reattach their refs on-mount, this was required\n     * for react18 strict mode to work properly.\n     *\n     * See https://github.com/pmndrs/react-spring/issues/1890\n     */\n\n    each(transitions, t => {\n      ref?.add(t.ctrl)\n      t.ctrl.ref = ref\n    })\n\n    // Destroy all transitions on dismount.\n    return () => {\n      each(usedTransitions.current!, t => {\n        if (t.expired) {\n          clearTimeout(t.expirationId!)\n        }\n        detachRefs(t.ctrl, ref)\n        t.ctrl.stop(true)\n      })\n    }\n  })\n\n  // Keys help with reusing transitions between renders.\n  // The `key` prop can be undefined (which means the items themselves are used\n  // as keys), or a function (which maps each item to its key), or an array of\n  // keys (which are assigned to each item by index).\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions)\n\n  // Expired transitions that need clean up.\n  const expired = (reset && usedTransitions.current) || []\n  useIsomorphicLayoutEffect(() =>\n    each(expired, ({ ctrl, item, key }) => {\n      detachRefs(ctrl, ref)\n      callProp(onDestroyed, item, key)\n    })\n  )\n\n  // Map old indices to new indices.\n  const reused: number[] = []\n  if (prevTransitions)\n    each(prevTransitions, (t, i) => {\n      // Expired transitions are not rendered.\n      if (t.expired) {\n        clearTimeout(t.expirationId!)\n        expired.push(t)\n      } else {\n        i = reused[i] = keys.indexOf(t.key)\n        if (~i) transitions[i] = t\n      }\n    })\n\n  // Mount new items with fresh transitions.\n  each(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: TransitionPhase.MOUNT,\n        ctrl: new Controller(),\n      }\n\n      transitions[i].ctrl.item = item\n    }\n  })\n\n  // Update the item of any transition whose key still exists,\n  // and ensure leaving transitions are rendered until they finish.\n  if (reused.length) {\n    let i = -1\n    const { leave }: UseTransitionProps<any> = propsFn ? propsFn() : props\n    each(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions![prevIndex]\n      if (~keyIndex) {\n        i = transitions.indexOf(t)\n        transitions[i] = { ...t, item: items[keyIndex] }\n      } else if (leave) {\n        transitions.splice(++i, 0, t)\n      }\n    })\n  }\n\n  if (is.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item))\n  }\n\n  // Track cumulative delay for the \"trail\" prop.\n  let delay = -trail\n\n  // Expired transitions use this to dismount.\n  const forceUpdate = useForceUpdate()\n\n  // These props are inherited by every phase change.\n  const defaultProps = getDefaultProps<UseTransitionProps>(props)\n  // Generate changes to apply in useEffect.\n  const changes = new Map<TransitionState, Change>()\n  const exitingTransitions = useRef(new Map<TransitionState, Change>())\n\n  const forceChange = useRef(false)\n  each(transitions, (t, i) => {\n    const key = t.key\n    const prevPhase = t.phase\n\n    const p: UseTransitionProps<any> = propsFn ? propsFn() : props\n\n    let to: TransitionTo<any>\n    let phase: TransitionPhase\n\n    const propsDelay = callProp(p.delay || 0, key)\n\n    if (prevPhase == TransitionPhase.MOUNT) {\n      to = p.enter\n      phase = TransitionPhase.ENTER\n    } else {\n      const isLeave = keys.indexOf(key) < 0\n      if (prevPhase != TransitionPhase.LEAVE) {\n        if (isLeave) {\n          to = p.leave\n          phase = TransitionPhase.LEAVE\n        } else if ((to = p.update)) {\n          phase = TransitionPhase.UPDATE\n        } else return\n      } else if (!isLeave) {\n        to = p.enter\n        phase = TransitionPhase.ENTER\n      } else return\n    }\n\n    // When \"to\" is a function, it can return (1) an array of \"useSpring\" props,\n    // (2) an async function, or (3) an object with any \"useSpring\" props.\n    to = callProp(to, t.item, i)\n    to = is.obj(to) ? inferTo(to) : { to }\n\n    /**\n     * This would allow us to give different delays for phases.\n     * If we were to do this, we'd have to suffle the prop\n     * spreading below to set delay last.\n     * But if we were going to do that, we should consider letting\n     * the prop trail also be part of a phase.\n     */\n    // if (to.delay) {\n    //   phaseDelay = callProp(to.delay, key)\n    // }\n\n    if (!to.config) {\n      const config = propsConfig || defaultProps.config\n      to.config = callProp(config, t.item, i, phase)\n    }\n\n    delay += trail\n\n    // The payload is used to update the spring props once the current render is committed.\n    const payload: ControllerUpdate<UnknownProps> = {\n      ...defaultProps,\n      // we need to add our props.delay value you here.\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      // This prevents implied resets.\n      reset: false,\n      // Merge any phase-specific props.\n      ...(to as any),\n    }\n\n    if (phase == TransitionPhase.ENTER && is.und(payload.from)) {\n      const p = propsFn ? propsFn() : props\n      // The `initial` prop is used on the first render of our parent component,\n      // as well as when `reset: true` is passed. It overrides the `from` prop\n      // when defined, and it makes `enter` instant when null.\n      const from = is.und(p.initial) || prevTransitions ? p.from : p.initial\n\n      payload.from = callProp(from, t.item, i)\n    }\n\n    const { onResolve } = payload\n    payload.onResolve = result => {\n      callProp(onResolve, result)\n\n      const transitions = usedTransitions.current!\n      const t = transitions.find(t => t.key === key)\n      if (!t) return\n\n      // Reset the phase of a cancelled enter/leave transition, so it can\n      // retry the animation on the next render.\n      if (result.cancelled && t.phase != TransitionPhase.UPDATE) {\n        /**\n         * @legacy Reset the phase of a cancelled enter/leave transition, so it can\n         * retry the animation on the next render.\n         *\n         * Note: leaving this here made the transitioned item respawn.\n         */\n        // t.phase = prevPhase\n        return\n      }\n\n      if (t.ctrl.idle) {\n        const idle = transitions.every(t => t.ctrl.idle)\n        if (t.phase == TransitionPhase.LEAVE) {\n          const expiry = callProp(expires, t.item)\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry\n            t.expired = true\n\n            // Force update once the expiration delay ends.\n            if (!idle && expiryMs > 0) {\n              // The maximum timeout is 2^31-1\n              if (expiryMs <= 0x7fffffff)\n                t.expirationId = setTimeout(forceUpdate, expiryMs)\n              return\n            }\n          }\n        }\n        // Force update once idle and expired items exist.\n        if (idle && transitions.some(t => t.expired)) {\n          /**\n           * Remove the exited transition from the list\n           * this may not exist but we'll try anyway.\n           */\n          exitingTransitions.current.delete(t)\n\n          if (exitBeforeEnter) {\n            /**\n             * If we have exitBeforeEnter == true\n             * we need to force the animation to start\n             */\n            forceChange.current = true\n          }\n\n          forceUpdate()\n        }\n      }\n    }\n\n    const springs = getSprings(t.ctrl, payload)\n\n    /**\n     * Make a separate map for the exiting changes and \"regular\" changes\n     */\n    if (phase === TransitionPhase.LEAVE && exitBeforeEnter) {\n      exitingTransitions.current.set(t, { phase, springs, payload })\n    } else {\n      changes.set(t, { phase, springs, payload })\n    }\n  })\n\n  // The prop overrides from an ancestor.\n  const context = useContext(SpringContext)\n  const prevContext = usePrev(context)\n  const hasContext = context !== prevContext && hasProps(context)\n\n  // Merge the context into each transition.\n  useIsomorphicLayoutEffect(() => {\n    if (hasContext) {\n      each(transitions, t => {\n        t.ctrl.start({ default: context })\n      })\n    }\n  }, [context])\n\n  each(changes, (_, t) => {\n    /**\n     * If we have children to exit because exitBeforeEnter is\n     * set to true, we remove the transitions so they go to back\n     * to their initial state.\n     */\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex(state => state.key === t.key)\n      transitions.splice(ind, 1)\n    }\n  })\n\n  useIsomorphicLayoutEffect(\n    () => {\n      /*\n       * if exitingTransitions.current has a size it means we're exiting before enter\n       * so we want to map through those and fire those first.\n       */\n      each(\n        exitingTransitions.current.size ? exitingTransitions.current : changes,\n        ({ phase, payload }, t) => {\n          const { ctrl } = t\n\n          t.phase = phase\n\n          // Attach the controller to our local ref.\n          ref?.add(ctrl)\n\n          // Merge the context into new items.\n          if (hasContext && phase == TransitionPhase.ENTER) {\n            ctrl.start({ default: context })\n          }\n\n          if (payload) {\n            // Update the injected ref if needed.\n            replaceRef(ctrl, payload.ref)\n\n            /**\n             * When an injected ref exists, the update is postponed\n             * until the ref has its `start` method called.\n             * Unless we have exitBeforeEnter in which case will skip\n             * to enter the new animation straight away as if they \"overlapped\"\n             */\n            if ((ctrl.ref || ref) && !forceChange.current) {\n              ctrl.update(payload)\n            } else {\n              ctrl.start(payload)\n\n              if (forceChange.current) {\n                forceChange.current = false\n              }\n            }\n          }\n        }\n      )\n    },\n    reset ? void 0 : deps\n  )\n\n  const renderTransitions: TransitionFn = render => (\n    <>\n      {transitions.map((t, i) => {\n        const { springs } = changes.get(t) || t.ctrl\n        const elem: any = render({ ...springs }, t.item, t, i)\n        return elem && elem.type ? (\n          <elem.type\n            {...elem.props}\n            key={is.str(t.key) || is.num(t.key) ? t.key : t.ctrl.id}\n            ref={elem.ref}\n          />\n        ) : (\n          elem\n        )\n      })}\n    </>\n  )\n\n  return ref ? [renderTransitions, ref] : renderTransitions\n}\n\n/** Local state for auto-generated item keys */\nlet nextKey = 1\n\nfunction getKeys(\n  items: readonly any[],\n  { key, keys = key }: { key?: ItemKeys; keys?: ItemKeys },\n  prevTransitions: TransitionState[] | null\n): readonly any[] {\n  if (keys === null) {\n    const reused = new Set()\n    return items.map(item => {\n      const t =\n        prevTransitions &&\n        prevTransitions.find(\n          t =>\n            t.item === item &&\n            t.phase !== TransitionPhase.LEAVE &&\n            !reused.has(t)\n        )\n      if (t) {\n        reused.add(t)\n        return t.key\n      }\n      return nextKey++\n    })\n  }\n  return is.und(keys) ? items : is.fun(keys) ? items.map(keys) : toArray(keys)\n}\n", "import { MutableRefObject } from 'react'\nimport { each, onScroll, useIsomorphicLayoutEffect } from '@react-spring/shared'\n\nimport { SpringProps, SpringValues } from '../types'\n\nimport { useSpring } from './useSpring'\n\nexport interface UseScrollOptions extends Omit<SpringProps, 'to' | 'from'> {\n  container?: MutableRefObject<HTMLElement>\n}\n\n/**\n * A small utility abstraction around our signature useSpring hook. It's a great way to create\n * a scroll-linked animation. With either the raw value of distance or a 0-1 progress value.\n * You can either use the scroll values of the whole document, or just a specific element.\n *\n * \n ```jsx\n    import { useScroll, animated } from '@react-spring/web'\n\n    function MyComponent() {\n      const { scrollYProgress } = useScroll()\n\n      return (\n        <animated.div style={{ opacity: scrollYProgress }}>\n          Hello World\n        </animated.div>\n      )\n    }\n  ```\n * \n * @param {UseScrollOptions} useScrollOptions options for the useScroll hook.\n * @param {MutableRefObject<HTMLElement>} useScrollOptions.container the container to listen to scroll events on, defaults to the window.\n *\n * @returns {SpringValues<{scrollX: number; scrollY: number; scrollXProgress: number; scrollYProgress: number}>} SpringValues the collection of values returned from the inner hook\n */\nexport const useScroll = ({\n  container,\n  ...springOptions\n}: UseScrollOptions = {}): SpringValues<{\n  scrollX: number\n  scrollY: number\n  scrollXProgress: number\n  scrollYProgress: number\n}> => {\n  const [scrollValues, api] = useSpring(\n    () => ({\n      scrollX: 0,\n      scrollY: 0,\n      scrollXProgress: 0,\n      scrollYProgress: 0,\n      ...springOptions,\n    }),\n    []\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onScroll(\n      ({ x, y }) => {\n        api.start({\n          scrollX: x.current,\n          scrollXProgress: x.progress,\n          scrollY: y.current,\n          scrollYProgress: y.progress,\n        })\n      },\n      { container: container?.current || undefined }\n    )\n\n    return () => {\n      /**\n       * Stop the springs on unmount.\n       */\n      each(Object.values(scrollValues), value => value.stop())\n\n      cleanupScroll()\n    }\n  }, [])\n\n  return scrollValues\n}\n", "import { MutableRefObject } from 'react'\nimport { onResize, each, useIsomorphicLayoutEffect } from '@react-spring/shared'\n\nimport { SpringProps, SpringValues } from '../types'\n\nimport { useSpring } from './useSpring'\n\nexport interface UseResizeOptions extends Omit<SpringProps, 'to' | 'from'> {\n  container?: MutableRefObject<HTMLElement | null | undefined>\n}\n\n/**\n * A small abstraction around the `useSpring` hook. It returns a `SpringValues` \n * object with the `width` and `height` of the element it's attached to & doesn't \n * necessarily have to be attached to the window, by passing a `container` you \n * can observe that element's size instead.\n * \n ```jsx\n    import { useResize, animated } from '@react-spring/web'\n\n    function MyComponent() {\n      const { width } = useResize()\n\n      return (\n        <animated.div style={{ width }}>\n          Hello World\n        </animated.div>\n      )\n    }\n  ```\n * \n * @param {UseResizeOptions} UseResizeOptions options for the useScroll hook.\n * @param {MutableRefObject<HTMLElement>} UseResizeOptions.container the container to listen to scroll events on, defaults to the window.\n *\n * @returns {SpringValues<{width: number; height: number;}>} SpringValues the collection of values returned from the inner hook\n */\nexport const useResize = ({\n  container,\n  ...springOptions\n}: UseResizeOptions): SpringValues<{\n  width: number\n  height: number\n}> => {\n  const [sizeValues, api] = useSpring(\n    () => ({\n      width: 0,\n      height: 0,\n      ...springOptions,\n    }),\n    []\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onResize(\n      ({ width, height }) => {\n        api.start({\n          width,\n          height,\n          immediate:\n            sizeValues.width.get() === 0 || sizeValues.height.get() === 0,\n        })\n      },\n      { container: container?.current || undefined }\n    )\n\n    return () => {\n      /**\n       * Stop the springs on unmount.\n       */\n      each(Object.values(sizeValues), value => value.stop())\n\n      cleanupScroll()\n    }\n  }, [])\n\n  return sizeValues\n}\n", "import { RefObject, useRef, useState } from 'react'\nimport { is, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { Lookup } from '@react-spring/types'\n\nimport { PickAnimated, SpringValues } from '../types'\nimport { useSpring, UseSpringProps } from './useSpring'\nimport { Valid } from '../types/common'\n\nexport interface IntersectionArgs\n  extends Omit<IntersectionObserverInit, 'root' | 'threshold'> {\n  root?: React.MutableRefObject<HTMLElement>\n  once?: boolean\n  amount?: 'any' | 'all' | number | number[]\n}\n\nconst defaultThresholdOptions = {\n  any: 0,\n  all: 1,\n}\n\nexport function useInView(args?: IntersectionArgs): [RefObject<any>, boolean]\nexport function useInView<Props extends object>(\n  /**\n   * TODO: make this narrower to only accept reserved props.\n   */\n  props: () => Props & Valid<Props, UseSpringProps<Props>>,\n  args?: IntersectionArgs\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [RefObject<any>, SpringValues<State>]\n    : never\n  : never\nexport function useInView<TElement extends HTMLElement>(\n  props?: (() => UseSpringProps<any>) | IntersectionArgs,\n  args?: IntersectionArgs\n) {\n  const [isInView, setIsInView] = useState(false)\n  const ref = useRef<TElement>()\n\n  const propsFn = is.fun(props) && props\n\n  const springsProps = propsFn ? propsFn() : {}\n  const { to = {}, from = {}, ...restSpringProps } = springsProps\n\n  const intersectionArguments = propsFn ? args : props\n\n  const [springs, api] = useSpring(() => ({ from, ...restSpringProps }), [])\n\n  useIsomorphicLayoutEffect(() => {\n    const element = ref.current\n    const {\n      root,\n      once,\n      amount = 'any',\n      ...restArgs\n    } = intersectionArguments ?? {}\n\n    if (\n      !element ||\n      (once && isInView) ||\n      typeof IntersectionObserver === 'undefined'\n    )\n      return\n\n    const activeIntersections = new WeakMap<Element, VoidFunction>()\n\n    const onEnter = () => {\n      if (to) {\n        // @ts-expect-error – TODO: fix this type error\n        api.start(to)\n      }\n\n      setIsInView(true)\n\n      const cleanup = () => {\n        if (from) {\n          api.start(from)\n        }\n        setIsInView(false)\n      }\n\n      return once ? undefined : cleanup\n    }\n\n    const handleIntersection: IntersectionObserverCallback = entries => {\n      entries.forEach(entry => {\n        const onLeave = activeIntersections.get(entry.target)\n\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return\n        }\n\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter()\n          if (is.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave)\n          } else {\n            observer.unobserve(entry.target)\n          }\n        } else if (onLeave) {\n          onLeave()\n          activeIntersections.delete(entry.target)\n        }\n      })\n    }\n\n    const observer = new IntersectionObserver(handleIntersection, {\n      root: (root && root.current) || undefined,\n      threshold:\n        typeof amount === 'number' || Array.isArray(amount)\n          ? amount\n          : defaultThresholdOptions[amount],\n      ...restArgs,\n    })\n\n    observer.observe(element)\n\n    return () => observer.unobserve(element)\n  }, [intersectionArguments])\n\n  if (propsFn) {\n    return [ref, springs]\n  }\n\n  return [ref, isInView]\n}\n", "import { NoInfer, UnknownProps } from '@react-spring/types'\nimport { useSpring, UseSpringProps } from '../hooks/useSpring'\nimport { SpringValues, SpringToFn, SpringChain } from '../types'\n\nexport type SpringComponentProps<State extends object = UnknownProps> =\n  unknown &\n    UseSpringProps<State> & {\n      children: (values: SpringValues<State>) => JSX.Element | null\n    }\n\n// Infer state from \"from\" object prop.\nexport function Spring<State extends object>(\n  props: {\n    from: State\n    to?: SpringChain<NoInfer<State>> | SpringToFn<NoInfer<State>>\n  } & Omit<SpringComponentProps<NoInfer<State>>, 'from' | 'to'>\n): JSX.Element | null\n\n// Infer state from \"to\" object prop.\nexport function Spring<State extends object>(\n  props: { to: State } & Omit<SpringComponentProps<NoInfer<State>>, 'to'>\n): JSX.Element | null\n\nexport function Spring({ children, ...props }: any) {\n  return children(useSpring(props))\n}\n", "import { ReactNode } from 'react'\nimport { NoInfer, Falsy } from '@react-spring/types'\nimport { is } from '@react-spring/shared'\n\nimport { Valid } from '../types/common'\nimport { PickAnimated, SpringValues } from '../types'\nimport { UseSpringProps } from '../hooks/useSpring'\nimport { useTrail } from '../hooks/useTrail'\n\nexport type TrailComponentProps<Item, Props extends object = any> = unknown &\n  UseSpringProps<Props> & {\n    items: readonly Item[]\n    children: (\n      item: NoInfer<Item>,\n      index: number\n    ) => ((values: SpringValues<PickAnimated<Props>>) => ReactNode) | Falsy\n  }\n\nexport function Trail<Item, Props extends TrailComponentProps<Item>>({\n  items,\n  children,\n  ...props\n}: Props & Valid<Props, TrailComponentProps<Item, Props>>) {\n  const trails: any[] = useTrail(items.length, props)\n  return items.map((item, index) => {\n    const result = children(item, index)\n    return is.fun(result) ? result(trails[index]) : result\n  })\n}\n", "import { Valid } from '../types/common'\nimport { TransitionComponentProps } from '../types'\nimport { useTransition } from '../hooks'\n\nexport function Transition<Item, Props extends TransitionComponentProps<Item>>(\n  props:\n    | TransitionComponentProps<Item>\n    | (Props & Valid<Props, TransitionComponentProps<Item, Props>>)\n): JSX.Element\n\nexport function Transition({\n  items,\n  children,\n  ...props\n}: TransitionComponentProps<any>) {\n  return useTransition(items, props)(children)\n}\n", "import { FluidValue, deprecateInterpolate } from '@react-spring/shared'\nimport {\n  Constrain,\n  OneOrMore,\n  Animatable,\n  ExtrapolateType,\n  InterpolatorConfig,\n  InterpolatorFn,\n} from '@react-spring/types'\nimport { Interpolation } from './Interpolation'\n\n/** Map the value of one or more dependencies */\nexport const to: Interpolator = (source: any, ...args: [any]) =>\n  new Interpolation(source, args)\n\n/** @deprecated Use the `to` export instead */\nexport const interpolate: Interpolator = (source: any, ...args: [any]) => (\n  deprecateInterpolate(), new Interpolation(source, args)\n)\n\n/** Extract the raw value types that are being interpolated */\nexport type Interpolated<T extends ReadonlyArray<any>> = {\n  [P in keyof T]: T[P] extends infer Element\n    ? Element extends FluidValue<infer U>\n      ? U\n      : Element\n    : never\n}\n\n/**\n * This interpolates one or more `FluidValue` objects.\n * The exported `interpolate` function uses this type.\n */\nexport interface Interpolator {\n  // Tuple of parent values\n  <Input extends ReadonlyArray<any>, Output>(\n    parents: Input,\n    interpolator: (...args: Interpolated<Input>) => Output\n  ): Interpolation<Output>\n\n  // Single parent value\n  <Input, Output>(\n    parent: FluidValue<Input> | Input,\n    interpolator: InterpolatorFn<Input, Output>\n  ): Interpolation<Output>\n\n  // Interpolation config\n  <Out>(\n    parents: OneOrMore<FluidValue>,\n    config: InterpolatorConfig<Out>\n  ): Interpolation<Animatable<Out>>\n\n  // Range shortcuts\n  <Out>(\n    parents: OneOrMore<FluidValue<number>> | FluidValue<number[]>,\n    range: readonly number[],\n    output: readonly Constrain<Out, Animatable>[],\n    extrapolate?: ExtrapolateType\n  ): Interpolation<Animatable<Out>>\n}\n", "import { Arrify, InterpolatorArgs, InterpolatorFn } from '@react-spring/types'\nimport {\n  is,\n  raf,\n  each,\n  isEqual,\n  toArray,\n  frameLoop,\n  FluidValue,\n  getFluidValue,\n  createInterpolator,\n  Globals as G,\n  callFluidObservers,\n  addFluidObserver,\n  removeFluidObserver,\n  hasFluidValue,\n} from '@react-spring/shared'\n\nimport { FrameValue, isFrameValue } from './FrameValue'\nimport {\n  getAnimated,\n  setAnimated,\n  getAnimatedType,\n  getPayload,\n} from '@react-spring/animated'\n\n/**\n * An `Interpolation` is a memoized value that's computed whenever one of its\n * `FluidValue` dependencies has its value changed.\n *\n * Other `FrameValue` objects can depend on this. For example, passing an\n * `Interpolation` as the `to` prop of a `useSpring` call will trigger an\n * animation toward the memoized value.\n */\nexport class Interpolation<\n  Input = any,\n  Output = any,\n> extends FrameValue<Output> {\n  /** Useful for debugging. */\n  key?: string\n\n  /** Equals false when in the frameloop */\n  idle = true\n\n  /** The function that maps inputs values to output */\n  readonly calc: InterpolatorFn<Input, Output>\n\n  /** The inputs which are currently animating */\n  protected _active = new Set<FluidValue>()\n\n  constructor(\n    /** The source of input values */\n    readonly source: unknown,\n    args: InterpolatorArgs<Input, Output>\n  ) {\n    super()\n    this.calc = createInterpolator(...args)\n\n    const value = this._get()\n    const nodeType = getAnimatedType(value)\n\n    // Assume the computed value never changes type.\n    setAnimated(this, nodeType.create(value))\n  }\n\n  advance(_dt?: number) {\n    const value = this._get()\n    const oldValue = this.get()\n    if (!isEqual(value, oldValue)) {\n      getAnimated(this)!.setValue(value)\n      this._onChange(value, this.idle)\n    }\n    // Become idle when all parents are idle or paused.\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this)\n    }\n  }\n\n  protected _get() {\n    const inputs: Arrify<Input> = is.arr(this.source)\n      ? this.source.map(getFluidValue)\n      : (toArray(getFluidValue(this.source)) as any)\n\n    return this.calc(...inputs)\n  }\n\n  protected _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false\n\n      each(getPayload(this)!, node => {\n        node.done = false\n      })\n\n      if (G.skipAnimation) {\n        raf.batchedUpdates(() => this.advance())\n        becomeIdle(this)\n      } else {\n        frameLoop.start(this)\n      }\n    }\n  }\n\n  // Observe our sources only when we're observed.\n  protected _attach() {\n    let priority = 1\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        addFluidObserver(source, this)\n      }\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source)\n        }\n        priority = Math.max(priority, source.priority + 1)\n      }\n    })\n    this.priority = priority\n    this._start()\n  }\n\n  // Stop observing our sources once we have no observers.\n  protected _detach() {\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        removeFluidObserver(source, this)\n      }\n    })\n    this._active.clear()\n    becomeIdle(this)\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    // Update our value when an idle parent is changed,\n    // and enter the frameloop when a parent is resumed.\n    if (event.type == 'change') {\n      if (event.idle) {\n        this.advance()\n      } else {\n        this._active.add(event.parent)\n        this._start()\n      }\n    }\n    // Once all parents are idle, the `advance` method runs one more time,\n    // so we should avoid updating the `idle` status here.\n    else if (event.type == 'idle') {\n      this._active.delete(event.parent)\n    }\n    // Ensure our priority is greater than all parents, which means\n    // our value won't be updated until our parents have updated.\n    else if (event.type == 'priority') {\n      this.priority = toArray(this.source).reduce(\n        (highest: number, parent) =>\n          Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1),\n        0\n      )\n    }\n  }\n}\n\n/** Returns true for an idle source. */\nfunction isIdle(source: any) {\n  return source.idle !== false\n}\n\n/** Return true if all values in the given set are idle or paused. */\nfunction checkIdle(active: Set<FluidValue>) {\n  // Parents can be active even when paused, so the `.every` check\n  // removes us from the frameloop if all active parents are paused.\n  return !active.size || Array.from(active).every(isIdle)\n}\n\n/** Become idle if not already idle. */\nfunction becomeIdle(self: Interpolation) {\n  if (!self.idle) {\n    self.idle = true\n\n    each(getPayload(self)!, node => {\n      node.done = true\n    })\n\n    callFluidObservers(self, {\n      type: 'idle',\n      parent: self,\n    })\n  }\n}\n", "import {\n  Globals,\n  frameLoop,\n  createStringInterpolator,\n} from '@react-spring/shared'\nimport { Interpolation } from './Interpolation'\n\n// Sane defaults\nGlobals.assign({\n  createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args),\n})\n\nexport { Globals }\n\n/** Advance all animations by the given time */\nexport const update = frameLoop.advance\n", "export * from './hooks'\nexport * from './components'\nexport * from './interpolate'\nexport * from './constants'\nexport * from './globals'\n\nexport { Controller } from './Controller'\nexport { SpringValue } from './SpringValue'\nexport { SpringContext } from './SpringContext'\nexport { SpringRef } from './SpringRef'\n\nexport { FrameValue } from './FrameValue'\nexport { Interpolation } from './Interpolation'\nexport { BailSignal } from './runAsync'\nexport {\n  createInterpolator,\n  useIsomorphicLayoutEffect,\n  useReducedMotion,\n  easings,\n} from '@react-spring/shared'\nexport { inferTo } from './helpers'\n\nexport * from './types'\nexport * from '@react-spring/types'\n"], "mappings": ";AAAA,SAASA,IAAA,EAAMC,yBAAA,QAAiC;;;ACAhD,SACEC,EAAA,EACAC,OAAA,EACAC,QAAA,EACAC,aAAA,EACAC,gBAAA,EAEAC,OAAA,IAAWC,CAAA,QACN;AAMA,SAASC,SACdC,KAAA,KACGC,IAAA,EACoC;EACvC,OAAOT,EAAA,CAAGU,GAAA,CAAIF,KAAK,IAAIA,KAAA,CAAM,GAAGC,IAAI,IAAID,KAAA;AAC1C;AAGO,IAAMG,SAAA,GAAYA,CACvBH,KAAA,EACAI,GAAA,KAEAJ,KAAA,KAAU,QACV,CAAC,EACCI,GAAA,IACAJ,KAAA,KACCR,EAAA,CAAGU,GAAA,CAAIF,KAAK,IAAIA,KAAA,CAAMI,GAAG,IAAIX,OAAA,CAAQO,KAAK,EAAEK,QAAA,CAASD,GAAG;AAGtD,IAAME,WAAA,GAAcA,CACzBC,IAAA,EACAH,GAAA,KACIZ,EAAA,CAAGgB,GAAA,CAAID,IAAI,IAAIH,GAAA,IAAQG,IAAA,CAAaH,GAAG,IAAIG,IAAA;AAU1C,IAAME,cAAA,GAAiBA,CAC5BC,KAAA,EACAN,GAAA,KAEAM,KAAA,CAAMC,OAAA,KAAY,OACdD,KAAA,CAAMN,GAAG,IACTM,KAAA,CAAMC,OAAA,GACJD,KAAA,CAAMC,OAAA,CAAQP,GAAG,IACjB;AAER,IAAMQ,aAAA,GAAiBZ,KAAA,IAAeA,KAAA;AAS/B,IAAMa,eAAA,GAAkBA,CAC7BH,KAAA,EACAI,SAAA,GAA8CF,aAAA,KACxC;EACN,IAAIG,IAAA,GAA0BC,aAAA;EAC9B,IAAIN,KAAA,CAAMC,OAAA,IAAWD,KAAA,CAAMC,OAAA,KAAY,MAAM;IAC3CD,KAAA,GAAQA,KAAA,CAAMC,OAAA;IACdI,IAAA,GAAOE,MAAA,CAAOF,IAAA,CAAKL,KAAK;EAC1B;EACA,MAAMQ,SAAA,GAAgB,CAAC;EACvB,WAAWd,GAAA,IAAOW,IAAA,EAAM;IACtB,MAAMf,KAAA,GAAQc,SAAA,CAAUJ,KAAA,CAAMN,GAAG,GAAGA,GAAG;IACvC,IAAI,CAACZ,EAAA,CAAG2B,GAAA,CAAInB,KAAK,GAAG;MAClBkB,SAAA,CAASd,GAAG,IAAIJ,KAAA;IAClB;EACF;EACA,OAAOkB,SAAA;AACT;AAaO,IAAMF,aAAA,GAAgB,CAC3B,UACA,WACA,WACA,YACA,WACA,YACA,SACF;AAEA,IAAMI,cAAA,GAEF;EACFC,MAAA,EAAQ;EACRC,IAAA,EAAM;EACNC,EAAA,EAAI;EACJC,GAAA,EAAK;EACLC,IAAA,EAAM;EACNC,KAAA,EAAO;EACPC,KAAA,EAAO;EACPC,MAAA,EAAQ;EACRC,OAAA,EAAS;EACTC,SAAA,EAAW;EACXnB,OAAA,EAAS;EACToB,KAAA,EAAO;EACPC,OAAA,EAAS;EACTC,OAAA,EAAS;EACTC,QAAA,EAAU;EACVC,OAAA,EAAS;EACTC,QAAA,EAAU;EACVC,MAAA,EAAQ;EACRC,SAAA,EAAW;EAAA;EAGXC,KAAA,EAAO;EACPC,KAAA,EAAO;EACPC,IAAA,EAAM;EACNC,OAAA,EAAS;EACTC,OAAA,EAAS;EACTC,KAAA,EAAO;EACPC,MAAA,EAAQ;EACRC,KAAA,EAAO;EACPC,QAAA,EAAU;EACVC,WAAA,EAAa;EAAA;EAGbjC,IAAA,EAAM;EACNkC,MAAA,EAAQ;EACRC,QAAA,EAAU;AACZ;AAOA,SAASC,gBACPzC,KAAA,EACiC;EACjC,MAAM0C,OAAA,GAAe,CAAC;EAEtB,IAAIC,KAAA,GAAQ;EACZ3D,QAAA,CAASgB,KAAA,EAAO,CAACV,KAAA,EAAOO,IAAA,KAAS;IAC/B,IAAI,CAACa,cAAA,CAAeb,IAAI,GAAG;MACzB6C,OAAA,CAAQ7C,IAAI,IAAIP,KAAA;MAChBqD,KAAA;IACF;EACF,CAAC;EAED,IAAIA,KAAA,EAAO;IACT,OAAOD,OAAA;EACT;AACF;AAMO,SAASE,QAA0B5C,KAAA,EAAsB;EAC9D,MAAM6C,GAAA,GAAKJ,eAAA,CAAgBzC,KAAK;EAChC,IAAI6C,GAAA,EAAI;IACN,MAAMC,GAAA,GAAW;MAAEjC,EAAA,EAAAgC;IAAG;IACtB7D,QAAA,CAASgB,KAAA,EAAO,CAAC+C,GAAA,EAAKrD,GAAA,KAAQA,GAAA,IAAOmD,GAAA,KAAOC,GAAA,CAAIpD,GAAG,IAAIqD,GAAA,CAAI;IAC3D,OAAOD,GAAA;EACT;EACA,OAAO;IAAE,GAAG9C;EAAM;AACpB;AAGO,SAASgD,YAAe1D,KAAA,EAA6B;EAC1DA,KAAA,GAAQL,aAAA,CAAcK,KAAK;EAC3B,OAAOR,EAAA,CAAGmE,GAAA,CAAI3D,KAAK,IACfA,KAAA,CAAM4D,GAAA,CAAIF,WAAW,IACrB9D,gBAAA,CAAiBI,KAAK,IACnBF,CAAA,CAAE+D,wBAAA,CAAyB;IAC1BC,KAAA,EAAO,CAAC,GAAG,CAAC;IACZC,MAAA,EAAQ,CAAC/D,KAAA,EAAOA,KAAK;EACvB,CAAC,EAAE,CAAC,IACJA,KAAA;AACR;AAEO,SAASgE,SAAStD,KAAA,EAAe;EACtC,WAAWuD,CAAA,IAAKvD,KAAA,EAAO,OAAO;EAC9B,OAAO;AACT;AAEO,SAASwD,UAAUX,GAAA,EAAS;EACjC,OAAO/D,EAAA,CAAGU,GAAA,CAAIqD,GAAE,KAAM/D,EAAA,CAAGmE,GAAA,CAAIJ,GAAE,KAAK/D,EAAA,CAAGgB,GAAA,CAAI+C,GAAA,CAAG,CAAC,CAAC;AAClD;AAGO,SAASY,WAAWC,IAAA,EAAkB5C,GAAA,EAAiB;EAC5D4C,IAAA,CAAK5C,GAAA,EAAK6C,MAAA,CAAOD,IAAI;EACrB5C,GAAA,EAAK6C,MAAA,CAAOD,IAAI;AAClB;AAGO,SAASE,WAAWF,IAAA,EAAkB5C,GAAA,EAAiB;EAC5D,IAAIA,GAAA,IAAO4C,IAAA,CAAK5C,GAAA,KAAQA,GAAA,EAAK;IAC3B4C,IAAA,CAAK5C,GAAA,EAAK6C,MAAA,CAAOD,IAAI;IACrB5C,GAAA,CAAI+C,GAAA,CAAIH,IAAI;IACZA,IAAA,CAAK5C,GAAA,GAAMA,GAAA;EACb;AACF;;;AD/LO,SAASgD,SACdC,IAAA,EACAC,SAAA,EACAC,SAAA,GAAY,KACZ;EACApF,yBAAA,CAA0B,MAAM;IAC9B,IAAImF,SAAA,EAAW;MACb,IAAIE,SAAA,GAAY;MAChBtF,IAAA,CAAKmF,IAAA,EAAM,CAACjD,GAAA,EAAKqD,CAAA,KAAM;QACrB,MAAMC,WAAA,GAActD,GAAA,CAAIuD,OAAA;QACxB,IAAID,WAAA,CAAYE,MAAA,EAAQ;UACtB,IAAIjD,KAAA,GAAQ4C,SAAA,GAAYD,SAAA,CAAUG,CAAC;UAGnC,IAAII,KAAA,CAAMlD,KAAK,GAAGA,KAAA,GAAQ6C,SAAA,MACrBA,SAAA,GAAY7C,KAAA;UAEjBzC,IAAA,CAAKwF,WAAA,EAAaV,IAAA,IAAQ;YACxB9E,IAAA,CAAK8E,IAAA,CAAKc,KAAA,EAAOxE,KAAA,IAAS;cAExB,MAAMyE,iBAAA,GAAoBzE,KAAA,CAAMqB,KAAA;cAChCrB,KAAA,CAAMqB,KAAA,GAAQ3B,GAAA,IAAO2B,KAAA,GAAQhC,QAAA,CAASoF,iBAAA,IAAqB,GAAG/E,GAAG;YACnE,CAAC;UACH,CAAC;UAEDoB,GAAA,CAAI4D,KAAA,CAAM;QACZ;MACF,CAAC;IACH,OAAO;MACL,IAAIC,CAAA,GAAkBC,OAAA,CAAQC,OAAA,CAAQ;MACtCjG,IAAA,CAAKmF,IAAA,EAAMjD,GAAA,IAAO;QAChB,MAAMsD,WAAA,GAActD,GAAA,CAAIuD,OAAA;QACxB,IAAID,WAAA,CAAYE,MAAA,EAAQ;UAEtB,MAAMQ,MAAA,GAASV,WAAA,CAAYlB,GAAA,CAAIQ,IAAA,IAAQ;YACrC,MAAMqB,CAAA,GAAIrB,IAAA,CAAKc,KAAA;YACfd,IAAA,CAAKc,KAAA,GAAQ,EAAC;YACd,OAAOO,CAAA;UACT,CAAC;UAGDJ,CAAA,GAAIA,CAAA,CAAEK,IAAA,CAAK,MAAM;YACfpG,IAAA,CAAKwF,WAAA,EAAa,CAACV,IAAA,EAAMS,CAAA,KACvBvF,IAAA,CAAKkG,MAAA,CAAOX,CAAC,KAAK,EAAC,EAAGc,OAAA,IAAUvB,IAAA,CAAKc,KAAA,CAAMU,IAAA,CAAKD,OAAM,CAAC,CACzD;YACA,OAAOL,OAAA,CAAQO,GAAA,CAAIrE,GAAA,CAAI4D,KAAA,CAAM,CAAC;UAChC,CAAC;QACH;MACF,CAAC;IACH;EACF,CAAC;AACH;;;AE7EA,SAAS5F,EAAA,IAAAsG,GAAA,QAAU;;;ACDnB,SAASC,UAAA,IAAAC,WAAA,EAAYC,OAAA,EAASC,MAAA,QAAc;AAE5C,SACE1G,EAAA,IAAA2G,GAAA,EACA7G,IAAA,IAAA8G,KAAA,EACAC,OAAA,EACAC,OAAA,EACAC,cAAA,EACAhH,yBAAA,IAAAiH,0BAAA,QACK;;;ACTP,SACEhH,EAAA,IAAAiH,GAAA,EACAC,GAAA,IAAAC,IAAA,EACArH,IAAA,IAAAsH,KAAA,EACAC,OAAA,EACApH,OAAA,IAAAqH,QAAA,EACApH,QAAA,IAAAqH,SAAA,EACAC,SAAA,IAAAC,UAAA,EACAC,UAAA,EACAvH,aAAA,IAAAwH,cAAA,EACAvH,gBAAA,IAAAwH,iBAAA,EAEAvH,OAAA,IAAWwH,EAAA,EACXC,kBAAA,IAAAC,mBAAA,EACAC,aAAA,EACAC,gBAAA,EACAC,mBAAA,EACAC,iBAAA,QACK;AACP,SAEEC,aAAA,EACAC,cAAA,EACAC,UAAA,EACAC,WAAA,IAAAC,YAAA,EACAC,WAAA,EACAC,eAAA,QACK;;;AC3BP,SAAS1I,EAAA,IAAA2I,GAAA,EAAIC,OAAA,QAAe;;;ACCrB,IAAM/G,MAAA,GAAS;EACpBV,OAAA,EAAS;IAAE0H,OAAA,EAAS;IAAKC,QAAA,EAAU;EAAG;EACtCC,MAAA,EAAQ;IAAEF,OAAA,EAAS;IAAKC,QAAA,EAAU;EAAG;EACrCE,MAAA,EAAQ;IAAEH,OAAA,EAAS;IAAKC,QAAA,EAAU;EAAG;EACrCG,KAAA,EAAO;IAAEJ,OAAA,EAAS;IAAKC,QAAA,EAAU;EAAG;EACpCI,IAAA,EAAM;IAAEL,OAAA,EAAS;IAAKC,QAAA,EAAU;EAAG;EACnCK,QAAA,EAAU;IAAEN,OAAA,EAAS;IAAKC,QAAA,EAAU;EAAI;AAC1C;;;ADJA,IAAMM,QAAA,GAAgB;EACpB,GAAGvH,MAAA,CAAQV,OAAA;EACXkI,IAAA,EAAM;EACNC,OAAA,EAAS;EACTC,MAAA,EAAQX,OAAA,CAAQY,MAAA;EAChBC,KAAA,EAAO;AACT;AAEO,IAAMC,eAAA,GAAN,MAAsB;EA2I3BC,YAAA,EAAc;IAnFd;AAAA;AAAA;AAAA;AAAA;IAAA,KAAAC,QAAA,GAA8B;IAoF5BnI,MAAA,CAAOoI,MAAA,CAAO,MAAMT,QAAQ;EAC9B;AACF;AAQO,SAASU,YACdC,OAAA,EACAC,SAAA,EACAC,aAAA,EACA;EACA,IAAIA,aAAA,EAAe;IACjBA,aAAA,GAAgB;MAAE,GAAGA;IAAc;IACnCC,cAAA,CAAeD,aAAA,EAAeD,SAAS;IACvCA,SAAA,GAAY;MAAE,GAAGC,aAAA;MAAe,GAAGD;IAAU;EAC/C;EAEAE,cAAA,CAAeH,OAAA,EAAQC,SAAS;EAChCvI,MAAA,CAAOoI,MAAA,CAAOE,OAAA,EAAQC,SAAS;EAE/B,WAAWpJ,GAAA,IAAOwI,QAAA,EAAU;IAC1B,IAAIW,OAAA,CAAOnJ,GAAG,KAAK,MAAM;MACvBmJ,OAAA,CAAOnJ,GAAG,IAAIwI,QAAA,CAASxI,GAAG;IAC5B;EACF;EAEA,IAAI;IAAEuJ,SAAA;IAAWb;EAAQ,IAAIS,OAAA;EAC7B,MAAM;IAAEV;EAAK,IAAIU,OAAA;EACjB,IAAI,CAACpB,GAAA,CAAGhH,GAAA,CAAIwI,SAAS,GAAG;IACtB,IAAIA,SAAA,GAAY,MAAMA,SAAA,GAAY;IAClC,IAAIb,OAAA,GAAU,GAAGA,OAAA,GAAU;IAC3BS,OAAA,CAAOlB,OAAA,GAAUuB,IAAA,CAAKC,GAAA,CAAK,IAAID,IAAA,CAAKE,EAAA,GAAMH,SAAA,EAAW,CAAC,IAAId,IAAA;IAC1DU,OAAA,CAAOjB,QAAA,GAAY,IAAIsB,IAAA,CAAKE,EAAA,GAAKhB,OAAA,GAAUD,IAAA,GAAQc,SAAA;EACrD;EAEA,OAAOJ,OAAA;AACT;AAIA,SAASG,eACPH,OAAA,EACA7I,KAAA,EACA;EACA,IAAI,CAACyH,GAAA,CAAGhH,GAAA,CAAIT,KAAA,CAAMqJ,KAAK,GAAG;IACxBR,OAAA,CAAOS,QAAA,GAAW;EACpB,OAAO;IACL,MAAMC,eAAA,GAAkB,CAAC9B,GAAA,CAAGhH,GAAA,CAAIT,KAAA,CAAM2H,OAAO,KAAK,CAACF,GAAA,CAAGhH,GAAA,CAAIT,KAAA,CAAM4H,QAAQ;IACxE,IACE2B,eAAA,IACA,CAAC9B,GAAA,CAAGhH,GAAA,CAAIT,KAAA,CAAMiJ,SAAS,KACvB,CAACxB,GAAA,CAAGhH,GAAA,CAAIT,KAAA,CAAMoI,OAAO,KACrB,CAACX,GAAA,CAAGhH,GAAA,CAAIT,KAAA,CAAMmI,IAAI,GAClB;MACAU,OAAA,CAAOS,QAAA,GAAW;MAClBT,OAAA,CAAOQ,KAAA,GAAQ;IACjB;IACA,IAAIE,eAAA,EAAiB;MACnBV,OAAA,CAAOI,SAAA,GAAY;IACrB;EACF;AACF;;;AEnNA,IAAMO,UAAA,GAA6B,EAAC;AAI7B,IAAMC,SAAA,GAAN,MAAyB;EAAzBhB,YAAA;IACL,KAAAiB,OAAA,GAAU;IACV,KAAAC,MAAA,GAAmCH,UAAA;IACnC,KAAAI,QAAA,GAAqC;IACrC,KAAAC,UAAA,GAAgCL,UAAA;IAIhC,KAAA7I,MAAA,GAAS,IAAI6H,eAAA,CAAgB;IAC7B,KAAApH,SAAA,GAAY;EAAA;AACd;;;ACpBA,SAAkBtC,EAAA,IAAAgL,GAAA,EAAI9D,GAAA,EAAK7G,OAAA,IAAW4K,EAAA,QAAS;AAiCxC,SAASC,cACdzH,MAAA,EACA;EAAE7C,GAAA;EAAKM,KAAA;EAAOiK,YAAA;EAAcC,KAAA;EAAOC;AAAQ,GAC3B;EAChB,OAAO,IAAIvF,OAAA,CAAQ,CAACC,OAAA,EAASuF,MAAA,KAAW;IACtC,IAAI/I,KAAA;IACJ,IAAIgJ,OAAA;IAEJ,IAAInJ,MAAA,GAASzB,SAAA,CAAUO,KAAA,CAAMkB,MAAA,IAAU+I,YAAA,EAAc/I,MAAA,EAAQxB,GAAG;IAChE,IAAIwB,MAAA,EAAQ;MACVK,OAAA,CAAQ;IACV,OAAO;MAEL,IAAI,CAACuI,GAAA,CAAGrJ,GAAA,CAAIT,KAAA,CAAMiB,KAAK,GAAG;QACxBiJ,KAAA,CAAMI,MAAA,GAAS7K,SAAA,CAAUO,KAAA,CAAMiB,KAAA,EAAOvB,GAAG;MAC3C;MAGA,IAAIuB,KAAA,GAAQgJ,YAAA,EAAchJ,KAAA;MAC1B,IAAIA,KAAA,KAAU,MAAM;QAClBA,KAAA,GAAQiJ,KAAA,CAAMI,MAAA,IAAU7K,SAAA,CAAUwB,KAAA,EAAOvB,GAAG;MAC9C;MAEA2B,KAAA,GAAQhC,QAAA,CAASW,KAAA,CAAMqB,KAAA,IAAS,GAAG3B,GAAG;MACtC,IAAIuB,KAAA,EAAO;QACTiJ,KAAA,CAAMK,WAAA,CAAY1G,GAAA,CAAInC,QAAQ;QAC9ByI,OAAA,CAAQlJ,KAAA,CAAM;MAChB,OAAO;QACLkJ,OAAA,CAAQK,MAAA,CAAO;QACf9I,QAAA,CAAS;MACX;IACF;IAEA,SAASD,QAAA,EAAU;MACjByI,KAAA,CAAMK,WAAA,CAAY1G,GAAA,CAAInC,QAAQ;MAC9BwI,KAAA,CAAMO,QAAA,CAAS9G,MAAA,CAAO0G,OAAO;MAC7BA,OAAA,CAAQnJ,MAAA,CAAO;MAEfG,KAAA,GAAQgJ,OAAA,CAAQK,IAAA,GAAO1E,GAAA,CAAI2E,GAAA,CAAI;IACjC;IAEA,SAASjJ,SAAA,EAAW;MAClB,IAAIL,KAAA,GAAQ,KAAK,CAAC0I,EAAA,CAAEa,aAAA,EAAe;QACjCV,KAAA,CAAMW,OAAA,GAAU;QAChBR,OAAA,GAAUrE,GAAA,CAAI8E,UAAA,CAAWvJ,OAAA,EAASF,KAAK;QACvC6I,KAAA,CAAMa,UAAA,CAAWlH,GAAA,CAAIpC,OAAO;QAC5ByI,KAAA,CAAMO,QAAA,CAAS5G,GAAA,CAAIwG,OAAO;MAC5B,OAAO;QACL9I,OAAA,CAAQ;MACV;IACF;IAEA,SAASA,QAAA,EAAU;MACjB,IAAI2I,KAAA,CAAMW,OAAA,EAAS;QACjBX,KAAA,CAAMW,OAAA,GAAU;MAClB;MAEAX,KAAA,CAAMa,UAAA,CAAWpH,MAAA,CAAOlC,OAAO;MAC/ByI,KAAA,CAAMO,QAAA,CAAS9G,MAAA,CAAO0G,OAAO;MAG7B,IAAI9H,MAAA,KAAW2H,KAAA,CAAMc,QAAA,IAAY,IAAI;QACnC9J,MAAA,GAAS;MACX;MAEA,IAAI;QACFiJ,OAAA,CAAQzF,KAAA,CAAM;UAAE,GAAG1E,KAAA;UAAOuC,MAAA;UAAQrB;QAAO,GAAG2D,OAAO;MACrD,SAASoG,GAAA,EAAP;QACAb,MAAA,CAAOa,GAAG;MACZ;IACF;EACF,CAAC;AACH;;;ACzGA,SACEnM,EAAA,IAAAoM,GAAA,EACAlF,GAAA,IAAAmF,IAAA,EACAC,KAAA,EACApM,QAAA,IAAAqM,SAAA,EAEAlM,OAAA,IAAWmM,EAAA,QACN;;;ACHA,IAAMC,iBAAA,GAAoBA,CAC/BC,MAAA,EACAC,OAAA,KAEAA,OAAA,CAAQnH,MAAA,IAAU,IACdmH,OAAA,CAAQ,CAAC,IACTA,OAAA,CAAQC,IAAA,CAAKC,MAAA,IAAUA,MAAA,CAAOC,SAAS,IACrCC,kBAAA,CAAmBL,MAAA,CAAOM,GAAA,CAAI,CAAC,IAC/BL,OAAA,CAAQM,KAAA,CAAMJ,MAAA,IAAUA,MAAA,CAAOK,IAAI,IACjCC,aAAA,CAAcT,MAAA,CAAOM,GAAA,CAAI,CAAC,IAC1BI,iBAAA,CACEV,MAAA,CAAOM,GAAA,CAAI,GACXL,OAAA,CAAQM,KAAA,CAAMJ,MAAA,IAAUA,MAAA,CAAOQ,QAAQ,CACzC;AAGH,IAAMF,aAAA,GAAiB3M,KAAA,KAAgB;EAC5CA,KAAA;EACA0M,IAAA,EAAM;EACNG,QAAA,EAAU;EACVP,SAAA,EAAW;AACb;AAEO,IAAMM,iBAAA,GAAoBA,CAC/B5M,KAAA,EACA6M,QAAA,EACAP,SAAA,GAAY,WACR;EACJtM,KAAA;EACA6M,QAAA;EACAP;AACF;AAEO,IAAMC,kBAAA,GAAsBvM,KAAA,KAAgB;EACjDA,KAAA;EACAsM,SAAA,EAAW;EACXO,QAAA,EAAU;AACZ;;;ADKO,SAASC,SACdvJ,GAAA,EACA7C,KAAA,EACAkK,KAAA,EACAsB,MAAA,EACgB;EAChB,MAAM;IAAEjJ,MAAA;IAAQC,QAAA;IAAUb;EAAO,IAAI3B,KAAA;EACrC,MAAM;IAAEqM,OAAA,EAASC,MAAA;IAAQC,OAAA,EAASC;EAAY,IAAItC,KAAA;EAElD,IAAI,CAAC1H,QAAA,IAAYK,GAAA,KAAOyJ,MAAA,IAAU,CAACtM,KAAA,CAAMgB,KAAA,EAAO;IAC9C,OAAOwL,WAAA;EACT;EAEA,OAAQtC,KAAA,CAAMqC,OAAA,IAAW,YAAY;IACnCrC,KAAA,CAAMuC,OAAA,GAAUlK,MAAA;IAChB2H,KAAA,CAAMmC,OAAA,GAAUxJ,GAAA;IAGhB,MAAMoH,YAAA,GAAe9J,eAAA,CAA+BH,KAAA,EAAO,CAACV,KAAA,EAAOI,GAAA;IAAA;IAEjEA,GAAA,KAAQ,WAAW,SAAYJ,KACjC;IAEA,IAAIoN,WAAA;IACJ,IAAIC,IAAA;IAGJ,MAAMC,WAAA,GAAc,IAAIhI,OAAA,CACtB,CAACC,OAAA,EAASuF,MAAA,MAAasC,WAAA,GAAc7H,OAAA,EAAW8H,IAAA,GAAOvC,MAAA,CACzD;IAEA,MAAMyC,WAAA,GAAeC,UAAA,IAA2B;MAC9C,MAAMC,UAAA;MAAA;MAEHxK,MAAA,KAAW2H,KAAA,CAAMc,QAAA,IAAY,MAAMa,kBAAA,CAAmBL,MAAM;MAAA;MAE5DjJ,MAAA,KAAW2H,KAAA,CAAMuC,OAAA,IAAWP,iBAAA,CAAkBV,MAAA,EAAQ,KAAK;MAE9D,IAAIuB,UAAA,EAAY;QACdD,UAAA,CAAWnB,MAAA,GAASoB,UAAA;QAIpBJ,IAAA,CAAKG,UAAU;QACf,MAAMA,UAAA;MACR;IACF;IAEA,MAAME,OAAA,GAAeA,CAACC,IAAA,EAAWC,IAAA,KAAe;MAG9C,MAAMJ,UAAA,GAAa,IAAIK,UAAA,CAAW;MAClC,MAAMC,mBAAA,GAAsB,IAAIC,mBAAA,CAAoB;MAEpD,QAAQ,YAAY;QAClB,IAAI/B,EAAA,CAAEV,aAAA,EAAe;UAMnB0C,SAAA,CAAUpD,KAAK;UAGfkD,mBAAA,CAAoBzB,MAAA,GAASO,iBAAA,CAAkBV,MAAA,EAAQ,KAAK;UAC5DmB,IAAA,CAAKS,mBAAmB;UACxB,MAAMA,mBAAA;QACR;QAEAP,WAAA,CAAYC,UAAU;QAEtB,MAAMS,MAAA,GAAarC,GAAA,CAAGpL,GAAA,CAAImN,IAAI,IAAI;UAAE,GAAGA;QAAK,IAAI;UAAE,GAAGC,IAAA;UAAMrM,EAAA,EAAIoM;QAAK;QACpEM,MAAA,CAAM/K,QAAA,GAAWD,MAAA;QAEjB8I,SAAA,CAASpB,YAAA,EAAc,CAAC3K,KAAA,EAAOI,GAAA,KAAQ;UACrC,IAAIwL,GAAA,CAAGzK,GAAA,CAAI8M,MAAA,CAAM7N,GAAG,CAAC,GAAG;YACtB6N,MAAA,CAAM7N,GAAG,IAAIJ,KAAA;UACf;QACF,CAAC;QAED,MAAMkO,OAAA,GAAS,MAAMhC,MAAA,CAAO9G,KAAA,CAAM6I,MAAK;QACvCV,WAAA,CAAYC,UAAU;QAEtB,IAAI5C,KAAA,CAAMI,MAAA,EAAQ;UAChB,MAAM,IAAI1F,OAAA,CAAc4F,MAAA,IAAU;YAChCN,KAAA,CAAMK,WAAA,CAAY1G,GAAA,CAAI2G,MAAM;UAC9B,CAAC;QACH;QAEA,OAAOgD,OAAA;MACT,GAAG;IACL;IAEA,IAAI7B,MAAA;IAEJ,IAAIL,EAAA,CAAEV,aAAA,EAAe;MAKnB0C,SAAA,CAAUpD,KAAK;MACf,OAAOgC,iBAAA,CAAkBV,MAAA,EAAQ,KAAK;IACxC;IAEA,IAAI;MACF,IAAIiC,SAAA;MAGJ,IAAIvC,GAAA,CAAGjI,GAAA,CAAIJ,GAAE,GAAG;QACd4K,SAAA,IAAa,MAAOjJ,KAAA,IAAiB;UACnC,WAAW+I,MAAA,IAAS/I,KAAA,EAAO;YACzB,MAAMwI,OAAA,CAAQO,MAAK;UACrB;QACF,GAAG1K,GAAE;MACP,OAGK;QACH4K,SAAA,GAAY7I,OAAA,CAAQC,OAAA,CAAQhC,GAAA,CAAGmK,OAAA,EAASxB,MAAA,CAAOkC,IAAA,CAAKC,IAAA,CAAKnC,MAAM,CAAC,CAAC;MACnE;MAEA,MAAM5G,OAAA,CAAQO,GAAA,CAAI,CAACsI,SAAA,CAAUzI,IAAA,CAAK0H,WAAW,GAAGE,WAAW,CAAC;MAC5DjB,MAAA,GAASO,iBAAA,CAAkBV,MAAA,CAAOM,GAAA,CAAI,GAAG,MAAM,KAAK;IAGtD,SAASb,GAAA,EAAP;MACA,IAAIA,GAAA,YAAekC,UAAA,EAAY;QAC7BxB,MAAA,GAASV,GAAA,CAAIU,MAAA;MACf,WAAWV,GAAA,YAAeoC,mBAAA,EAAqB;QAC7C1B,MAAA,GAASV,GAAA,CAAIU,MAAA;MACf,OAAO;QACL,MAAMV,GAAA;MACR;IAGF,UAAE;MACA,IAAI1I,MAAA,IAAU2H,KAAA,CAAMuC,OAAA,EAAS;QAC3BvC,KAAA,CAAMuC,OAAA,GAAUjK,QAAA;QAChB0H,KAAA,CAAMmC,OAAA,GAAU7J,QAAA,GAAW8J,MAAA,GAAS;QACpCpC,KAAA,CAAMqC,OAAA,GAAU/J,QAAA,GAAWgK,WAAA,GAAc;MAC3C;IACF;IAEA,IAAItB,GAAA,CAAG1L,GAAA,CAAImC,MAAM,GAAG;MAClBwJ,IAAA,CAAIyC,cAAA,CAAe,MAAM;QACvBjM,MAAA,CAAOgK,MAAA,EAAQH,MAAA,EAAQA,MAAA,CAAOqC,IAAI;MACpC,CAAC;IACH;IAEA,OAAOlC,MAAA;EACT,GAAG;AACL;AAGO,SAAS2B,UAAUpD,KAAA,EAAsBc,QAAA,EAA2B;EACzEI,KAAA,CAAMlB,KAAA,CAAMO,QAAA,EAAUqD,CAAA,IAAKA,CAAA,CAAE5M,MAAA,CAAO,CAAC;EACrCgJ,KAAA,CAAMa,UAAA,CAAWgD,KAAA,CAAM;EACvB7D,KAAA,CAAMK,WAAA,CAAYwD,KAAA,CAAM;EACxB7D,KAAA,CAAMuC,OAAA,GAAUvC,KAAA,CAAMmC,OAAA,GAAUnC,KAAA,CAAMqC,OAAA,GAAU;EAChD,IAAIvB,QAAA,EAAUd,KAAA,CAAMc,QAAA,GAAWA,QAAA;AACjC;AAGO,IAAMmC,UAAA,GAAN,cAAyBa,KAAA,CAAM;EAEpCvF,YAAA,EAAc;IACZ,MACE,yIAEF;EACF;AACF;AAEO,IAAM4E,mBAAA,GAAN,cAAkCW,KAAA,CAAM;EAG7CvF,YAAA,EAAc;IACZ,MAAM,qBAAqB;EAC7B;AACF;;;AEjOA,SACEwF,oBAAA,EACA3H,SAAA,EACA4H,UAAA,IAAAC,WAAA,EACAhP,OAAA,IAAWiP,EAAA,EACXxH,kBAAA,QACK;AAEP,SAASS,WAAA,QAAmB;AAIrB,IAAMgH,YAAA,GAAgB/O,KAAA,IAC3BA,KAAA,YAAiBgP,UAAA;AAEnB,IAAIC,MAAA,GAAS;AAON,IAAeD,UAAA,GAAf,cAA2CH,WAAA,CAGhD;EAHK1F,YAAA;IAAA,SAAA+F,SAAA;IAIL,KAASC,EAAA,GAAKF,MAAA;IAKd,KAAUG,SAAA,GAAY;EAAA;EAEtB,IAAIC,SAAA,EAAW;IACb,OAAO,KAAKD,SAAA;EACd;EACA,IAAIC,SAASA,QAAA,EAAkB;IAC7B,IAAI,KAAKD,SAAA,IAAaC,QAAA,EAAU;MAC9B,KAAKD,SAAA,GAAYC,QAAA;MACjB,KAAKC,iBAAA,CAAkBD,QAAQ;IACjC;EACF;EAAA;EAGA7C,IAAA,EAAS;IACP,MAAM+C,IAAA,GAAOxH,WAAA,CAAY,IAAI;IAC7B,OAAOwH,IAAA,IAAQA,IAAA,CAAKC,QAAA,CAAS;EAC/B;EAAA;EAGAjO,GAAA,GAAWtB,IAAA,EAAgC;IACzC,OAAO6O,EAAA,CAAEvN,EAAA,CAAG,MAAMtB,IAAI;EACxB;EAAA;EAGAwP,YAAA,GAAoBxP,IAAA,EAAgC;IAClD0O,oBAAA,CAAqB;IACrB,OAAOG,EAAA,CAAEvN,EAAA,CAAG,MAAMtB,IAAI;EACxB;EAEAyP,OAAA,EAAS;IACP,OAAO,KAAKlD,GAAA,CAAI;EAClB;EAEUmD,cAActM,KAAA,EAAe;IACrC,IAAIA,KAAA,IAAS,GAAG,KAAKuM,OAAA,CAAQ;EAC/B;EAEUC,gBAAgBxM,KAAA,EAAe;IACvC,IAAIA,KAAA,IAAS,GAAG,KAAKyM,OAAA,CAAQ;EAC/B;EAAA;EASUF,QAAA,EAAU,CAAC;EAAA;EAGXE,QAAA,EAAU,CAAC;EAAA;EAGXC,UAAU/P,KAAA,EAAUgQ,IAAA,GAAO,OAAO;IAC1C1I,kBAAA,CAAmB,MAAM;MACvB2I,IAAA,EAAM;MACNC,MAAA,EAAQ;MACRlQ,KAAA;MACAgQ;IACF,CAAC;EACH;EAAA;EAGUV,kBAAkBD,QAAA,EAAkB;IAC5C,IAAI,CAAC,KAAKW,IAAA,EAAM;MACdhJ,SAAA,CAAUvE,IAAA,CAAK,IAAI;IACrB;IACA6E,kBAAA,CAAmB,MAAM;MACvB2I,IAAA,EAAM;MACNC,MAAA,EAAQ;MACRb;IACF,CAAC;EACH;AACF;;;ACxGA,IAAMc,EAAA,GAAKC,MAAA,CAAOC,GAAA,CAAI,aAAa;AAEnC,IAAMC,YAAA,GAAe;AACrB,IAAMC,YAAA,GAAe;AACrB,IAAMC,SAAA,GAAY;AAGX,IAAMC,WAAA,GAAevE,MAAA,KAAiBA,MAAA,CAAOiE,EAAE,IAAIG,YAAA,IAAgB;AAGnE,IAAMI,WAAA,GAAexE,MAAA,KAAiBA,MAAA,CAAOiE,EAAE,IAAII,YAAA,IAAgB;AAGnE,IAAMI,QAAA,GAAYzE,MAAA,KAAiBA,MAAA,CAAOiE,EAAE,IAAIK,SAAA,IAAa;AAG7D,IAAMI,YAAA,GAAeA,CAAC1E,MAAA,EAAa2E,MAAA,KACxCA,MAAA,GACK3E,MAAA,CAAOiE,EAAE,KAAKI,YAAA,GAAeD,YAAA,GAC7BpE,MAAA,CAAOiE,EAAE,KAAK,CAACI,YAAA;AAEf,IAAMO,YAAA,GAAeA,CAAC5E,MAAA,EAAalB,MAAA,KACxCA,MAAA,GAAUkB,MAAA,CAAOiE,EAAE,KAAKK,SAAA,GAActE,MAAA,CAAOiE,EAAE,KAAK,CAACK,SAAA;;;ARqDhD,IAAMO,WAAA,GAAN,cAAmC/B,UAAA,CAAc;EAmCtD7F,YAAYwE,IAAA,EAAYC,IAAA,EAAY;IAClC,MAAM;IA/BR;IAAA,KAAAoD,SAAA,GAAY,IAAI7G,SAAA,CAAa;IAM7B;IAAA,KAAAQ,YAAA,GAAsC,CAAC;IAGvC;IAAA,KAAUsG,MAAA,GAAwC;MAChDjG,MAAA,EAAQ;MACRO,OAAA,EAAS;MACTE,UAAA,EAAY,mBAAIyF,GAAA,CAAI;MACpBjG,WAAA,EAAa,mBAAIiG,GAAA,CAAI;MACrB/F,QAAA,EAAU,mBAAI+F,GAAA,CAAI;IACpB;IAGA;IAAA,KAAUC,aAAA,GAAgB,mBAAID,GAAA,CAA6B;IAG3D;IAAA,KAAUE,WAAA,GAAc;IAGxB;IAAA,KAAUC,SAAA,GAAY;IAEtB,KAAUC,iBAAA,GAAoB;IAM5B,IAAI,CAAC7K,GAAA,CAAGtF,GAAA,CAAIwM,IAAI,KAAK,CAAClH,GAAA,CAAGtF,GAAA,CAAIyM,IAAI,GAAG;MAClC,MAAMlN,KAAA,GAAQ+F,GAAA,CAAGjG,GAAA,CAAImN,IAAI,IAAI;QAAE,GAAGA;MAAK,IAAI;QAAE,GAAGC,IAAA;QAAMtM,IAAA,EAAMqM;MAAK;MACjE,IAAIlH,GAAA,CAAGtF,GAAA,CAAIT,KAAA,CAAMC,OAAO,GAAG;QACzBD,KAAA,CAAMC,OAAA,GAAU;MAClB;MACA,KAAKyE,KAAA,CAAM1E,KAAK;IAClB;EACF;EAAA;EAGA,IAAIsP,KAAA,EAAO;IACT,OAAO,EAAEU,WAAA,CAAY,IAAI,KAAK,KAAKO,MAAA,CAAOlE,OAAA,KAAY4D,QAAA,CAAS,IAAI;EACrE;EAEA,IAAIY,KAAA,EAAO;IACT,OAAOpK,cAAA,CAAc,KAAK6J,SAAA,CAAUzP,EAAE;EACxC;EAEA,IAAI6H,SAAA,EAA4B;IAC9B,MAAMmG,IAAA,GAAOvH,YAAA,CAAY,IAAI;IAC7B,OACEuH,IAAA,YAAgB3H,aAAA,GACZ2H,IAAA,CAAKiC,YAAA,IAAgB,IACrBjC,IAAA,CAAKzH,UAAA,CAAW,EAAElE,GAAA,CAAI6N,KAAA,IAAQA,KAAA,CAAKD,YAAA,IAAgB,CAAC;EAE5D;EAAA;AAAA;AAAA;EAKA,IAAIf,YAAA,EAAc;IAChB,OAAOA,WAAA,CAAY,IAAI;EACzB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIC,YAAA,EAAc;IAChB,OAAOA,WAAA,CAAY,IAAI;EACzB;EAAA;AAAA;AAAA;EAKA,IAAIC,SAAA,EAAW;IACb,OAAOA,QAAA,CAAS,IAAI;EACtB;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIe,UAAA,EAAY;IACd,OAAO,KAAKT,MAAA,CAAO1F,OAAA;EACrB;EAAA;EAGAoG,QAAQC,EAAA,EAAY;IAClB,IAAI5B,IAAA,GAAO;IACX,IAAI5F,OAAA,GAAU;IAEd,MAAMyH,IAAA,GAAO,KAAKb,SAAA;IAClB,IAAI;MAAE1G;IAAS,IAAIuH,IAAA;IACnB,MAAM;MAAExQ,MAAA,EAAAkI;IAAO,IAAIsI,IAAA;IAEnB,MAAMC,OAAA,GAAUhK,UAAA,CAAW+J,IAAA,CAAKtQ,EAAE;IAClC,IAAI,CAACuQ,OAAA,IAAWtK,aAAA,CAAcqK,IAAA,CAAKtQ,EAAE,GAAG;MACtC+I,QAAA,GAAWxD,QAAA,CAAQK,cAAA,CAAc0K,IAAA,CAAKtQ,EAAE,CAAC;IAC3C;IAEAsQ,IAAA,CAAKxH,MAAA,CAAO0H,OAAA,CAAQ,CAACN,KAAA,EAAM5M,CAAA,KAAM;MAC/B,IAAI4M,KAAA,CAAKO,IAAA,EAAM;MAEf,MAAMzO,GAAA;MAAA;MAEJkO,KAAA,CAAKtI,WAAA,IAAetB,cAAA,GAChB,IACAiK,OAAA,GACEA,OAAA,CAAQjN,CAAC,EAAEoN,YAAA,GACX3H,QAAA,CAAUzF,CAAC;MAEnB,IAAIgI,QAAA,GAAWgF,IAAA,CAAK/P,SAAA;MACpB,IAAIoQ,QAAA,GAAW3O,GAAA;MAEf,IAAI,CAACsJ,QAAA,EAAU;QACbqF,QAAA,GAAWT,KAAA,CAAKQ,YAAA;QAGhB,IAAI1I,OAAA,CAAOlB,OAAA,IAAW,GAAG;UACvBoJ,KAAA,CAAKO,IAAA,GAAO;UACZ;QACF;QAEA,IAAIG,OAAA,GAAWV,KAAA,CAAKW,WAAA,IAAeR,EAAA;QACnC,MAAMtQ,IAAA,GAAOuQ,IAAA,CAAKtH,UAAA,CAAW1F,CAAC;QAE9B,MAAMwN,EAAA,GACJZ,KAAA,CAAKY,EAAA,IAAM,OACPZ,KAAA,CAAKY,EAAA,GACJZ,KAAA,CAAKY,EAAA,GAAK5L,GAAA,CAAG9C,GAAA,CAAI4F,OAAA,CAAOH,QAAQ,IAC7BG,OAAA,CAAOH,QAAA,CAASvE,CAAC,IACjB0E,OAAA,CAAOH,QAAA;QAEjB,IAAIA,QAAA;QAOJ,MAAMkJ,SAAA,GACJ/I,OAAA,CAAO+I,SAAA,KACNhR,IAAA,IAAQiC,GAAA,GAAK,OAAQqG,IAAA,CAAK2I,GAAA,CAAI,GAAG3I,IAAA,CAAK4I,GAAA,CAAIjP,GAAA,GAAKjC,IAAI,IAAI,IAAK;QAG/D,IAAI,CAACmF,GAAA,CAAGtF,GAAA,CAAIoI,OAAA,CAAOS,QAAQ,GAAG;UAC5B,IAAI3E,CAAA,GAAI;UACR,IAAIkE,OAAA,CAAOS,QAAA,GAAW,GAAG;YAOvB,IAAI,KAAKsH,iBAAA,KAAsB/H,OAAA,CAAOS,QAAA,EAAU;cAE9C,KAAKsH,iBAAA,GAAoB/H,OAAA,CAAOS,QAAA;cAGhC,IAAIyH,KAAA,CAAKgB,gBAAA,GAAmB,GAAG;gBAE7BhB,KAAA,CAAKW,WAAA,GAAc7I,OAAA,CAAOS,QAAA,GAAWyH,KAAA,CAAKgB,gBAAA;gBAE1CN,OAAA,GAAUV,KAAA,CAAKW,WAAA,IAAeR,EAAA;cAChC;YACF;YAGAvM,CAAA,IAAKkE,OAAA,CAAOmJ,QAAA,IAAY,KAAKP,OAAA,GAAU,KAAKb,iBAAA;YAE5CjM,CAAA,GAAIA,CAAA,GAAI,IAAI,IAAIA,CAAA,GAAI,IAAI,IAAIA,CAAA;YAE5BoM,KAAA,CAAKgB,gBAAA,GAAmBpN,CAAA;UAC1B;UAEA6M,QAAA,GAAW5Q,IAAA,GAAOiI,OAAA,CAAOR,MAAA,CAAO1D,CAAC,KAAK9B,GAAA,GAAKjC,IAAA;UAC3C8H,QAAA,IAAY8I,QAAA,GAAWT,KAAA,CAAKQ,YAAA,IAAgBL,EAAA;UAE5C/E,QAAA,GAAWxH,CAAA,IAAK;QAClB,WAGSkE,OAAA,CAAOQ,KAAA,EAAO;UACrB,MAAMA,KAAA,GAAQR,OAAA,CAAOQ,KAAA,KAAU,OAAO,QAAQR,OAAA,CAAOQ,KAAA;UACrD,MAAM4I,CAAA,GAAI/I,IAAA,CAAKgJ,GAAA,CAAI,EAAE,IAAI7I,KAAA,IAASoI,OAAO;UAEzCD,QAAA,GAAW5Q,IAAA,GAAQ+Q,EAAA,IAAM,IAAItI,KAAA,KAAW,IAAI4I,CAAA;UAC5C9F,QAAA,GAAWjD,IAAA,CAAK4I,GAAA,CAAIf,KAAA,CAAKQ,YAAA,GAAeC,QAAQ,KAAKI,SAAA;UAGrDlJ,QAAA,GAAWiJ,EAAA,GAAKM,CAAA;QAClB,OAGK;UACHvJ,QAAA,GAAWqI,KAAA,CAAKD,YAAA,IAAgB,OAAOa,EAAA,GAAKZ,KAAA,CAAKD,YAAA;UAGjD,MAAMqB,YAAA,GAAetJ,OAAA,CAAOsJ,YAAA,IAAgBP,SAAA,GAAY;UAGxD,MAAMQ,YAAA,GAAevJ,OAAA,CAAON,KAAA,GAAQ,IAAIM,OAAA,CAAOwJ,MAAA;UAC/C,MAAMC,SAAA,GAAY,CAACvM,GAAA,CAAGtF,GAAA,CAAI2R,YAAY;UAGtC,MAAMG,SAAA,GAAY3R,IAAA,IAAQiC,GAAA,GAAKkO,KAAA,CAAKY,EAAA,GAAK,IAAI/Q,IAAA,GAAOiC,GAAA;UAGpD,IAAI2P,QAAA;UAGJ,IAAIC,UAAA,GAAa;UAEjB,MAAMC,IAAA,GAAO;UACb,MAAMC,QAAA,GAAWzJ,IAAA,CAAK0J,IAAA,CAAK1B,EAAA,GAAKwB,IAAI;UACpC,SAASG,CAAA,GAAI,GAAGA,CAAA,GAAIF,QAAA,EAAU,EAAEE,CAAA,EAAG;YACjCL,QAAA,GAAWtJ,IAAA,CAAK4I,GAAA,CAAIpJ,QAAQ,IAAIyJ,YAAA;YAEhC,IAAI,CAACK,QAAA,EAAU;cACbrG,QAAA,GAAWjD,IAAA,CAAK4I,GAAA,CAAIjP,GAAA,GAAK2O,QAAQ,KAAKI,SAAA;cACtC,IAAIzF,QAAA,EAAU;gBACZ;cACF;YACF;YAEA,IAAImG,SAAA,EAAW;cACbG,UAAA,GAAajB,QAAA,IAAY3O,GAAA,IAAM2O,QAAA,GAAW3O,GAAA,IAAM0P,SAAA;cAGhD,IAAIE,UAAA,EAAY;gBACd/J,QAAA,GAAW,CAACA,QAAA,GAAW0J,YAAA;gBACvBZ,QAAA,GAAW3O,GAAA;cACb;YACF;YAEA,MAAMiQ,WAAA,GAAc,CAACjK,OAAA,CAAOlB,OAAA,GAAU,QAAY6J,QAAA,GAAW3O,GAAA;YAC7D,MAAMkQ,YAAA,GAAe,CAAClK,OAAA,CAAOjB,QAAA,GAAW,OAAQc,QAAA;YAChD,MAAMsK,YAAA,IAAgBF,WAAA,GAAcC,YAAA,IAAgBlK,OAAA,CAAOV,IAAA;YAE3DO,QAAA,GAAWA,QAAA,GAAWsK,YAAA,GAAeN,IAAA;YACrClB,QAAA,GAAWA,QAAA,GAAW9I,QAAA,GAAWgK,IAAA;UACnC;QACF;QAEA3B,KAAA,CAAKD,YAAA,GAAepI,QAAA;QAEpB,IAAIuK,MAAA,CAAO1O,KAAA,CAAMiN,QAAQ,GAAG;UAC1B0B,OAAA,CAAQC,IAAA,CAAK,4BAA4B,IAAI;UAC7ChH,QAAA,GAAW;QACb;MACF;MAGA,IAAIiF,OAAA,IAAW,CAACA,OAAA,CAAQjN,CAAC,EAAEmN,IAAA,EAAM;QAC/BnF,QAAA,GAAW;MACb;MAEA,IAAIA,QAAA,EAAU;QACZ4E,KAAA,CAAKO,IAAA,GAAO;MACd,OAAO;QACLhC,IAAA,GAAO;MACT;MAEA,IAAIyB,KAAA,CAAKqC,QAAA,CAAS5B,QAAA,EAAU3I,OAAA,CAAOwK,KAAK,GAAG;QACzC3J,OAAA,GAAU;MACZ;IACF,CAAC;IAED,MAAMmF,IAAA,GAAOvH,YAAA,CAAY,IAAI;IAK7B,MAAMgM,OAAA,GAAUzE,IAAA,CAAKC,QAAA,CAAS;IAC9B,IAAIQ,IAAA,EAAM;MAER,MAAMiE,QAAA,GAAW9M,cAAA,CAAc0K,IAAA,CAAKtQ,EAAE;MAKtC,KAAKyS,OAAA,KAAYC,QAAA,IAAY7J,OAAA,KAAY,CAACb,OAAA,CAAOQ,KAAA,EAAO;QAEtDwF,IAAA,CAAKuE,QAAA,CAASG,QAAQ;QACtB,KAAKlE,SAAA,CAAUkE,QAAQ;MACzB,WAAW7J,OAAA,IAAWb,OAAA,CAAOQ,KAAA,EAAO;QAKlC,KAAKgG,SAAA,CAAUiE,OAAO;MACxB;MAEA,KAAKE,KAAA,CAAM;IACb,WAAW9J,OAAA,EAAS;MAKlB,KAAK2F,SAAA,CAAUiE,OAAO;IACxB;EACF;EAAA;EAGAG,IAAInU,KAAA,EAA0B;IAC5B2G,IAAA,CAAI2H,cAAA,CAAe,MAAM;MACvB,KAAK4F,KAAA,CAAM;MAIX,KAAKE,MAAA,CAAOpU,KAAK;MACjB,KAAKqU,IAAA,CAAKrU,KAAK;IACjB,CAAC;IACD,OAAO;EACT;EAAA;AAAA;AAAA;AAAA;EAMA2B,MAAA,EAAQ;IACN,KAAK2S,OAAA,CAAQ;MAAE3S,KAAA,EAAO;IAAK,CAAC;EAC9B;EAAA;EAGAuJ,OAAA,EAAS;IACP,KAAKoJ,OAAA,CAAQ;MAAE3S,KAAA,EAAO;IAAM,CAAC;EAC/B;EAAA;EAGA4S,OAAA,EAAS;IACP,IAAI7D,WAAA,CAAY,IAAI,GAAG;MACrB,MAAM;QAAEnP,EAAA,EAAAgC,GAAA;QAAIlC,MAAA,EAAAkI;MAAO,IAAI,KAAKyH,SAAA;MAC5BrK,IAAA,CAAI2H,cAAA,CAAe,MAAM;QAEvB,KAAKkG,QAAA,CAAS;QAId,IAAI,CAACjL,OAAA,CAAOQ,KAAA,EAAO;UACjB,KAAKsK,IAAA,CAAK9Q,GAAA,EAAI,KAAK;QACrB;QAEA,KAAK2Q,KAAA,CAAM;MACb,CAAC;IACH;IACA,OAAO;EACT;EAAA;EAGArR,OAAOnC,KAAA,EAAwB;IAC7B,MAAMwE,KAAA,GAAQ,KAAKA,KAAA,KAAU,KAAKA,KAAA,GAAQ,EAAC;IAC3CA,KAAA,CAAMU,IAAA,CAAKlF,KAAK;IAChB,OAAO;EACT;EAeA0E,MAAM7B,GAAA,EAAUqK,IAAA,EAAY;IAC1B,IAAI1I,KAAA;IACJ,IAAI,CAACuB,GAAA,CAAGtF,GAAA,CAAIoC,GAAE,GAAG;MACf2B,KAAA,GAAQ,CAACuB,GAAA,CAAGjG,GAAA,CAAI+C,GAAE,IAAIA,GAAA,GAAK;QAAE,GAAGqK,IAAA;QAAMrM,EAAA,EAAAgC;MAAG,CAAC;IAC5C,OAAO;MACL2B,KAAA,GAAQ,KAAKA,KAAA,IAAS,EAAC;MACvB,KAAKA,KAAA,GAAQ,EAAC;IAChB;IAEA,OAAOI,OAAA,CAAQO,GAAA,CACbX,KAAA,CAAMtB,GAAA,CAAIlD,KAAA,IAAS;MACjB,MAAM+T,EAAA,GAAK,KAAKH,OAAA,CAAQ5T,KAAK;MAC7B,OAAO+T,EAAA;IACT,CAAC,CACH,EAAE/O,IAAA,CAAKyG,OAAA,IAAWF,iBAAA,CAAkB,MAAME,OAAO,CAAC;EACpD;EAAA;AAAA;AAAA;AAAA;AAAA;EAOAiC,KAAKxM,MAAA,EAAkB;IACrB,MAAM;MAAEL,EAAA,EAAAgC;IAAG,IAAI,KAAKyN,SAAA;IAGpB,KAAKoD,MAAA,CAAO,KAAK5H,GAAA,CAAI,CAAC;IAEtBwB,SAAA,CAAU,KAAKiD,MAAA,EAAQrP,MAAA,IAAU,KAAKwP,WAAW;IACjDzK,IAAA,CAAI2H,cAAA,CAAe,MAAM,KAAK4F,KAAA,CAAM3Q,GAAA,EAAI3B,MAAM,CAAC;IAE/C,OAAO;EACT;EAAA;EAGAF,MAAA,EAAQ;IACN,KAAK4S,OAAA,CAAQ;MAAE5S,KAAA,EAAO;IAAK,CAAC;EAC9B;EAAA;EAGAgT,cAAcC,KAAA,EAAyB;IACrC,IAAIA,KAAA,CAAM1E,IAAA,IAAQ,UAAU;MAC1B,KAAK2E,MAAA,CAAO;IACd,WAAWD,KAAA,CAAM1E,IAAA,IAAQ,YAAY;MACnC,KAAKZ,QAAA,GAAWsF,KAAA,CAAMtF,QAAA,GAAW;IACnC;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQUwF,aAAanU,KAAA,EAKpB;IACD,MAAMN,GAAA,GAAM,KAAKA,GAAA,IAAO;IAExB,IAAI;MAAEmB,EAAA,EAAAgC,GAAA;MAAIjC;IAAK,IAAIZ,KAAA;IAEnB6C,GAAA,GAAKkD,GAAA,CAAGjG,GAAA,CAAI+C,GAAE,IAAIA,GAAA,CAAGnD,GAAG,IAAImD,GAAA;IAC5B,IAAIA,GAAA,IAAM,QAAQW,SAAA,CAAUX,GAAE,GAAG;MAC/BA,GAAA,GAAK;IACP;IAEAjC,IAAA,GAAOmF,GAAA,CAAGjG,GAAA,CAAIc,IAAI,IAAIA,IAAA,CAAKlB,GAAG,IAAIkB,IAAA;IAClC,IAAIA,IAAA,IAAQ,MAAM;MAChBA,IAAA,GAAO;IACT;IAGA,MAAMwC,KAAA,GAAQ;MAAEvC,EAAA,EAAAgC,GAAA;MAAIjC;IAAK;IAIzB,IAAI,CAACmP,WAAA,CAAY,IAAI,GAAG;MACtB,IAAI/P,KAAA,CAAMmB,OAAA,EAAS,CAAC0B,GAAA,EAAIjC,IAAI,IAAI,CAACA,IAAA,EAAMiC,GAAE;MAEzCjC,IAAA,GAAO6F,cAAA,CAAc7F,IAAI;MACzB,IAAI,CAACmF,GAAA,CAAGtF,GAAA,CAAIG,IAAI,GAAG;QACjB,KAAK+S,IAAA,CAAK/S,IAAI;MAChB,WAES,CAAC0G,YAAA,CAAY,IAAI,GAAG;QAC3B,KAAKqM,IAAA,CAAK9Q,GAAE;MACd;IACF;IAEA,OAAOO,KAAA;EACT;EAAA;EAGUwQ,QACR;IAAE,GAAG5T;EAAM,GACXoU,MAAA,EAC6B;IAC7B,MAAM;MAAE1U,GAAA;MAAKuK;IAAa,IAAI;IAG9B,IAAIjK,KAAA,CAAMC,OAAA,EACRM,MAAA,CAAOoI,MAAA,CACLsB,YAAA,EACA9J,eAAA,CAAgBH,KAAA,EAAO,CAACV,KAAA,EAAOO,IAAA,KAC7B,MAAMwU,IAAA,CAAKxU,IAAI,IAAID,WAAA,CAAYN,KAAA,EAAOI,GAAG,IAAIJ,KAC/C,CACF;IAEFgV,aAAA,CAAc,MAAMtU,KAAA,EAAO,SAAS;IACpCuU,SAAA,CAAU,MAAM,WAAWvU,KAAA,EAAO,IAAI;IAGtC,MAAMoD,KAAA,GAAQ,KAAK+Q,YAAA,CAAanU,KAAK;IAErC,IAAIO,MAAA,CAAOiU,QAAA,CAAS,IAAI,GAAG;MACzB,MAAMxG,KAAA,CACJ,4IAEF;IACF;IAEA,MAAM9D,KAAA,GAAQ,KAAKqG,MAAA;IAEnB,OAAOvG,aAAA,CAAc,EAAE,KAAK0G,WAAA,EAAa;MACvChR,GAAA;MACAM,KAAA;MACAiK,YAAA;MACAC,KAAA;MACAC,OAAA,EAAS;QACPlJ,KAAA,EAAOA,CAAA,KAAM;UACX,IAAI,CAACgP,QAAA,CAAS,IAAI,GAAG;YACnBG,YAAA,CAAa,MAAM,IAAI;YACvB5J,UAAA,CAAW0D,KAAA,CAAMa,UAAU;YAC3BwJ,SAAA,CACE,MACA,WACArI,iBAAA,CAAkB,MAAMuI,aAAA,CAAc,MAAM,KAAKnE,SAAA,CAAUzP,EAAE,CAAC,GAC9D,IACF;UACF;QACF;QACA2J,MAAA,EAAQA,CAAA,KAAM;UACZ,IAAIyF,QAAA,CAAS,IAAI,GAAG;YAClBG,YAAA,CAAa,MAAM,KAAK;YACxB,IAAIJ,WAAA,CAAY,IAAI,GAAG;cACrB,KAAK0E,OAAA,CAAQ;YACf;YACAlO,UAAA,CAAW0D,KAAA,CAAMK,WAAW;YAC5BgK,SAAA,CACE,MACA,YACArI,iBAAA,CAAkB,MAAMuI,aAAA,CAAc,MAAM,KAAKnE,SAAA,CAAUzP,EAAE,CAAC,GAC9D,IACF;UACF;QACF;QACA6D,KAAA,EAAO,KAAKiQ,MAAA,CAAOhH,IAAA,CAAK,MAAMvK,KAAK;MACrC;IACF,CAAC,EAAE4B,IAAA,CAAK2G,MAAA,IAAU;MAChB,IAAI3L,KAAA,CAAMe,IAAA,IAAQ4K,MAAA,CAAOQ,QAAA,IAAY,EAAEiI,MAAA,IAAUzI,MAAA,CAAOK,IAAA,GAAO;QAC7D,MAAM4I,SAAA,GAAYC,gBAAA,CAAiB7U,KAAK;QACxC,IAAI4U,SAAA,EAAW;UACb,OAAO,KAAKhB,OAAA,CAAQgB,SAAA,EAAW,IAAI;QACrC;MACF;MACA,OAAOjJ,MAAA;IACT,CAAC;EACH;EAAA;EAGUgJ,OACRvR,KAAA,EACApD,KAAA,EACA6E,OAAA,EACM;IAGN,IAAI7E,KAAA,CAAMkB,MAAA,EAAQ;MAChB,KAAKwM,IAAA,CAAK,IAAI;MACd,OAAO7I,OAAA,CAAQgH,kBAAA,CAAmB,IAAI,CAAC;IACzC;IAGA,MAAMiJ,SAAA,GAAY,CAAC/O,GAAA,CAAGtF,GAAA,CAAI2C,KAAA,CAAMvC,EAAE;IAGlC,MAAMkU,WAAA,GAAc,CAAChP,GAAA,CAAGtF,GAAA,CAAI2C,KAAA,CAAMxC,IAAI;IAItC,IAAIkU,SAAA,IAAaC,WAAA,EAAa;MAC5B,IAAI/U,KAAA,CAAMuC,MAAA,GAAS,KAAKoO,SAAA,EAAW;QACjC,KAAKA,SAAA,GAAY3Q,KAAA,CAAMuC,MAAA;MACzB,OAAO;QACL,OAAOsC,OAAA,CAAQgH,kBAAA,CAAmB,IAAI,CAAC;MACzC;IACF;IAEA,MAAM;MAAEnM,GAAA;MAAKuK,YAAA;MAAcqG,SAAA,EAAWa;IAAK,IAAI;IAC/C,MAAM;MAAEtQ,EAAA,EAAIyL,MAAA;MAAQ1L,IAAA,EAAMoU;IAAS,IAAI7D,IAAA;IACvC,IAAI;MAAEtQ,EAAA,EAAAgC,GAAA,GAAKyJ,MAAA;MAAQ1L,IAAA,GAAOoU;IAAS,IAAI5R,KAAA;IAIvC,IAAI2R,WAAA,IAAe,CAACD,SAAA,KAAc,CAAC9U,KAAA,CAAMC,OAAA,IAAW8F,GAAA,CAAGtF,GAAA,CAAIoC,GAAE,IAAI;MAC/DA,GAAA,GAAKjC,IAAA;IACP;IAGA,IAAIZ,KAAA,CAAMmB,OAAA,EAAS,CAAC0B,GAAA,EAAIjC,IAAI,IAAI,CAACA,IAAA,EAAMiC,GAAE;IAGzC,MAAMoS,cAAA,GAAiB,CAAC9O,OAAA,CAAQvF,IAAA,EAAMoU,QAAQ;IAE9C,IAAIC,cAAA,EAAgB;MAClB9D,IAAA,CAAKvQ,IAAA,GAAOA,IAAA;IACd;IAGAA,IAAA,GAAO6F,cAAA,CAAc7F,IAAI;IAGzB,MAAMsU,YAAA,GAAe,CAAC/O,OAAA,CAAQtD,GAAA,EAAIyJ,MAAM;IAExC,IAAI4I,YAAA,EAAc;MAChB,KAAKxB,MAAA,CAAO7Q,GAAE;IAChB;IAGA,MAAMsS,UAAA,GAAa3R,SAAA,CAAUxD,KAAA,CAAMa,EAAE;IAErC,MAAM;MAAEF,MAAA,EAAAkI;IAAO,IAAIsI,IAAA;IACnB,MAAM;MAAE9H,KAAA;MAAOX;IAAS,IAAIG,OAAA;IAG5B,IAAIiM,SAAA,IAAaC,WAAA,EAAa;MAC5BlM,OAAA,CAAOH,QAAA,GAAW;IACpB;IAIA,IAAI1I,KAAA,CAAMW,MAAA,IAAU,CAACwU,UAAA,EAAY;MAC/BvM,WAAA,CACEC,OAAA,EACAxJ,QAAA,CAASW,KAAA,CAAMW,MAAA,EAAQjB,GAAI;MAAA;MAE3BM,KAAA,CAAMW,MAAA,KAAWsJ,YAAA,CAAatJ,MAAA,GAC1BtB,QAAA,CAAS4K,YAAA,CAAatJ,MAAA,EAAQjB,GAAI,IAClC,MACN;IACF;IAIA,IAAImP,IAAA,GAAOvH,YAAA,CAAY,IAAI;IAC3B,IAAI,CAACuH,IAAA,IAAQ9I,GAAA,CAAGtF,GAAA,CAAIoC,GAAE,GAAG;MACvB,OAAOgC,OAAA,CAAQqH,iBAAA,CAAkB,MAAM,IAAI,CAAC;IAC9C;IAGA,MAAMlL,KAAA;IAAA;IAAA;IAAA;IAIJ+E,GAAA,CAAGtF,GAAA,CAAIT,KAAA,CAAMgB,KAAK,IACd+T,WAAA,IAAe,CAAC/U,KAAA,CAAMC,OAAA,GACtB,CAAC8F,GAAA,CAAGtF,GAAA,CAAIG,IAAI,KAAKnB,SAAA,CAAUO,KAAA,CAAMgB,KAAA,EAAOtB,GAAG;IAGjD,MAAMJ,KAAA,GAAQ0B,KAAA,GAASJ,IAAA,GAAa,KAAKkL,GAAA,CAAI;IAG7C,MAAM+E,IAAA,GAAO7N,WAAA,CAAiBH,GAAE;IAGhC,MAAMuS,YAAA,GAAerP,GAAA,CAAGsP,GAAA,CAAIxE,IAAI,KAAK9K,GAAA,CAAG9C,GAAA,CAAI4N,IAAI,KAAKnK,iBAAA,CAAiBmK,IAAI;IAG1E,MAAMzP,SAAA,GACJ,CAAC+T,UAAA,KACA,CAACC,YAAA,IACA3V,SAAA,CAAUwK,YAAA,CAAa7I,SAAA,IAAapB,KAAA,CAAMoB,SAAA,EAAW1B,GAAG;IAE5D,IAAIwV,YAAA,EAAc;MAChB,MAAMI,QAAA,GAAW9N,eAAA,CAAgB3E,GAAE;MACnC,IAAIyS,QAAA,KAAazG,IAAA,CAAKpG,WAAA,EAAa;QACjC,IAAIrH,SAAA,EAAW;UACbyN,IAAA,GAAO,KAAK8E,IAAA,CAAK9C,IAAI;QACvB,OACE,MAAM7C,KAAA,CACJ,0BAA0Ba,IAAA,CAAKpG,WAAA,CAAY8M,IAAA,QAAYD,QAAA,CAASC,IAAA,6BAClE;MACJ;IACF;IAGA,MAAMC,QAAA,GAAW3G,IAAA,CAAKpG,WAAA;IAKtB,IAAIgN,OAAA,GAAU3O,aAAA,CAAcjE,GAAE;IAC9B,IAAIsJ,QAAA,GAAW;IAEf,IAAI,CAACsJ,OAAA,EAAS;MAEZ,MAAMC,eAAA,GAAkB1U,KAAA,IAAU,CAAC+O,WAAA,CAAY,IAAI,KAAKkF,cAAA;MAIxD,IAAIC,YAAA,IAAgBQ,eAAA,EAAiB;QACnCvJ,QAAA,GAAWhG,OAAA,CAAQnD,WAAA,CAAY1D,KAAK,GAAGuR,IAAI;QAC3C4E,OAAA,GAAU,CAACtJ,QAAA;MACb;MAGA,IACG,CAAChG,OAAA,CAAQgL,IAAA,CAAK/P,SAAA,EAAWA,SAAS,KAAK,CAACA,SAAA,IACzC,CAAC+E,OAAA,CAAQ0C,OAAA,CAAOQ,KAAA,EAAOA,KAAK,KAC5B,CAAClD,OAAA,CAAQ0C,OAAA,CAAOH,QAAA,EAAUA,QAAQ,GAClC;QACA+M,OAAA,GAAU;MACZ;IACF;IAGA,IAAItJ,QAAA,IAAY6D,WAAA,CAAY,IAAI,GAAG;MAGjC,IAAImB,IAAA,CAAKzH,OAAA,IAAW,CAAC1I,KAAA,EAAO;QAC1ByU,OAAA,GAAU;MACZ,WAES,CAACA,OAAA,EAAS;QACjB,KAAKjC,KAAA,CAAMlH,MAAM;MACnB;IACF;IAEA,IAAI,CAAC6I,UAAA,EAAY;MAGf,IAAIM,OAAA,IAAW3O,aAAA,CAAcwF,MAAM,GAAG;QACpC6E,IAAA,CAAKxH,MAAA,GAASkF,IAAA,CAAKzH,UAAA,CAAW;QAC9B+J,IAAA,CAAKvH,QAAA,GAAW9C,aAAA,CAAcjE,GAAE,IAC5B,OACA2S,QAAA,IAAYrO,cAAA,GACV,CAAC,CAAC,IACFf,QAAA,CAAQyK,IAAI;MACpB;MAEA,IAAIM,IAAA,CAAK/P,SAAA,IAAaA,SAAA,EAAW;QAC/B+P,IAAA,CAAK/P,SAAA,GAAYA,SAAA;QAGjB,IAAI,CAACA,SAAA,IAAa,CAACJ,KAAA,EAAO;UACxB,KAAK2S,IAAA,CAAKrH,MAAM;QAClB;MACF;MAEA,IAAImJ,OAAA,EAAS;QACX,MAAM;UAAE9T;QAAO,IAAIwP,IAAA;QAGnBjL,KAAA,CAAKyP,aAAA,EAAepG,IAAA,IAAQ+E,aAAA,CAAc,MAAMtU,KAAA,EAAOuP,IAAI,CAAC;QAE5D,MAAM5D,MAAA,GAASO,iBAAA,CAAkB,MAAMuI,aAAA,CAAc,MAAMnI,MAAM,CAAC;QAClE9F,UAAA,CAAW,KAAKiK,aAAA,EAAe9E,MAAM;QACrC,KAAK8E,aAAA,CAAc5M,GAAA,CAAIgB,OAAO;QAE9B,IAAIsM,IAAA,CAAKzH,OAAA,EACPzD,IAAA,CAAI2H,cAAA,CAAe,MAAM;UAEvBuD,IAAA,CAAKzH,OAAA,GAAU,CAAC1I,KAAA;UAGhBW,MAAA,GAASgK,MAAA,EAAQ,IAAI;UAIrB,IAAI3K,KAAA,EAAO;YACT3B,QAAA,CAAS4K,YAAA,CAAatI,MAAA,EAAQgK,MAAM;UACtC,OAIK;YACHwF,IAAA,CAAK5P,OAAA,GAAUoK,MAAA,EAAQ,IAAI;UAC7B;QACF,CAAC;MACL;IACF;IAEA,IAAI3K,KAAA,EAAO;MACT,KAAK2S,IAAA,CAAKrU,KAAK;IACjB;IAEA,IAAI6V,UAAA,EAAY;MACdtQ,OAAA,CAAQuH,QAAA,CAASpM,KAAA,CAAMa,EAAA,EAAIb,KAAA,EAAO,KAAKuQ,MAAA,EAAQ,IAAI,CAAC;IACtD,WAGSkF,OAAA,EAAS;MAChB,KAAKvB,MAAA,CAAO;IACd,WAISlE,WAAA,CAAY,IAAI,KAAK,CAACkF,YAAA,EAAc;MAC3C,KAAKzE,aAAA,CAAc5M,GAAA,CAAIgB,OAAO;IAChC,OAGK;MACHA,OAAA,CAAQoH,aAAA,CAAc3M,KAAK,CAAC;IAC9B;EACF;EAAA;EAGUoU,OAAOpU,KAAA,EAA0B;IACzC,MAAM6R,IAAA,GAAO,KAAKb,SAAA;IAClB,IAAIhR,KAAA,KAAU6R,IAAA,CAAKtQ,EAAA,EAAI;MACrB,IAAIoG,iBAAA,CAAkB,IAAI,GAAG;QAC3B,KAAKmI,OAAA,CAAQ;MACf;MACA+B,IAAA,CAAKtQ,EAAA,GAAKvB,KAAA;MACV,IAAI2H,iBAAA,CAAkB,IAAI,GAAG;QAC3B,KAAKiI,OAAA,CAAQ;MACf;IACF;EACF;EAEUA,QAAA,EAAU;IAClB,IAAIP,QAAA,GAAW;IAEf,MAAM;MAAE9N,EAAA,EAAAgC;IAAG,IAAI,KAAKyN,SAAA;IACpB,IAAIxJ,aAAA,CAAcjE,GAAE,GAAG;MACrBkE,gBAAA,CAAiBlE,GAAA,EAAI,IAAI;MACzB,IAAIwL,YAAA,CAAaxL,GAAE,GAAG;QACpB8L,QAAA,GAAW9L,GAAA,CAAG8L,QAAA,GAAW;MAC3B;IACF;IAEA,KAAKA,QAAA,GAAWA,QAAA;EAClB;EAEUS,QAAA,EAAU;IAClB,MAAM;MAAEvO,EAAA,EAAAgC;IAAG,IAAI,KAAKyN,SAAA;IACpB,IAAIxJ,aAAA,CAAcjE,GAAE,GAAG;MACrBmE,mBAAA,CAAoBnE,GAAA,EAAI,IAAI;IAC9B;EACF;EAAA;AAAA;AAAA;AAAA;EAMU8Q,KAAKiC,GAAA,EAAwBtG,IAAA,GAAO,MAA4B;IACxE,MAAMhQ,KAAA,GAAQmH,cAAA,CAAcmP,GAAG;IAC/B,IAAI,CAAC7P,GAAA,CAAGtF,GAAA,CAAInB,KAAK,GAAG;MAClB,MAAMuW,OAAA,GAAUvO,YAAA,CAAY,IAAI;MAChC,IAAI,CAACuO,OAAA,IAAW,CAAC1P,OAAA,CAAQ7G,KAAA,EAAOuW,OAAA,CAAQ/G,QAAA,CAAS,CAAC,GAAG;QAEnD,MAAMwG,QAAA,GAAW9N,eAAA,CAAgBlI,KAAK;QACtC,IAAI,CAACuW,OAAA,IAAWA,OAAA,CAAQpN,WAAA,IAAe6M,QAAA,EAAU;UAC/C/N,WAAA,CAAY,MAAM+N,QAAA,CAASQ,MAAA,CAAOxW,KAAK,CAAC;QAC1C,OAAO;UACLuW,OAAA,CAAQzC,QAAA,CAAS9T,KAAK;QACxB;QAEA,IAAIuW,OAAA,EAAS;UACX5P,IAAA,CAAI2H,cAAA,CAAe,MAAM;YACvB,KAAKyB,SAAA,CAAU/P,KAAA,EAAOgQ,IAAI;UAC5B,CAAC;QACH;MACF;IACF;IACA,OAAOhI,YAAA,CAAY,IAAI;EACzB;EAEUwM,SAAA,EAAW;IACnB,MAAM3C,IAAA,GAAO,KAAKb,SAAA;IAClB,IAAI,CAACa,IAAA,CAAKzH,OAAA,EAAS;MACjByH,IAAA,CAAKzH,OAAA,GAAU;MACf6K,SAAA,CACE,MACA,WACArI,iBAAA,CAAkB,MAAMuI,aAAA,CAAc,MAAMtD,IAAA,CAAKtQ,EAAE,CAAC,GACpD,IACF;IACF;EACF;EAEUwO,UAAU/P,KAAA,EAAUgQ,IAAA,EAAgB;IAC5C,IAAI,CAACA,IAAA,EAAM;MACT,KAAKwE,QAAA,CAAS;MACdzU,QAAA,CAAS,KAAKiR,SAAA,CAAU9O,QAAA,EAAUlC,KAAA,EAAO,IAAI;IAC/C;IACAD,QAAA,CAAS,KAAK4K,YAAA,CAAazI,QAAA,EAAUlC,KAAA,EAAO,IAAI;IAChD,MAAM+P,SAAA,CAAU/P,KAAA,EAAOgQ,IAAI;EAC7B;EAAA;EAAA;EAAA;EAKU4E,OAAA,EAAS;IACjB,MAAM/C,IAAA,GAAO,KAAKb,SAAA;IAGlBhJ,YAAA,CAAY,IAAI,EAAGtG,KAAA,CAAMyF,cAAA,CAAc0K,IAAA,CAAKtQ,EAAE,CAAC;IAG/C,IAAI,CAACsQ,IAAA,CAAK/P,SAAA,EAAW;MACnB+P,IAAA,CAAKtH,UAAA,GAAasH,IAAA,CAAKxH,MAAA,CAAOzG,GAAA,CAAI2L,IAAA,IAAQA,IAAA,CAAK0C,YAAY;IAC7D;IAEA,IAAI,CAACvB,WAAA,CAAY,IAAI,GAAG;MACtBE,YAAA,CAAa,MAAM,IAAI;MACvB,IAAI,CAACD,QAAA,CAAS,IAAI,GAAG;QACnB,KAAKyE,OAAA,CAAQ;MACf;IACF;EACF;EAEUA,QAAA,EAAU;IAElB,IAAI/N,EAAA,CAAEiE,aAAA,EAAe;MACnB,KAAKiJ,MAAA,CAAO;IACd,OAAO;MACLtN,UAAA,CAAU7B,KAAA,CAAM,IAAI;IACtB;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOU8O,MAAM3C,IAAA,EAAY3P,MAAA,EAAkB;IAC5C,IAAI8O,WAAA,CAAY,IAAI,GAAG;MACrBE,YAAA,CAAa,MAAM,KAAK;MAExB,MAAMiB,IAAA,GAAO,KAAKb,SAAA;MAClBpK,KAAA,CAAKiL,IAAA,CAAKxH,MAAA,EAAQkF,IAAA,IAAQ;QACxBA,IAAA,CAAKyC,IAAA,GAAO;MACd,CAAC;MAKD,IAAIH,IAAA,CAAKvH,QAAA,EAAU;QACjBuH,IAAA,CAAK3P,QAAA,GAAW2P,IAAA,CAAK1P,OAAA,GAAU0P,IAAA,CAAKzP,QAAA,GAAW;MACjD;MAEAmF,mBAAA,CAAmB,MAAM;QACvB0I,IAAA,EAAM;QACNC,MAAA,EAAQ;MACV,CAAC;MAED,MAAM7D,MAAA,GAASzK,MAAA,GACX2K,kBAAA,CAAmB,KAAKC,GAAA,CAAI,CAAC,IAC7BI,iBAAA,CAAkB,KAAKJ,GAAA,CAAI,GAAG2I,aAAA,CAAc,MAAM5D,IAAA,IAAQM,IAAA,CAAKtQ,EAAE,CAAC;MAEtE2F,UAAA,CAAW,KAAKiK,aAAA,EAAe9E,MAAM;MACrC,IAAIwF,IAAA,CAAKzH,OAAA,EAAS;QAChByH,IAAA,CAAKzH,OAAA,GAAU;QACf6K,SAAA,CAAU,MAAM,UAAU5I,MAAA,EAAQ,IAAI;MACxC;IACF;EACF;AACF;AAGA,SAAS8I,cAAiBjJ,MAAA,EAAwB3I,GAAA,EAAuB;EACvE,MAAMgO,IAAA,GAAO7N,WAAA,CAAYH,GAAE;EAC3B,MAAMvD,KAAA,GAAQ0D,WAAA,CAAYwI,MAAA,CAAOM,GAAA,CAAI,CAAC;EACtC,OAAO3F,OAAA,CAAQ7G,KAAA,EAAOuR,IAAI;AAC5B;AAEO,SAASgE,iBACd7U,KAAA,EACAe,IAAA,GAAOf,KAAA,CAAMe,IAAA,EACb8B,GAAA,GAAK7C,KAAA,CAAMa,EAAA,EACI;EACf,MAAMkV,OAAA,GAAU1W,QAAA,CAAS0B,IAAI;EAC7B,IAAIgV,OAAA,EAAS;IACX,MAAMC,SAAA,GAAYD,OAAA,KAAY,QAAQnT,OAAA,CAAQmT,OAAO;IACrD,MAAM5U,OAAA,IAAW6U,SAAA,IAAahW,KAAA,EAAOmB,OAAA;IACrC,MAAMH,KAAA,GAAQ,CAACgV,SAAA,IAAaA,SAAA,CAAUhV,KAAA;IACtC,OAAOiV,YAAA,CAAa;MAClB,GAAGjW,KAAA;MACHe,IAAA;MAAA;MAGAd,OAAA,EAAS;MAAA;MAGTgB,KAAA,EAAO;MAAA;MAAA;MAAA;MAKPJ,EAAA,EAAI,CAACM,OAAA,IAAWqC,SAAA,CAAUX,GAAE,IAAIA,GAAA,GAAK;MAAA;MAGrCjC,IAAA,EAAMI,KAAA,GAAQhB,KAAA,CAAMY,IAAA,GAAO;MAC3BI,KAAA;MAAA;MAAA;MAIA,GAAGgV;IACL,CAAC;EACH;AACF;AASO,SAASC,aAAajW,KAAA,EAAY;EACvC,MAAM;IAAEa,EAAA,EAAAgC,GAAA;IAAIjC;EAAK,IAAKZ,KAAA,GAAQ4C,OAAA,CAAQ5C,KAAK;EAG3C,MAAMK,IAAA,GAAO,mBAAImQ,GAAA,CAAY;EAE7B,IAAIzK,GAAA,CAAGjG,GAAA,CAAI+C,GAAE,GAAGqT,WAAA,CAAYrT,GAAA,EAAIxC,IAAI;EACpC,IAAI0F,GAAA,CAAGjG,GAAA,CAAIc,IAAI,GAAGsV,WAAA,CAAYtV,IAAA,EAAMP,IAAI;EAGxCL,KAAA,CAAMK,IAAA,GAAOA,IAAA,CAAK8V,IAAA,GAAOC,KAAA,CAAMxV,IAAA,CAAKP,IAAI,IAAI;EAE5C,OAAOL,KAAA;AACT;AAKO,SAASqW,cAAcrW,KAAA,EAAY;EACxC,MAAMiF,OAAA,GAASgR,YAAA,CAAajW,KAAK;EACjC,IAAI+F,GAAA,CAAGtF,GAAA,CAAIwE,OAAA,CAAOhF,OAAO,GAAG;IAC1BgF,OAAA,CAAOhF,OAAA,GAAUE,eAAA,CAAgB8E,OAAM;EACzC;EACA,OAAOA,OAAA;AACT;AAGA,SAASiR,YAAYvM,MAAA,EAAgBtJ,IAAA,EAAmB;EACtDgG,SAAA,CAASsD,MAAA,EAAQ,CAACrK,KAAA,EAAOI,GAAA,KAAQJ,KAAA,IAAS,QAAQe,IAAA,CAAKwD,GAAA,CAAInE,GAAU,CAAC;AACxE;AAGA,IAAMiW,aAAA,GAAgB,CACpB,WACA,UACA,YACA,WACA,WACF;AAEA,SAASrB,cACP9I,MAAA,EACAxL,KAAA,EACAuP,IAAA,EACA;EACA/D,MAAA,CAAO8E,SAAA,CAAUf,IAAI,IACnBvP,KAAA,CAAMuP,IAAI,MAAMxP,cAAA,CAAeC,KAAA,EAAOuP,IAAI,IACtC3P,WAAA,CAAiBI,KAAA,CAAMuP,IAAI,GAAG/D,MAAA,CAAO9L,GAAG,IACxC;AACR;AAOA,SAAS6U,UACP/I,MAAA,EACA+D,IAAA,KACGhQ,IAAA,EACH;EACAiM,MAAA,CAAO8E,SAAA,CAAUf,IAAI,IAAI,GAAIhQ,IAAmB;EAChDiM,MAAA,CAAOvB,YAAA,CAAasF,IAAI,IAAI,GAAIhQ,IAAmB;AACrD;;;ASnnCA,SACET,EAAA,IAAAwX,GAAA,EACAtQ,GAAA,IAAAuQ,IAAA,EACA3X,IAAA,IAAA4X,KAAA,EACAxK,IAAA,EACAZ,KAAA,IAAAqL,MAAA,EACA1X,OAAA,IAAA2X,QAAA,EACA1X,QAAA,IAAA2X,SAAA,EACAnQ,UAAA,IAAAoQ,WAAA,EACA7P,gBAAA,IAAA8P,iBAAA,QAEK;AAuBP,IAAMC,cAAA,GAAiB,CAAC,WAAW,YAAY,QAAQ;AAEvD,IAAIC,OAAA,GAAS;AAWN,IAAMC,UAAA,GAAN,MAAgD;EA2DrDvO,YACEzI,KAAA,EACAiX,MAAA,EACA;IA7DF,KAASxI,EAAA,GAAKsI,OAAA;IAGd;IAAA,KAAAG,OAAA,GAA+B,CAAC;IAGhC;IAAA,KAAA1S,KAAA,GAAgC,EAAC;IAejC;IAAA,KAAU2S,YAAA,GAAe;IAGzB;IAAA,KAAUC,OAAA,GAAU,mBAAI5G,GAAA,CAAgB;IAGxC;IAAA,KAAU6G,QAAA,GAAW,mBAAI7G,GAAA,CAAgB;IAGzC;IAAA,KAAU8G,QAAA,GAAW;IAKrB;IAAA,KAAU/G,MAAA,GAA8B;MACtCjG,MAAA,EAAQ;MACRS,UAAA,EAAY,mBAAIyF,GAAA,CAAI;MACpBjG,WAAA,EAAa,mBAAIiG,GAAA,CAAI;MACrB/F,QAAA,EAAU,mBAAI+F,GAAA,CAAI;IACpB;IAGA;IAAA,KAAU+G,OAAA,GAAU;MAClBhW,OAAA,EAAS,mBAAIiW,GAAA,CAGX;MACFhW,QAAA,EAAU,mBAAIgW,GAAA,CAGZ;MACF7V,MAAA,EAAQ,mBAAI6V,GAAA,CAGV;IACJ;IAME,KAAKC,QAAA,GAAW,KAAKA,QAAA,CAAS9J,IAAA,CAAK,IAAI;IACvC,IAAIsJ,MAAA,EAAO;MACT,KAAKS,MAAA,GAAST,MAAA;IAChB;IACA,IAAIjX,KAAA,EAAO;MACT,KAAK0E,KAAA,CAAM;QAAEzE,OAAA,EAAS;QAAM,GAAGD;MAAM,CAAC;IACxC;EACF;EAAA;AAAA;AAAA;AAAA;EAMA,IAAIsP,KAAA,EAAO;IACT,OACE,CAAC,KAAKiB,MAAA,CAAOlE,OAAA,IACb9L,MAAA,CAAOoJ,MAAA,CAAO,KAAKuN,OAA8B,EAAEnL,KAAA,CAAM4L,MAAA,IAAU;MACjE,OAAOA,MAAA,CAAOrI,IAAA,IAAQ,CAACqI,MAAA,CAAO3G,SAAA,IAAa,CAAC2G,MAAA,CAAO1H,QAAA;IACrD,CAAC;EAEL;EAEA,IAAIpC,KAAA,EAAO;IACT,OAAO,KAAK+J,KAAA;EACd;EAEA,IAAI/J,KAAKA,IAAA,EAAM;IACb,KAAK+J,KAAA,GAAQ/J,IAAA;EACf;EAAA;EAGA/B,IAAA,EAA4B;IAC1B,MAAMnC,MAAA,GAAc,CAAC;IACrB,KAAK/K,IAAA,CAAK,CAAC+Y,MAAA,EAAQjY,GAAA,KAASiK,MAAA,CAAOjK,GAAG,IAAIiY,MAAA,CAAO7L,GAAA,CAAI,CAAE;IACvD,OAAOnC,MAAA;EACT;EAAA;EAGA8J,IAAI9J,MAAA,EAAwB;IAC1B,WAAWjK,GAAA,IAAOiK,MAAA,EAAQ;MACxB,MAAMrK,KAAA,GAAQqK,MAAA,CAAOjK,GAAG;MACxB,IAAI,CAAC4W,GAAA,CAAG7V,GAAA,CAAInB,KAAK,GAAG;QAClB,KAAK4X,OAAA,CAAQxX,GAAG,EAAE+T,GAAA,CAAInU,KAAK;MAC7B;IACF;EACF;EAAA;EAGA6C,OAAOnC,KAAA,EAAwC;IAC7C,IAAIA,KAAA,EAAO;MACT,KAAKwE,KAAA,CAAMU,IAAA,CAAK+Q,YAAA,CAAajW,KAAK,CAAC;IACrC;IACA,OAAO;EACT;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASA0E,MAAM1E,KAAA,EAAsE;IAC1E,IAAI;MAAEwE;IAAM,IAAI;IAChB,IAAIxE,KAAA,EAAO;MACTwE,KAAA,GAAQkS,QAAA,CAAa1W,KAAK,EAAEkD,GAAA,CAAI+S,YAAY;IAC9C,OAAO;MACL,KAAKzR,KAAA,GAAQ,EAAC;IAChB;IAEA,IAAI,KAAKkT,MAAA,EAAQ;MACf,OAAO,KAAKA,MAAA,CAAO,MAAMlT,KAAK;IAChC;IAEAqT,WAAA,CAAY,MAAMrT,KAAK;IACvB,OAAOsT,gBAAA,CAAiB,MAAMtT,KAAK;EACrC;EAAA;EAeAkJ,KAAKkI,GAAA,EAAmCvV,IAAA,EAA0B;IAChE,IAAIuV,GAAA,KAAQ,CAAC,CAACA,GAAA,EAAK;MACjBvV,IAAA,GAAOuV,GAAA;IACT;IACA,IAAIvV,IAAA,EAAM;MACR,MAAM6W,OAAA,GAAU,KAAKA,OAAA;MACrBV,KAAA,CAAKE,QAAA,CAAQrW,IAAI,GAAeX,GAAA,IAAOwX,OAAA,CAAQxX,GAAG,EAAEgO,IAAA,CAAK,CAAC,CAACkI,GAAG,CAAC;IACjE,OAAO;MACLtI,SAAA,CAAU,KAAKiD,MAAA,EAAQ,KAAK4G,YAAY;MACxC,KAAKvY,IAAA,CAAK+Y,MAAA,IAAUA,MAAA,CAAOjK,IAAA,CAAK,CAAC,CAACkI,GAAG,CAAC;IACxC;IACA,OAAO;EACT;EAAA;EAGA3U,MAAMZ,IAAA,EAA0B;IAC9B,IAAIiW,GAAA,CAAG7V,GAAA,CAAIJ,IAAI,GAAG;MAChB,KAAKqE,KAAA,CAAM;QAAEzD,KAAA,EAAO;MAAK,CAAC;IAC5B,OAAO;MACL,MAAMiW,OAAA,GAAU,KAAKA,OAAA;MACrBV,KAAA,CAAKE,QAAA,CAAQrW,IAAI,GAAeX,GAAA,IAAOwX,OAAA,CAAQxX,GAAG,EAAEuB,KAAA,CAAM,CAAC;IAC7D;IACA,OAAO;EACT;EAAA;EAGAuJ,OAAOnK,IAAA,EAA0B;IAC/B,IAAIiW,GAAA,CAAG7V,GAAA,CAAIJ,IAAI,GAAG;MAChB,KAAKqE,KAAA,CAAM;QAAEzD,KAAA,EAAO;MAAM,CAAC;IAC7B,OAAO;MACL,MAAMiW,OAAA,GAAU,KAAKA,OAAA;MACrBV,KAAA,CAAKE,QAAA,CAAQrW,IAAI,GAAeX,GAAA,IAAOwX,OAAA,CAAQxX,GAAG,EAAE8K,MAAA,CAAO,CAAC;IAC9D;IACA,OAAO;EACT;EAAA;EAGA5L,KAAKmZ,QAAA,EAAsD;IACzDpB,SAAA,CAAS,KAAKO,OAAA,EAASa,QAAe;EACxC;EAAA;EAGUN,SAAA,EAAW;IACnB,MAAM;MAAElW,OAAA;MAASC,QAAA;MAAUG;IAAO,IAAI,KAAK4V,OAAA;IAE3C,MAAMpH,MAAA,GAAS,KAAKiH,OAAA,CAAQjB,IAAA,GAAO;IACnC,MAAMzM,OAAA,GAAU,KAAK2N,QAAA,CAASlB,IAAA,GAAO;IAErC,IAAKhG,MAAA,IAAU,CAAC,KAAKmH,QAAA,IAAc5N,OAAA,IAAW,CAAC,KAAK4N,QAAA,EAAW;MAC7D,KAAKA,QAAA,GAAW;MAChBb,MAAA,CAAMlV,OAAA,EAAS,CAAC,CAACyW,QAAA,EAASrM,MAAM,MAAM;QACpCA,MAAA,CAAOrM,KAAA,GAAQ,KAAKwM,GAAA,CAAI;QACxBkM,QAAA,CAAQrM,MAAA,EAAQ,MAAM,KAAKiM,KAAK;MAClC,CAAC;IACH;IAEA,MAAMtI,IAAA,GAAO,CAACa,MAAA,IAAU,KAAKmH,QAAA;IAC7B,MAAM3N,MAAA,GAASD,OAAA,IAAY4F,IAAA,IAAQ3N,MAAA,CAAOwU,IAAA,GAAQ,KAAKrK,GAAA,CAAI,IAAI;IAE/D,IAAIpC,OAAA,IAAWlI,QAAA,CAAS2U,IAAA,EAAM;MAC5BM,MAAA,CAAMjV,QAAA,EAAU,CAAC,CAACyW,SAAA,EAAUtM,MAAM,MAAM;QACtCA,MAAA,CAAOrM,KAAA,GAAQqK,MAAA;QACfsO,SAAA,CAAStM,MAAA,EAAQ,MAAM,KAAKiM,KAAK;MACnC,CAAC;IACH;IAGA,IAAItI,IAAA,EAAM;MACR,KAAKgI,QAAA,GAAW;MAChBb,MAAA,CAAM9U,MAAA,EAAQ,CAAC,CAACuW,OAAA,EAAQvM,MAAM,MAAM;QAClCA,MAAA,CAAOrM,KAAA,GAAQqK,MAAA;QACfuO,OAAA,CAAOvM,MAAA,EAAQ,MAAM,KAAKiM,KAAK;MACjC,CAAC;IACH;EACF;EAAA;EAGA5D,cAAcC,KAAA,EAAyB;IACrC,IAAIA,KAAA,CAAM1E,IAAA,IAAQ,UAAU;MAC1B,KAAK8H,QAAA,CAASxT,GAAA,CAAIoQ,KAAA,CAAMzE,MAAM;MAC9B,IAAI,CAACyE,KAAA,CAAM3E,IAAA,EAAM;QACf,KAAK8H,OAAA,CAAQvT,GAAA,CAAIoQ,KAAA,CAAMzE,MAAM;MAC/B;IACF,WAAWyE,KAAA,CAAM1E,IAAA,IAAQ,QAAQ;MAC/B,KAAK6H,OAAA,CAAQzT,MAAA,CAAOsQ,KAAA,CAAMzE,MAAM;IAClC,OAEK;IACL+G,IAAA,CAAI4B,OAAA,CAAQ,KAAKV,QAAQ;EAC3B;AACF;AAKO,SAASK,iBACdpU,IAAA,EACAc,KAAA,EACA;EACA,OAAOI,OAAA,CAAQO,GAAA,CAAIX,KAAA,CAAMtB,GAAA,CAAIlD,KAAA,IAASoY,WAAA,CAAY1U,IAAA,EAAM1D,KAAK,CAAC,CAAC,EAAEgF,IAAA,CAC/DyG,OAAA,IAAWF,iBAAA,CAAkB7H,IAAA,EAAM+H,OAAO,CAC5C;AACF;AAWA,eAAsB2M,YACpB1U,IAAA,EACA1D,KAAA,EACAoU,MAAA,EACa;EACb,MAAM;IAAE/T,IAAA;IAAMQ,EAAA,EAAAgC,GAAA;IAAIjC,IAAA;IAAMG,IAAA;IAAMY,MAAA;IAAQC;EAAU,IAAI5B,KAAA;EACpD,MAAMQ,SAAA,GAAW8V,GAAA,CAAGxW,GAAA,CAAIE,KAAA,CAAMC,OAAO,KAAKD,KAAA,CAAMC,OAAA;EAIhD,IAAIc,IAAA,EAAM;IACRf,KAAA,CAAMe,IAAA,GAAO;EACf;EAGA,IAAI8B,GAAA,KAAO,OAAO7C,KAAA,CAAMa,EAAA,GAAK;EAC7B,IAAID,IAAA,KAAS,OAAOZ,KAAA,CAAMY,IAAA,GAAO;EAEjC,MAAMyL,OAAA,GAAUiK,GAAA,CAAGrT,GAAA,CAAIJ,GAAE,KAAKyT,GAAA,CAAG9W,GAAA,CAAIqD,GAAE,IAAIA,GAAA,GAAK;EAChD,IAAIwJ,OAAA,EAAS;IACXrM,KAAA,CAAMa,EAAA,GAAK;IACXb,KAAA,CAAM2B,MAAA,GAAS;IACf,IAAInB,SAAA,EAAU;MACZA,SAAA,CAASmB,MAAA,GAAS;IACpB;EACF,OAIK;IACH6U,KAAA,CAAKM,cAAA,EAAgBpX,GAAA,IAAO;MAC1B,MAAM2Y,OAAA,GAAerY,KAAA,CAAMN,GAAG;MAC9B,IAAI4W,GAAA,CAAG9W,GAAA,CAAI6Y,OAAO,GAAG;QACnB,MAAM7T,KAAA,GAAQd,IAAA,CAAK,SAAS,EAAEhE,GAAG;QACjCM,KAAA,CAAMN,GAAG,IAAK,CAAC;UAAEyM,QAAA;UAAUP;QAAU,MAAuB;UAC1D,MAAM4B,OAAA,GAAShJ,KAAA,CAAMsH,GAAA,CAAIuM,OAAO;UAChC,IAAI7K,OAAA,EAAQ;YACV,IAAI,CAACrB,QAAA,EAAUqB,OAAA,CAAOrB,QAAA,GAAW;YACjC,IAAIP,SAAA,EAAW4B,OAAA,CAAO5B,SAAA,GAAY;UACpC,OAAO;YAELpH,KAAA,CAAMiP,GAAA,CAAI4E,OAAA,EAAS;cACjB/Y,KAAA,EAAO;cACP6M,QAAA,EAAUA,QAAA,IAAY;cACtBP,SAAA,EAAWA,SAAA,IAAa;YAC1B,CAAC;UACH;QACF;QAGA,IAAIpL,SAAA,EAAU;UACZA,SAAA,CAASd,GAAG,IAAIM,KAAA,CAAMN,GAAG;QAC3B;MACF;IACF,CAAC;EACH;EAEA,MAAMwK,KAAA,GAAQxG,IAAA,CAAK,QAAQ;EAG3B,IAAI1D,KAAA,CAAMiB,KAAA,KAAU,CAACiJ,KAAA,CAAMI,MAAA,EAAQ;IACjCJ,KAAA,CAAMI,MAAA,GAAStK,KAAA,CAAMiB,KAAA;IACrB2V,WAAA,CAAW5W,KAAA,CAAMiB,KAAA,GAAQiJ,KAAA,CAAMa,UAAA,GAAab,KAAA,CAAMK,WAAW;EAC/D,WAESL,KAAA,CAAMI,MAAA,EAAQ;IACrBtK,KAAA,CAAMiB,KAAA,GAAQ;EAChB;EAEA,MAAMqX,QAAA,IAA2BjY,IAAA,IAAQE,MAAA,CAAOF,IAAA,CAAKqD,IAAA,CAAKwT,OAAO,GAAGhU,GAAA,CAAIxD,GAAA,IACtEgE,IAAA,CAAKwT,OAAA,CAAQxX,GAAG,EAAGgF,KAAA,CAAM1E,KAAY,CACvC;EAEA,MAAMkB,MAAA,GACJlB,KAAA,CAAMkB,MAAA,KAAW,QAAQnB,cAAA,CAAeC,KAAA,EAAO,QAAQ,MAAM;EAE/D,IAAIqM,OAAA,IAAYnL,MAAA,IAAUgJ,KAAA,CAAMuC,OAAA,EAAU;IACxC6L,QAAA,CAASpT,IAAA,CACP8E,aAAA,CAAc,EAAEtG,IAAA,CAAK,cAAc,GAAG;MACpC1D,KAAA;MACAkK,KAAA;MACAC,OAAA,EAAS;QACPlJ,KAAA,EAAO+K,IAAA;QACPxB,MAAA,EAAQwB,IAAA;QACRtH,MAAM6I,MAAA,EAAO1I,OAAA,EAAS;UACpB,IAAI3D,MAAA,EAAQ;YACVoM,SAAA,CAAUpD,KAAA,EAAOxG,IAAA,CAAK,cAAc,CAAC;YACrCmB,OAAA,CAAQgH,kBAAA,CAAmBnI,IAAI,CAAC;UAClC,OAAO;YACL6J,MAAA,CAAM5L,MAAA,GAASA,MAAA;YACfkD,OAAA,CACEuH,QAAA,CACEC,OAAA,EACAkB,MAAA,EACArD,KAAA,EACAxG,IACF,CACF;UACF;QACF;MACF;IACF,CAAC,CACH;EACF;EAIA,IAAIwG,KAAA,CAAMI,MAAA,EAAQ;IAGhB,MAAM,IAAI1F,OAAA,CAAc4F,MAAA,IAAU;MAChCN,KAAA,CAAMK,WAAA,CAAY1G,GAAA,CAAI2G,MAAM;IAC9B,CAAC;EACH;EAEA,MAAMmB,MAAA,GAASJ,iBAAA,CAAuB7H,IAAA,EAAM,MAAMkB,OAAA,CAAQO,GAAA,CAAImT,QAAQ,CAAC;EACvE,IAAIvX,IAAA,IAAQ4K,MAAA,CAAOQ,QAAA,IAAY,EAAEiI,MAAA,IAAUzI,MAAA,CAAOK,IAAA,GAAO;IACvD,MAAM4I,SAAA,GAAYC,gBAAA,CAAiB7U,KAAA,EAAOe,IAAA,EAAM8B,GAAE;IAClD,IAAI+R,SAAA,EAAW;MACbiD,WAAA,CAAYnU,IAAA,EAAM,CAACkR,SAAS,CAAC;MAC7B,OAAOwD,WAAA,CAAY1U,IAAA,EAAMkR,SAAA,EAAW,IAAI;IAC1C;EACF;EACA,IAAIhT,SAAA,EAAW;IACb2U,IAAA,CAAI3I,cAAA,CAAe,MAAMhM,SAAA,CAAU+J,MAAA,EAAQjI,IAAA,EAAMA,IAAA,CAAKmK,IAAI,CAAC;EAC7D;EACA,OAAOlC,MAAA;AACT;AAUO,SAAS4M,WACd7U,IAAA,EACA1D,KAAA,EACA;EACA,MAAMkX,OAAA,GAAU;IAAE,GAAGxT,IAAA,CAAKwT;EAAQ;EAClC,IAAIlX,KAAA,EAAO;IACTwW,KAAA,CAAKE,QAAA,CAAQ1W,KAAK,GAAIuN,MAAA,IAAe;MACnC,IAAI+I,GAAA,CAAG7V,GAAA,CAAI8M,MAAA,CAAMlN,IAAI,GAAG;QACtBkN,MAAA,GAAQ0I,YAAA,CAAa1I,MAAK;MAC5B;MACA,IAAI,CAAC+I,GAAA,CAAGxW,GAAA,CAAIyN,MAAA,CAAM1M,EAAE,GAAG;QAErB0M,MAAA,GAAQ;UAAE,GAAGA,MAAA;UAAO1M,EAAA,EAAI;QAAU;MACpC;MACA2X,cAAA,CAAetB,OAAA,EAAgB3J,MAAA,EAAO7N,GAAA,IAAO;QAC3C,OAAO+Y,YAAA,CAAa/Y,GAAG;MACzB,CAAC;IACH,CAAC;EACH;EACAgZ,UAAA,CAAWhV,IAAA,EAAMwT,OAAO;EACxB,OAAOA,OAAA;AACT;AAMO,SAASwB,WACdhV,IAAA,EACAwT,OAAA,EACA;EACAP,SAAA,CAASO,OAAA,EAAS,CAACS,MAAA,EAAQjY,GAAA,KAAQ;IACjC,IAAI,CAACgE,IAAA,CAAKwT,OAAA,CAAQxX,GAAG,GAAG;MACtBgE,IAAA,CAAKwT,OAAA,CAAQxX,GAAG,IAAIiY,MAAA;MACpBd,iBAAA,CAAiBc,MAAA,EAAQjU,IAAI;IAC/B;EACF,CAAC;AACH;AAEA,SAAS+U,aAAa/Y,GAAA,EAAaiZ,QAAA,EAA4C;EAC7E,MAAMhB,MAAA,GAAS,IAAItH,WAAA,CAAY;EAC/BsH,MAAA,CAAOjY,GAAA,GAAMA,GAAA;EACb,IAAIiZ,QAAA,EAAU;IACZ9B,iBAAA,CAAiBc,MAAA,EAAQgB,QAAQ;EACnC;EACA,OAAOhB,MAAA;AACT;AAQA,SAASa,eACPtB,OAAA,EACAlX,KAAA,EACA8V,MAAA,EACA;EACA,IAAI9V,KAAA,CAAMK,IAAA,EAAM;IACdmW,KAAA,CAAKxW,KAAA,CAAMK,IAAA,EAAMX,GAAA,IAAO;MACtB,MAAMiY,MAAA,GAAST,OAAA,CAAQxX,GAAG,MAAMwX,OAAA,CAAQxX,GAAG,IAAIoW,MAAA,CAAOpW,GAAG;MACzDiY,MAAA,CAAO,cAAc,EAAE3X,KAAK;IAC9B,CAAC;EACH;AACF;AAQA,SAAS6X,YAAYnU,IAAA,EAAuBc,KAAA,EAAkC;EAC5EgS,KAAA,CAAKhS,KAAA,EAAOxE,KAAA,IAAS;IACnBwY,cAAA,CAAe9U,IAAA,CAAKwT,OAAA,EAASlX,KAAA,EAAON,GAAA,IAAO;MACzC,OAAO+Y,YAAA,CAAa/Y,GAAA,EAAKgE,IAAI;IAC/B,CAAC;EACH,CAAC;AACH;;;ACnhBA,YAAYkV,KAAA,MAAW;AACvB,SAASvT,UAAA,QAAqC;AAC9C,SAASwT,UAAA,QAAkB;AAapB,IAAMC,aAAA,GAAgBA,CAAC;EAC5BzW,QAAA;EACA,GAAGrC;AACL,MAAwC;EACtC,MAAM+Y,SAAA,GAAY1T,UAAA,CAAW2T,GAAG;EAGhC,MAAM/X,KAAA,GAAQjB,KAAA,CAAMiB,KAAA,IAAS,CAAC,CAAC8X,SAAA,CAAU9X,KAAA;IACvCG,SAAA,GAAYpB,KAAA,CAAMoB,SAAA,IAAa,CAAC,CAAC2X,SAAA,CAAU3X,SAAA;EAG7CpB,KAAA,GAAQ6Y,UAAA,CAAW,OAAO;IAAE5X,KAAA;IAAOG;EAAU,IAAI,CAACH,KAAA,EAAOG,SAAS,CAAC;EAEnE,MAAM;IAAE6X;EAAS,IAAID,GAAA;EACrB,OAAO,eAAAJ,KAAA,CAAAM,aAAA,CAACD,QAAA;IAAS3Z,KAAA,EAAOU;EAAA,GAAQqC,QAAS;AAC3C;AAEA,IAAM2W,GAAA,GAAMG,WAAA,CAAYL,aAAA,EAAe,CAAC,CAAkB;AAG1DA,aAAA,CAAcG,QAAA,GAAWD,GAAA,CAAIC,QAAA;AAC7BH,aAAA,CAAcM,QAAA,GAAWJ,GAAA,CAAII,QAAA;AAG7B,SAASD,YAAe3N,MAAA,EAAa6N,IAAA,EAA2B;EAC9D9Y,MAAA,CAAOoI,MAAA,CAAO6C,MAAA,EAAcoN,KAAA,CAAAU,aAAA,CAAcD,IAAI,CAAC;EAC/C7N,MAAA,CAAOyN,QAAA,CAASM,QAAA,GAAW/N,MAAA;EAC3BA,MAAA,CAAO4N,QAAA,CAASG,QAAA,GAAW/N,MAAA;EAC3B,OAAOA,MAAA;AACT;;;AC5CA,SAAS5M,IAAA,IAAA4a,KAAA,EAAM1a,EAAA,IAAA2a,GAAA,EAAIC,mBAAA,QAA2B;AA8EvC,IAAMC,SAAA,GAAYA,CAAA,KAEA;EACvB,MAAMtV,OAAA,GAA+B,EAAC;EAEtC,MAAMuV,UAAA,GAA8B,SAAAD,CAAU3Z,KAAA,EAAO;IACnD0Z,mBAAA,CAAoB;IAEpB,MAAMjO,OAAA,GAAyB,EAAC;IAEhC+N,KAAA,CAAKnV,OAAA,EAAS,CAACX,IAAA,EAAMS,CAAA,KAAM;MACzB,IAAIsV,GAAA,CAAGhZ,GAAA,CAAIT,KAAK,GAAG;QACjByL,OAAA,CAAQvG,IAAA,CAAKxB,IAAA,CAAKgB,KAAA,CAAM,CAAC;MAC3B,OAAO;QACL,MAAMO,OAAA,GAAS4U,SAAA,CAAU7Z,KAAA,EAAO0D,IAAA,EAAMS,CAAC;QACvC,IAAIc,OAAA,EAAQ;UACVwG,OAAA,CAAQvG,IAAA,CAAKxB,IAAA,CAAKgB,KAAA,CAAMO,OAAM,CAAC;QACjC;MACF;IACF,CAAC;IAED,OAAOwG,OAAA;EACT;EAEAmO,UAAA,CAAUvV,OAAA,GAAUA,OAAA;EAGpBuV,UAAA,CAAU/V,GAAA,GAAM,UAAUH,IAAA,EAAyB;IACjD,IAAI,CAACW,OAAA,CAAQ1E,QAAA,CAAS+D,IAAI,GAAG;MAC3BW,OAAA,CAAQa,IAAA,CAAKxB,IAAI;IACnB;EACF;EAGAkW,UAAA,CAAUjW,MAAA,GAAS,UAAUD,IAAA,EAAyB;IACpD,MAAMS,CAAA,GAAIE,OAAA,CAAQyV,OAAA,CAAQpW,IAAI;IAC9B,IAAI,CAACS,CAAA,EAAGE,OAAA,CAAQ0V,MAAA,CAAO5V,CAAA,EAAG,CAAC;EAC7B;EAGAyV,UAAA,CAAU3Y,KAAA,GAAQ,YAAY;IAC5BuY,KAAA,CAAKnV,OAAA,EAASX,IAAA,IAAQA,IAAA,CAAKzC,KAAA,CAAM,GAAGuN,SAAS,CAAC;IAC9C,OAAO;EACT;EAGAoL,UAAA,CAAUpP,MAAA,GAAS,YAAY;IAC7BgP,KAAA,CAAKnV,OAAA,EAASX,IAAA,IAAQA,IAAA,CAAK8G,MAAA,CAAO,GAAGgE,SAAS,CAAC;IAC/C,OAAO;EACT;EAGAoL,UAAA,CAAUnG,GAAA,GAAM,UACd9J,MAAA,EAGA;IACA6P,KAAA,CAAKnV,OAAA,EAAS,CAACX,IAAA,EAAMS,CAAA,KAAM;MACzB,MAAMc,OAAA,GAASwU,GAAA,CAAGja,GAAA,CAAImK,MAAM,IAAIA,MAAA,CAAOxF,CAAA,EAAGT,IAAI,IAAIiG,MAAA;MAClD,IAAI1E,OAAA,EAAQ;QACVvB,IAAA,CAAK+P,GAAA,CAAIxO,OAAM;MACjB;IACF,CAAC;EACH;EAEA2U,UAAA,CAAUlV,KAAA,GAAQ,UAAU1E,KAAA,EAA4C;IACtE,MAAMyL,OAAA,GAAyB,EAAC;IAEhC+N,KAAA,CAAKnV,OAAA,EAAS,CAACX,IAAA,EAAMS,CAAA,KAAM;MACzB,IAAIsV,GAAA,CAAGhZ,GAAA,CAAIT,KAAK,GAAG;QACjByL,OAAA,CAAQvG,IAAA,CAAKxB,IAAA,CAAKgB,KAAA,CAAM,CAAC;MAC3B,OAAO;QACL,MAAMO,OAAA,GAAS,KAAK4U,SAAA,CAAU7Z,KAAA,EAAO0D,IAAA,EAAMS,CAAC;QAC5C,IAAIc,OAAA,EAAQ;UACVwG,OAAA,CAAQvG,IAAA,CAAKxB,IAAA,CAAKgB,KAAA,CAAMO,OAAM,CAAC;QACjC;MACF;IACF,CAAC;IAED,OAAOwG,OAAA;EACT;EAGAmO,UAAA,CAAUlM,IAAA,GAAO,YAAY;IAC3B8L,KAAA,CAAKnV,OAAA,EAASX,IAAA,IAAQA,IAAA,CAAKgK,IAAA,CAAK,GAAGc,SAAS,CAAC;IAC7C,OAAO;EACT;EAEAoL,UAAA,CAAUzX,MAAA,GAAS,UAAUnC,KAAA,EAA2C;IACtEwZ,KAAA,CAAKnV,OAAA,EAAS,CAACX,IAAA,EAAMS,CAAA,KAAMT,IAAA,CAAKvB,MAAA,CAAO,KAAK0X,SAAA,CAAU7Z,KAAA,EAAO0D,IAAA,EAAMS,CAAC,CAAC,CAAC;IACtE,OAAO;EACT;EAGA,MAAM0V,SAAA,GAAY,SAAAA,CAChBjE,GAAA,EACAlS,IAAA,EACAsW,KAAA,EACA;IACA,OAAOP,GAAA,CAAGja,GAAA,CAAIoW,GAAG,IAAIA,GAAA,CAAIoE,KAAA,EAAOtW,IAAI,IAAIkS,GAAA;EAC1C;EAEAgE,UAAA,CAAUC,SAAA,GAAYA,SAAA;EAEtB,OAAOD,UAAA;AACT;;;AZ9GO,SAASK,WACd3V,MAAA,EACAtE,KAAA,EACAka,IAAA,EACK;EACL,MAAMC,OAAA,GAAU1U,GAAA,CAAGjG,GAAA,CAAIQ,KAAK,KAAKA,KAAA;EACjC,IAAIma,OAAA,IAAW,CAACD,IAAA,EAAMA,IAAA,GAAO,EAAC;EAG9B,MAAMpZ,GAAA,GAAMyE,OAAA,CACV,MAAO4U,OAAA,IAAW3L,SAAA,CAAUlK,MAAA,IAAU,IAAIqV,SAAA,CAAU,IAAI,QACxD,EACF;EAYA,MAAMS,QAAA,GAAW5U,MAAA,CAAO,CAAC;EACzB,MAAM6U,WAAA,GAAcxU,cAAA,CAAe;EAGnC,MAAMqE,KAAA,GAAQ3E,OAAA,CACZ,OAAc;IACZ+U,KAAA,EAAO,EAAC;IACR9V,KAAA,EAAO,EAAC;IACR4G,MAAM1H,IAAA,EAAM6W,QAAA,EAAS;MACnB,MAAMC,QAAA,GAAUjC,UAAA,CAAW7U,IAAA,EAAM6W,QAAO;MAIxC,MAAME,YAAA,GACJL,QAAA,CAAS/V,OAAA,GAAU,KACnB,CAAC6F,KAAA,CAAM1F,KAAA,CAAMF,MAAA,IACb,CAAC/D,MAAA,CAAOF,IAAA,CAAKma,QAAO,EAAE9O,IAAA,CAAKhM,GAAA,IAAO,CAACgE,IAAA,CAAKwT,OAAA,CAAQxX,GAAG,CAAC;MAEtD,OAAO+a,YAAA,GACH3C,gBAAA,CAAiBpU,IAAA,EAAM6W,QAAO,IAC9B,IAAI3V,OAAA,CAAaC,OAAA,IAAW;QAC1B6T,UAAA,CAAWhV,IAAA,EAAM8W,QAAO;QACxBtQ,KAAA,CAAM1F,KAAA,CAAMU,IAAA,CAAK,MAAM;UACrBL,OAAA,CAAQiT,gBAAA,CAAiBpU,IAAA,EAAM6W,QAAO,CAAC;QACzC,CAAC;QACDF,WAAA,CAAY;MACd,CAAC;IACP;EACF,IACA,EACF;EAEA,MAAMC,KAAA,GAAQ9U,MAAA,CAAO,CAAC,GAAG0E,KAAA,CAAMoQ,KAAK,CAAC;EACrC,MAAMI,OAAA,GAAiB,EAAC;EAGxB,MAAMC,UAAA,GAAahV,OAAA,CAAQrB,MAAM,KAAK;EAItCiB,OAAA,CAAQ,MAAM;IAEZG,KAAA,CAAK4U,KAAA,CAAMjW,OAAA,CAAQuW,KAAA,CAAMtW,MAAA,EAAQqW,UAAU,GAAGjX,IAAA,IAAQ;MACpDD,UAAA,CAAWC,IAAA,EAAM5C,GAAG;MACpB4C,IAAA,CAAKgK,IAAA,CAAK,IAAI;IAChB,CAAC;IACD4M,KAAA,CAAMjW,OAAA,CAAQC,MAAA,GAASA,MAAA;IAEvBuW,cAAA,CAAeF,UAAA,EAAYrW,MAAM;EACnC,GAAG,CAACA,MAAM,CAAC;EAGXiB,OAAA,CAAQ,MAAM;IACZsV,cAAA,CAAe,GAAG3R,IAAA,CAAK2I,GAAA,CAAI8I,UAAA,EAAYrW,MAAM,CAAC;EAChD,GAAG4V,IAAI;EAGP,SAASW,eAAeC,UAAA,EAAoBC,QAAA,EAAkB;IAC5D,SAAS5W,CAAA,GAAI2W,UAAA,EAAY3W,CAAA,GAAI4W,QAAA,EAAU5W,CAAA,IAAK;MAC1C,MAAMT,IAAA,GACJ4W,KAAA,CAAMjW,OAAA,CAAQF,CAAC,MACdmW,KAAA,CAAMjW,OAAA,CAAQF,CAAC,IAAI,IAAI6S,UAAA,CAAW,MAAM9M,KAAA,CAAMkB,KAAK;MAEtD,MAAMnG,OAAA,GAA8BkV,OAAA,GAChCA,OAAA,CAAQhW,CAAA,EAAGT,IAAI,IACd1D,KAAA,CAAcmE,CAAC;MAEpB,IAAIc,OAAA,EAAQ;QACVyV,OAAA,CAAQvW,CAAC,IAAIkS,aAAA,CAAcpR,OAAM;MACnC;IACF;EACF;EAKA,MAAMiS,OAAA,GAAUoD,KAAA,CAAMjW,OAAA,CAAQnB,GAAA,CAAI,CAACQ,IAAA,EAAMS,CAAA,KAAMoU,UAAA,CAAW7U,IAAA,EAAMgX,OAAA,CAAQvW,CAAC,CAAC,CAAC;EAE3E,MAAM6W,OAAA,GAAU1V,WAAA,CAAWwT,aAAa;EACxC,MAAMmC,WAAA,GAActV,OAAA,CAAQqV,OAAO;EACnC,MAAME,UAAA,GAAaF,OAAA,KAAYC,WAAA,IAAe3X,QAAA,CAAS0X,OAAO;EAE9DlV,0BAAA,CAA0B,MAAM;IAC9BsU,QAAA,CAAS/V,OAAA;IAGT6F,KAAA,CAAMoQ,KAAA,GAAQA,KAAA,CAAMjW,OAAA;IAGpB,MAAM;MAAEG;IAAM,IAAI0F,KAAA;IAClB,IAAI1F,KAAA,CAAMF,MAAA,EAAQ;MAChB4F,KAAA,CAAM1F,KAAA,GAAQ,EAAC;MACfkB,KAAA,CAAKlB,KAAA,EAAO2W,EAAA,IAAMA,EAAA,CAAG,CAAC;IACxB;IAGAzV,KAAA,CAAK4U,KAAA,CAAMjW,OAAA,EAAS,CAACX,IAAA,EAAMS,CAAA,KAAM;MAE/BrD,GAAA,EAAK+C,GAAA,CAAIH,IAAI;MAGb,IAAIwX,UAAA,EAAY;QACdxX,IAAA,CAAKgB,KAAA,CAAM;UAAEzE,OAAA,EAAS+a;QAAQ,CAAC;MACjC;MAGA,MAAM/V,OAAA,GAASyV,OAAA,CAAQvW,CAAC;MACxB,IAAIc,OAAA,EAAQ;QAEVrB,UAAA,CAAWF,IAAA,EAAMuB,OAAA,CAAOnE,GAAG;QAI3B,IAAI4C,IAAA,CAAK5C,GAAA,EAAK;UACZ4C,IAAA,CAAKc,KAAA,CAAMU,IAAA,CAAKD,OAAM;QACxB,OAAO;UACLvB,IAAA,CAAKgB,KAAA,CAAMO,OAAM;QACnB;MACF;IACF,CAAC;EACH,CAAC;EAGDW,OAAA,CAAQ,MAAM,MAAM;IAClBF,KAAA,CAAKwE,KAAA,CAAMoQ,KAAA,EAAO5W,IAAA,IAAQA,IAAA,CAAKgK,IAAA,CAAK,IAAI,CAAC;EAC3C,CAAC;EAID,MAAM/D,MAAA,GAASuN,OAAA,CAAQhU,GAAA,CAAIkY,CAAA,KAAM;IAAE,GAAGA;EAAE,EAAE;EAE1C,OAAOta,GAAA,GAAM,CAAC6I,MAAA,EAAQ7I,GAAG,IAAI6I,MAAA;AAC/B;;;ADvKO,SAAS0R,UAAUrb,KAAA,EAAYka,IAAA,EAAuB;EAC3D,MAAMoB,IAAA,GAAOlW,GAAA,CAAG5F,GAAA,CAAIQ,KAAK;EACzB,MAAM,CAAC,CAAC2J,MAAM,GAAG7I,GAAG,IAAImZ,UAAA,CACtB,GACAqB,IAAA,GAAOtb,KAAA,GAAQ,CAACA,KAAK,GACrBsb,IAAA,GAAOpB,IAAA,IAAQ,EAAC,GAAIA,IACtB;EACA,OAAOoB,IAAA,IAAQ9M,SAAA,CAAUlK,MAAA,IAAU,IAAI,CAACqF,MAAA,EAAQ7I,GAAG,IAAI6I,MAAA;AACzD;;;ActEA,SAAS4R,QAAA,QAAgB;AAKzB,IAAMC,aAAA,GAAgBA,CAAA,KAAM7B,SAAA,CAAe;AAEpC,IAAM8B,YAAA,GAAeA,CAAA,KAC1BF,QAAA,CAASC,aAAa,EAAE,CAAC;;;ACR3B,SAASE,WAAA,EAAa9V,OAAA,IAAA+V,QAAA,QAAe;AAwB9B,IAAMC,cAAA,GAAiBA,CAC5B3Z,OAAA,EACAjC,KAAA,KACG;EACH,MAAM6b,WAAA,GAAcH,WAAA,CAAY,MAAM,IAAIrL,WAAA,CAAYpO,OAAA,EAASjC,KAAK,CAAC;EAErE2b,QAAA,CAAQ,MAAM,MAAM;IAClBE,WAAA,CAAYnO,IAAA,CAAK;EACnB,CAAC;EAED,OAAOmO,WAAA;AACT;;;ACnCA,SAASjd,IAAA,IAAAkd,KAAA,EAAMhd,EAAA,IAAAid,IAAA,EAAIld,yBAAA,IAAAmd,0BAAA,QAAiC;AAiF7C,SAASC,SACd3X,MAAA,EACA4X,QAAA,EACAhC,IAAA,EACA;EACA,MAAMC,OAAA,GAAU4B,IAAA,CAAGvc,GAAA,CAAI0c,QAAQ,KAAKA,QAAA;EACpC,IAAI/B,OAAA,IAAW,CAACD,IAAA,EAAMA,IAAA,GAAO,EAAC;EAG9B,IAAI/Y,OAAA,GAAU;EACd,IAAIgb,SAAA,GAAmC;EAEvC,MAAMxQ,MAAA,GAASsO,UAAA,CACb3V,MAAA,EACA,CAACH,CAAA,EAAGT,IAAA,KAAS;IACX,MAAM1D,KAAA,GAAQma,OAAA,GAAUA,OAAA,CAAQhW,CAAA,EAAGT,IAAI,IAAIwY,QAAA;IAC3CC,SAAA,GAAYnc,KAAA,CAAMc,GAAA;IAClBK,OAAA,GAAUA,OAAA,IAAWnB,KAAA,CAAMmB,OAAA;IAE3B,OAAOnB,KAAA;EACT;EAAA;EAAA;EAGAka,IAAA,IAAQ,CAAC,CAAC,CAAC,CACb;EAEA8B,0BAAA,CAA0B,MAAM;IAI9BF,KAAA,CAAKnQ,MAAA,CAAO,CAAC,EAAEtH,OAAA,EAAS,CAACX,IAAA,EAAMS,CAAA,KAAM;MACnC,MAAMqL,MAAA,GAAS7D,MAAA,CAAO,CAAC,EAAEtH,OAAA,CAAQF,CAAA,IAAKhD,OAAA,GAAU,IAAI,GAAG;MAKvDyC,UAAA,CAAWF,IAAA,EAAMyY,SAAS;MAO1B,IAAIzY,IAAA,CAAK5C,GAAA,EAAK;QACZ,IAAI0O,MAAA,EAAQ;UACV9L,IAAA,CAAKvB,MAAA,CAAO;YAAEtB,EAAA,EAAI2O,MAAA,CAAO0H;UAAQ,CAAC;QACpC;QAEA;MACF;MAEA,IAAI1H,MAAA,EAAQ;QACV9L,IAAA,CAAKgB,KAAA,CAAM;UAAE7D,EAAA,EAAI2O,MAAA,CAAO0H;QAAQ,CAAC;MACnC,OAAO;QACLxT,IAAA,CAAKgB,KAAA,CAAM;MACb;IACF,CAAC;EACH,GAAGwV,IAAI;EAEP,IAAIC,OAAA,IAAW3L,SAAA,CAAUlK,MAAA,IAAU,GAAG;IACpC,MAAMxD,GAAA,GAAMqb,SAAA,IAAaxQ,MAAA,CAAO,CAAC;IAEjC7K,GAAA,CAAI,WAAW,IAAI,CAACsb,SAAA,EAAU1Y,IAAA,EAAMS,CAAA,KAAM;MACxC,MAAMnE,KAAA,GAAQ+b,IAAA,CAAGvc,GAAA,CAAI4c,SAAQ,IAAIA,SAAA,CAASjY,CAAA,EAAGT,IAAI,IAAI0Y,SAAA;MACrD,IAAIpc,KAAA,EAAO;QACT,MAAMwP,MAAA,GAAS1O,GAAA,CAAIuD,OAAA,CAAQF,CAAA,IAAKnE,KAAA,CAAMmB,OAAA,GAAU,IAAI,GAAG;QACvD,IAAIqO,MAAA,EAAQxP,KAAA,CAAMa,EAAA,GAAK2O,MAAA,CAAO0H,OAAA;QAC9B,OAAOlX,KAAA;MACT;IACF;IACA,OAAO2L,MAAA;EACT;EAEA,OAAOA,MAAA,CAAO,CAAC;AACjB;;;AC3JA,YAAY0Q,MAAA,MAAW;AACvB,SAAShX,UAAA,IAAAiX,WAAA,EAAY9W,MAAA,IAAA+W,OAAA,EAAQhX,OAAA,IAAAiX,QAAA,QAAe;AAE5C,SACE1d,EAAA,IAAA2d,IAAA,EACA1d,OAAA,IAAA2d,QAAA,EACA7W,cAAA,IAAA8W,eAAA,EACA/W,OAAA,IAAAgX,QAAA,EACAjX,OAAA,IAAAkX,QAAA,EACAje,IAAA,IAAAke,KAAA,EACAje,yBAAA,IAAAke,0BAAA,QACK;AA6DA,SAASC,cACdC,IAAA,EACAjd,KAAA,EACAka,IAAA,EACK;EACL,MAAMC,OAAA,GAAUsC,IAAA,CAAGjd,GAAA,CAAIQ,KAAK,KAAKA,KAAA;EAEjC,MAAM;IACJgB,KAAA;IACAe,IAAA;IACAD,KAAA,GAAQ;IACRE,OAAA,GAAU;IACVkb,eAAA,GAAkB;IAClB5a,WAAA;IACAxB,GAAA,EAAKqc,QAAA;IACLxc,MAAA,EAAQyc;EACV,IAA6BjD,OAAA,GAAUA,OAAA,CAAQ,IAAIna,KAAA;EAGnD,MAAMc,GAAA,GAAM0b,QAAA,CACV,MAAOrC,OAAA,IAAW3L,SAAA,CAAUlK,MAAA,IAAU,IAAIqV,SAAA,CAAU,IAAI,QACxD,EACF;EAGA,MAAM9X,KAAA,GAAQ6a,QAAA,CAAQO,IAAI;EAC1B,MAAMI,WAAA,GAAiC,EAAC;EAGxC,MAAMC,eAAA,GAAkBf,OAAA,CAAiC,IAAI;EAC7D,MAAMgB,eAAA,GAAkBvc,KAAA,GAAQ,OAAOsc,eAAA,CAAgBjZ,OAAA;EAEvD0Y,0BAAA,CAA0B,MAAM;IAC9BO,eAAA,CAAgBjZ,OAAA,GAAUgZ,WAAA;EAC5B,CAAC;EAEDT,QAAA,CAAQ,MAAM;IASZE,KAAA,CAAKO,WAAA,EAAavP,CAAA,IAAK;MACrBhN,GAAA,EAAK+C,GAAA,CAAIiK,CAAA,CAAEpK,IAAI;MACfoK,CAAA,CAAEpK,IAAA,CAAK5C,GAAA,GAAMA,GAAA;IACf,CAAC;IAGD,OAAO,MAAM;MACXgc,KAAA,CAAKQ,eAAA,CAAgBjZ,OAAA,EAAUyJ,CAAA,IAAK;QAClC,IAAIA,CAAA,CAAE0P,OAAA,EAAS;UACbC,YAAA,CAAa3P,CAAA,CAAE4P,YAAa;QAC9B;QACAja,UAAA,CAAWqK,CAAA,CAAEpK,IAAA,EAAM5C,GAAG;QACtBgN,CAAA,CAAEpK,IAAA,CAAKgK,IAAA,CAAK,IAAI;MAClB,CAAC;IACH;EACF,CAAC;EAMD,MAAMrN,IAAA,GAAOsd,OAAA,CAAQ9b,KAAA,EAAOsY,OAAA,GAAUA,OAAA,CAAQ,IAAIna,KAAA,EAAOud,eAAe;EAGxE,MAAMC,OAAA,GAAWxc,KAAA,IAASsc,eAAA,CAAgBjZ,OAAA,IAAY,EAAC;EACvD0Y,0BAAA,CAA0B,MACxBD,KAAA,CAAKU,OAAA,EAAS,CAAC;IAAE9Z,IAAA;IAAMmK,IAAA;IAAMnO;EAAI,MAAM;IACrC+D,UAAA,CAAWC,IAAA,EAAM5C,GAAG;IACpBzB,QAAA,CAASiD,WAAA,EAAauL,IAAA,EAAMnO,GAAG;EACjC,CAAC,CACH;EAGA,MAAMke,MAAA,GAAmB,EAAC;EAC1B,IAAIL,eAAA,EACFT,KAAA,CAAKS,eAAA,EAAiB,CAACzP,CAAA,EAAG3J,CAAA,KAAM;IAE9B,IAAI2J,CAAA,CAAE0P,OAAA,EAAS;MACbC,YAAA,CAAa3P,CAAA,CAAE4P,YAAa;MAC5BF,OAAA,CAAQtY,IAAA,CAAK4I,CAAC;IAChB,OAAO;MACL3J,CAAA,GAAIyZ,MAAA,CAAOzZ,CAAC,IAAI9D,IAAA,CAAKyZ,OAAA,CAAQhM,CAAA,CAAEpO,GAAG;MAClC,IAAI,CAACyE,CAAA,EAAGkZ,WAAA,CAAYlZ,CAAC,IAAI2J,CAAA;IAC3B;EACF,CAAC;EAGHgP,KAAA,CAAKjb,KAAA,EAAO,CAACgM,IAAA,EAAM1J,CAAA,KAAM;IACvB,IAAI,CAACkZ,WAAA,CAAYlZ,CAAC,GAAG;MACnBkZ,WAAA,CAAYlZ,CAAC,IAAI;QACfzE,GAAA,EAAKW,IAAA,CAAK8D,CAAC;QACX0J,IAAA;QACAgQ,KAAA;QACAna,IAAA,EAAM,IAAIsT,UAAA,CAAW;MACvB;MAEAqG,WAAA,CAAYlZ,CAAC,EAAET,IAAA,CAAKmK,IAAA,GAAOA,IAAA;IAC7B;EACF,CAAC;EAID,IAAI+P,MAAA,CAAOtZ,MAAA,EAAQ;IACjB,IAAIH,CAAA,GAAI;IACR,MAAM;MAAE/B;IAAM,IAA6B+X,OAAA,GAAUA,OAAA,CAAQ,IAAIna,KAAA;IACjE8c,KAAA,CAAKc,MAAA,EAAQ,CAACE,QAAA,EAAUC,SAAA,KAAc;MACpC,MAAMjQ,CAAA,GAAIyP,eAAA,CAAiBQ,SAAS;MACpC,IAAI,CAACD,QAAA,EAAU;QACb3Z,CAAA,GAAIkZ,WAAA,CAAYvD,OAAA,CAAQhM,CAAC;QACzBuP,WAAA,CAAYlZ,CAAC,IAAI;UAAE,GAAG2J,CAAA;UAAGD,IAAA,EAAMhM,KAAA,CAAMic,QAAQ;QAAE;MACjD,WAAW1b,KAAA,EAAO;QAChBib,WAAA,CAAYtD,MAAA,CAAO,EAAE5V,CAAA,EAAG,GAAG2J,CAAC;MAC9B;IACF,CAAC;EACH;EAEA,IAAI2O,IAAA,CAAGjd,GAAA,CAAIuC,IAAI,GAAG;IAChBsb,WAAA,CAAYtb,IAAA,CAAK,CAACic,CAAA,EAAGC,CAAA,KAAMlc,IAAA,CAAKic,CAAA,CAAEnQ,IAAA,EAAMoQ,CAAA,CAAEpQ,IAAI,CAAC;EACjD;EAGA,IAAIxM,KAAA,GAAQ,CAACS,KAAA;EAGb,MAAMuY,WAAA,GAAcsC,eAAA,CAAe;EAGnC,MAAM1S,YAAA,GAAe9J,eAAA,CAAoCH,KAAK;EAE9D,MAAMke,OAAA,GAAU,mBAAI1G,GAAA,CAA6B;EACjD,MAAM2G,kBAAA,GAAqB5B,OAAA,CAAO,mBAAI/E,GAAA,CAA6B,CAAC;EAEpE,MAAM4G,WAAA,GAAc7B,OAAA,CAAO,KAAK;EAChCO,KAAA,CAAKO,WAAA,EAAa,CAACvP,CAAA,EAAG3J,CAAA,KAAM;IAC1B,MAAMzE,GAAA,GAAMoO,CAAA,CAAEpO,GAAA;IACd,MAAM2e,SAAA,GAAYvQ,CAAA,CAAE+P,KAAA;IAEpB,MAAMlZ,CAAA,GAA6BwV,OAAA,GAAUA,OAAA,CAAQ,IAAIna,KAAA;IAEzD,IAAI6C,GAAA;IACJ,IAAIgb,KAAA;IAEJ,MAAMS,UAAA,GAAajf,QAAA,CAASsF,CAAA,CAAEtD,KAAA,IAAS,GAAG3B,GAAG;IAE7C,IAAI2e,SAAA,yBAAoC;MACtCxb,GAAA,GAAK8B,CAAA,CAAEzC,KAAA;MACP2b,KAAA;IACF,OAAO;MACL,MAAMU,OAAA,GAAUle,IAAA,CAAKyZ,OAAA,CAAQpa,GAAG,IAAI;MACpC,IAAI2e,SAAA,yBAAoC;QACtC,IAAIE,OAAA,EAAS;UACX1b,GAAA,GAAK8B,CAAA,CAAEvC,KAAA;UACPyb,KAAA;QACF,WAAYhb,GAAA,GAAK8B,CAAA,CAAExC,MAAA,EAAS;UAC1B0b,KAAA;QACF,OAAO;MACT,WAAW,CAACU,OAAA,EAAS;QACnB1b,GAAA,GAAK8B,CAAA,CAAEzC,KAAA;QACP2b,KAAA;MACF,OAAO;IACT;IAIAhb,GAAA,GAAKxD,QAAA,CAASwD,GAAA,EAAIiL,CAAA,CAAED,IAAA,EAAM1J,CAAC;IAC3BtB,GAAA,GAAK4Z,IAAA,CAAG3c,GAAA,CAAI+C,GAAE,IAAID,OAAA,CAAQC,GAAE,IAAI;MAAEhC,EAAA,EAAAgC;IAAG;IAarC,IAAI,CAACA,GAAA,CAAGlC,MAAA,EAAQ;MACd,MAAMkI,OAAA,GAASuU,WAAA,IAAenT,YAAA,CAAatJ,MAAA;MAC3CkC,GAAA,CAAGlC,MAAA,GAAStB,QAAA,CAASwJ,OAAA,EAAQiF,CAAA,CAAED,IAAA,EAAM1J,CAAA,EAAG0Z,KAAK;IAC/C;IAEAxc,KAAA,IAASS,KAAA;IAGT,MAAMsP,OAAA,GAA0C;MAC9C,GAAGnH,YAAA;MAAA;MAEH5I,KAAA,EAAOid,UAAA,GAAajd,KAAA;MACpBP,GAAA,EAAKqc,QAAA;MACL/b,SAAA,EAAWuD,CAAA,CAAEvD,SAAA;MAAA;MAEbJ,KAAA,EAAO;MAAA;MAEP,GAAI6B;IACN;IAEA,IAAIgb,KAAA,2BAAkCpB,IAAA,CAAGhc,GAAA,CAAI2Q,OAAA,CAAQxQ,IAAI,GAAG;MAC1D,MAAM4d,EAAA,GAAIrE,OAAA,GAAUA,OAAA,CAAQ,IAAIna,KAAA;MAIhC,MAAMY,IAAA,GAAO6b,IAAA,CAAGhc,GAAA,CAAI+d,EAAA,CAAEvc,OAAO,KAAKsb,eAAA,GAAkBiB,EAAA,CAAE5d,IAAA,GAAO4d,EAAA,CAAEvc,OAAA;MAE/DmP,OAAA,CAAQxQ,IAAA,GAAOvB,QAAA,CAASuB,IAAA,EAAMkN,CAAA,CAAED,IAAA,EAAM1J,CAAC;IACzC;IAEA,MAAM;MAAEvC;IAAU,IAAIwP,OAAA;IACtBA,OAAA,CAAQxP,SAAA,GAAY+J,MAAA,IAAU;MAC5BtM,QAAA,CAASuC,SAAA,EAAW+J,MAAM;MAE1B,MAAM8S,YAAA,GAAcnB,eAAA,CAAgBjZ,OAAA;MACpC,MAAMqa,EAAA,GAAID,YAAA,CAAYE,IAAA,CAAKC,EAAA,IAAKA,EAAA,CAAElf,GAAA,KAAQA,GAAG;MAC7C,IAAI,CAACgf,EAAA,EAAG;MAIR,IAAI/S,MAAA,CAAOC,SAAA,IAAa8S,EAAA,CAAEb,KAAA,2BAAiC;QAQzD;MACF;MAEA,IAAIa,EAAA,CAAEhb,IAAA,CAAK4L,IAAA,EAAM;QACf,MAAMA,IAAA,GAAOmP,YAAA,CAAY1S,KAAA,CAAM6S,EAAA,IAAKA,EAAA,CAAElb,IAAA,CAAK4L,IAAI;QAC/C,IAAIoP,EAAA,CAAEb,KAAA,yBAAgC;UACpC,MAAMgB,MAAA,GAASxf,QAAA,CAAS2C,OAAA,EAAS0c,EAAA,CAAE7Q,IAAI;UACvC,IAAIgR,MAAA,KAAW,OAAO;YACpB,MAAMC,QAAA,GAAWD,MAAA,KAAW,OAAO,IAAIA,MAAA;YACvCH,EAAA,CAAElB,OAAA,GAAU;YAGZ,IAAI,CAAClO,IAAA,IAAQwP,QAAA,GAAW,GAAG;cAEzB,IAAIA,QAAA,IAAY,YACdJ,EAAA,CAAEhB,YAAA,GAAe5S,UAAA,CAAWuP,WAAA,EAAayE,QAAQ;cACnD;YACF;UACF;QACF;QAEA,IAAIxP,IAAA,IAAQmP,YAAA,CAAY/S,IAAA,CAAKkT,EAAA,IAAKA,EAAA,CAAEpB,OAAO,GAAG;UAK5CW,kBAAA,CAAmB9Z,OAAA,CAAQV,MAAA,CAAO+a,EAAC;UAEnC,IAAIxB,eAAA,EAAiB;YAKnBkB,WAAA,CAAY/Z,OAAA,GAAU;UACxB;UAEAgW,WAAA,CAAY;QACd;MACF;IACF;IAEA,MAAMnD,OAAA,GAAUqB,UAAA,CAAWzK,CAAA,CAAEpK,IAAA,EAAM0N,OAAO;IAK1C,IAAIyM,KAAA,4BAAmCX,eAAA,EAAiB;MACtDiB,kBAAA,CAAmB9Z,OAAA,CAAQoP,GAAA,CAAI3F,CAAA,EAAG;QAAE+P,KAAA;QAAO3G,OAAA;QAAS9F;MAAQ,CAAC;IAC/D,OAAO;MACL8M,OAAA,CAAQzK,GAAA,CAAI3F,CAAA,EAAG;QAAE+P,KAAA;QAAO3G,OAAA;QAAS9F;MAAQ,CAAC;IAC5C;EACF,CAAC;EAGD,MAAM4J,OAAA,GAAUsB,WAAA,CAAWxD,aAAa;EACxC,MAAMmC,WAAA,GAAc4B,QAAA,CAAQ7B,OAAO;EACnC,MAAME,UAAA,GAAaF,OAAA,KAAYC,WAAA,IAAe3X,QAAA,CAAS0X,OAAO;EAG9D+B,0BAAA,CAA0B,MAAM;IAC9B,IAAI7B,UAAA,EAAY;MACd4B,KAAA,CAAKO,WAAA,EAAavP,CAAA,IAAK;QACrBA,CAAA,CAAEpK,IAAA,CAAKgB,KAAA,CAAM;UAAEzE,OAAA,EAAS+a;QAAQ,CAAC;MACnC,CAAC;IACH;EACF,GAAG,CAACA,OAAO,CAAC;EAEZ8B,KAAA,CAAKoB,OAAA,EAAS,CAAC3a,CAAA,EAAGuK,CAAA,KAAM;IAMtB,IAAIqQ,kBAAA,CAAmB9Z,OAAA,CAAQ8R,IAAA,EAAM;MACnC,MAAM4I,GAAA,GAAM1B,WAAA,CAAY2B,SAAA,CAAU9U,KAAA,IAASA,KAAA,CAAMxK,GAAA,KAAQoO,CAAA,CAAEpO,GAAG;MAC9D2d,WAAA,CAAYtD,MAAA,CAAOgF,GAAA,EAAK,CAAC;IAC3B;EACF,CAAC;EAEDhC,0BAAA,CACE,MAAM;IAKJD,KAAA,CACEqB,kBAAA,CAAmB9Z,OAAA,CAAQ8R,IAAA,GAAOgI,kBAAA,CAAmB9Z,OAAA,GAAU6Z,OAAA,EAC/D,CAAC;MAAEL,KAAA;MAAOzM;IAAQ,GAAGtD,CAAA,KAAM;MACzB,MAAM;QAAEpK;MAAK,IAAIoK,CAAA;MAEjBA,CAAA,CAAE+P,KAAA,GAAQA,KAAA;MAGV/c,GAAA,EAAK+C,GAAA,CAAIH,IAAI;MAGb,IAAIwX,UAAA,IAAc2C,KAAA,yBAAgC;QAChDna,IAAA,CAAKgB,KAAA,CAAM;UAAEzE,OAAA,EAAS+a;QAAQ,CAAC;MACjC;MAEA,IAAI5J,OAAA,EAAS;QAEXxN,UAAA,CAAWF,IAAA,EAAM0N,OAAA,CAAQtQ,GAAG;QAQ5B,KAAK4C,IAAA,CAAK5C,GAAA,IAAOA,GAAA,KAAQ,CAACsd,WAAA,CAAY/Z,OAAA,EAAS;UAC7CX,IAAA,CAAKvB,MAAA,CAAOiP,OAAO;QACrB,OAAO;UACL1N,IAAA,CAAKgB,KAAA,CAAM0M,OAAO;UAElB,IAAIgN,WAAA,CAAY/Z,OAAA,EAAS;YACvB+Z,WAAA,CAAY/Z,OAAA,GAAU;UACxB;QACF;MACF;IACF,CACF;EACF,GACArD,KAAA,GAAQ,SAASkZ,IACnB;EAEA,MAAM+E,iBAAA,GAAkCC,MAAA,IACtC,eAAA7C,MAAA,CAAAnD,aAAA,CAAAmD,MAAA,CAAA8C,QAAA,QACG9B,WAAA,CAAYna,GAAA,CAAI,CAAC4K,CAAA,EAAG3J,CAAA,KAAM;IACzB,MAAM;MAAE+S;IAAQ,IAAIgH,OAAA,CAAQpS,GAAA,CAAIgC,CAAC,KAAKA,CAAA,CAAEpK,IAAA;IACxC,MAAM0b,IAAA,GAAYF,MAAA,CAAO;MAAE,GAAGhI;IAAQ,GAAGpJ,CAAA,CAAED,IAAA,EAAMC,CAAA,EAAG3J,CAAC;IACrD,OAAOib,IAAA,IAAQA,IAAA,CAAK7P,IAAA,GAClB,eAAA8M,MAAA,CAAAnD,aAAA,CAACkG,IAAA,CAAK7P,IAAA,EAAL;MACE,GAAG6P,IAAA,CAAKpf,KAAA;MACTN,GAAA,EAAK+c,IAAA,CAAG4C,GAAA,CAAIvR,CAAA,CAAEpO,GAAG,KAAK+c,IAAA,CAAGpH,GAAA,CAAIvH,CAAA,CAAEpO,GAAG,IAAIoO,CAAA,CAAEpO,GAAA,GAAMoO,CAAA,CAAEpK,IAAA,CAAK+K,EAAA;MACrD3N,GAAA,EAAKse,IAAA,CAAKte;IAAA,CACZ,IAEAse,IAAA;EAEJ,CAAC,CACH;EAGF,OAAOte,GAAA,GAAM,CAACme,iBAAA,EAAmBne,GAAG,IAAIme,iBAAA;AAC1C;AAGA,IAAIK,OAAA,GAAU;AAEd,SAAS3B,QACP9b,KAAA,EACA;EAAEnC,GAAA;EAAKW,IAAA,GAAOX;AAAI,GAClB6d,eAAA,EACgB;EAChB,IAAIld,IAAA,KAAS,MAAM;IACjB,MAAMud,MAAA,GAAS,mBAAIpN,GAAA,CAAI;IACvB,OAAO3O,KAAA,CAAMqB,GAAA,CAAI2K,IAAA,IAAQ;MACvB,MAAMC,CAAA,GACJyP,eAAA,IACAA,eAAA,CAAgBoB,IAAA,CACdD,EAAA,IACEA,EAAA,CAAE7Q,IAAA,KAASA,IAAA,IACX6Q,EAAA,CAAEb,KAAA,4BACF,CAACD,MAAA,CAAO2B,GAAA,CAAIb,EAAC,CACjB;MACF,IAAI5Q,CAAA,EAAG;QACL8P,MAAA,CAAO/Z,GAAA,CAAIiK,CAAC;QACZ,OAAOA,CAAA,CAAEpO,GAAA;MACX;MACA,OAAO4f,OAAA;IACT,CAAC;EACH;EACA,OAAO7C,IAAA,CAAGhc,GAAA,CAAIJ,IAAI,IAAIwB,KAAA,GAAQ4a,IAAA,CAAGjd,GAAA,CAAIa,IAAI,IAAIwB,KAAA,CAAMqB,GAAA,CAAI7C,IAAI,IAAIqc,QAAA,CAAQrc,IAAI;AAC7E;;;AC5dA,SAASzB,IAAA,IAAA4gB,KAAA,EAAMC,QAAA,EAAU5gB,yBAAA,IAAA6gB,0BAAA,QAAiC;AAmCnD,IAAMC,SAAA,GAAYA,CAAC;EACxBC,SAAA;EACA,GAAGC;AACL,IAAsB,CAAC,MAKjB;EACJ,MAAM,CAACC,YAAA,EAAcC,GAAG,IAAI1E,SAAA,CAC1B,OAAO;IACL2E,OAAA,EAAS;IACTC,OAAA,EAAS;IACTC,eAAA,EAAiB;IACjBC,eAAA,EAAiB;IACjB,GAAGN;EACL,IACA,EACF;EAEAH,0BAAA,CAA0B,MAAM;IAC9B,MAAMU,aAAA,GAAgBX,QAAA,CACpB,CAAC;MAAErE,CAAA;MAAGiF;IAAE,MAAM;MACZN,GAAA,CAAIrb,KAAA,CAAM;QACRsb,OAAA,EAAS5E,CAAA,CAAE/W,OAAA;QACX6b,eAAA,EAAiB9E,CAAA,CAAEpJ,QAAA;QACnBiO,OAAA,EAASI,CAAA,CAAEhc,OAAA;QACX8b,eAAA,EAAiBE,CAAA,CAAErO;MACrB,CAAC;IACH,GACA;MAAE4N,SAAA,EAAWA,SAAA,EAAWvb,OAAA,IAAW;IAAU,CAC/C;IAEA,OAAO,MAAM;MAIXmb,KAAA,CAAKjf,MAAA,CAAOoJ,MAAA,CAAOmW,YAAY,GAAGxgB,KAAA,IAASA,KAAA,CAAMoO,IAAA,CAAK,CAAC;MAEvD0S,aAAA,CAAc;IAChB;EACF,GAAG,EAAE;EAEL,OAAON,YAAA;AACT;;;AC/EA,SAASQ,QAAA,EAAU1hB,IAAA,IAAA2hB,KAAA,EAAM1hB,yBAAA,IAAA2hB,0BAAA,QAAiC;AAmCnD,IAAMC,SAAA,GAAYA,CAAC;EACxBb,SAAA;EACA,GAAGC;AACL,MAGM;EACJ,MAAM,CAACa,UAAA,EAAYX,GAAG,IAAI1E,SAAA,CACxB,OAAO;IACLsF,KAAA,EAAO;IACPC,MAAA,EAAQ;IACR,GAAGf;EACL,IACA,EACF;EAEAW,0BAAA,CAA0B,MAAM;IAC9B,MAAMJ,aAAA,GAAgBE,QAAA,CACpB,CAAC;MAAEK,KAAA;MAAOC;IAAO,MAAM;MACrBb,GAAA,CAAIrb,KAAA,CAAM;QACRic,KAAA;QACAC,MAAA;QACAxf,SAAA,EACEsf,UAAA,CAAWC,KAAA,CAAM7U,GAAA,CAAI,MAAM,KAAK4U,UAAA,CAAWE,MAAA,CAAO9U,GAAA,CAAI,MAAM;MAChE,CAAC;IACH,GACA;MAAE8T,SAAA,EAAWA,SAAA,EAAWvb,OAAA,IAAW;IAAU,CAC/C;IAEA,OAAO,MAAM;MAIXkc,KAAA,CAAKhgB,MAAA,CAAOoJ,MAAA,CAAO+W,UAAU,GAAGphB,KAAA,IAASA,KAAA,CAAMoO,IAAA,CAAK,CAAC;MAErD0S,aAAA,CAAc;IAChB;EACF,GAAG,EAAE;EAEL,OAAOM,UAAA;AACT;;;AC5EA,SAAoBlb,MAAA,IAAAqb,OAAA,EAAQtF,QAAA,IAAAuF,SAAA,QAAgB;AAC5C,SAAShiB,EAAA,IAAAiiB,IAAA,EAAIliB,yBAAA,IAAAmiB,0BAAA,QAAiC;AAc9C,IAAMC,uBAAA,GAA0B;EAC9BC,GAAA,EAAK;EACL/b,GAAA,EAAK;AACP;AAcO,SAASgc,UACdnhB,KAAA,EACAT,IAAA,EACA;EACA,MAAM,CAAC6hB,QAAA,EAAUC,WAAW,IAAIP,SAAA,CAAS,KAAK;EAC9C,MAAMhgB,GAAA,GAAM+f,OAAA,CAAiB;EAE7B,MAAM1G,OAAA,GAAU4G,IAAA,CAAGvhB,GAAA,CAAIQ,KAAK,KAAKA,KAAA;EAEjC,MAAMshB,YAAA,GAAenH,OAAA,GAAUA,OAAA,CAAQ,IAAI,CAAC;EAC5C,MAAM;IAAEtZ,EAAA,EAAAgC,GAAA,GAAK,CAAC;IAAGjC,IAAA,GAAO,CAAC;IAAG,GAAG2gB;EAAgB,IAAID,YAAA;EAEnD,MAAME,qBAAA,GAAwBrH,OAAA,GAAU5a,IAAA,GAAOS,KAAA;EAE/C,MAAM,CAACkX,OAAA,EAAS6I,GAAG,IAAI1E,SAAA,CAAU,OAAO;IAAEza,IAAA;IAAM,GAAG2gB;EAAgB,IAAI,EAAE;EAEzEP,0BAAA,CAA0B,MAAM;IAC9B,MAAMS,OAAA,GAAU3gB,GAAA,CAAIuD,OAAA;IACpB,MAAM;MACJqd,IAAA;MACAC,IAAA;MACAC,MAAA,GAAS;MACT,GAAGC;IACL,IAAIL,qBAAA,IAAyB,CAAC;IAE9B,IACE,CAACC,OAAA,IACAE,IAAA,IAAQP,QAAA,IACT,OAAOU,oBAAA,KAAyB,aAEhC;IAEF,MAAMC,mBAAA,GAAsB,mBAAIC,OAAA,CAA+B;IAE/D,MAAMC,OAAA,GAAUA,CAAA,KAAM;MACpB,IAAIpf,GAAA,EAAI;QAENkd,GAAA,CAAIrb,KAAA,CAAM7B,GAAE;MACd;MAEAwe,WAAA,CAAY,IAAI;MAEhB,MAAMa,OAAA,GAAUA,CAAA,KAAM;QACpB,IAAIthB,IAAA,EAAM;UACRmf,GAAA,CAAIrb,KAAA,CAAM9D,IAAI;QAChB;QACAygB,WAAA,CAAY,KAAK;MACnB;MAEA,OAAOM,IAAA,GAAO,SAAYO,OAAA;IAC5B;IAEA,MAAMC,kBAAA,GAAmDC,OAAA,IAAW;MAClEA,OAAA,CAAQ/Q,OAAA,CAAQgR,KAAA,IAAS;QACvB,MAAMC,OAAA,GAAUP,mBAAA,CAAoBjW,GAAA,CAAIuW,KAAA,CAAM7W,MAAM;QAEpD,IAAI6W,KAAA,CAAME,cAAA,KAAmBC,OAAA,CAAQF,OAAO,GAAG;UAC7C;QACF;QAEA,IAAID,KAAA,CAAME,cAAA,EAAgB;UACxB,MAAME,UAAA,GAAaR,OAAA,CAAQ;UAC3B,IAAIlB,IAAA,CAAGvhB,GAAA,CAAIijB,UAAU,GAAG;YACtBV,mBAAA,CAAoBtO,GAAA,CAAI4O,KAAA,CAAM7W,MAAA,EAAQiX,UAAU;UAClD,OAAO;YACL9J,QAAA,CAAS+J,SAAA,CAAUL,KAAA,CAAM7W,MAAM;UACjC;QACF,WAAW8W,OAAA,EAAS;UAClBA,OAAA,CAAQ;UACRP,mBAAA,CAAoBpe,MAAA,CAAO0e,KAAA,CAAM7W,MAAM;QACzC;MACF,CAAC;IACH;IAEA,MAAMmN,QAAA,GAAW,IAAImJ,oBAAA,CAAqBK,kBAAA,EAAoB;MAC5DT,IAAA,EAAOA,IAAA,IAAQA,IAAA,CAAKrd,OAAA,IAAY;MAChCse,SAAA,EACE,OAAOf,MAAA,KAAW,YAAYxL,KAAA,CAAMwM,OAAA,CAAQhB,MAAM,IAC9CA,MAAA,GACAX,uBAAA,CAAwBW,MAAM;MACpC,GAAGC;IACL,CAAC;IAEDlJ,QAAA,CAASkK,OAAA,CAAQpB,OAAO;IAExB,OAAO,MAAM9I,QAAA,CAAS+J,SAAA,CAAUjB,OAAO;EACzC,GAAG,CAACD,qBAAqB,CAAC;EAE1B,IAAIrH,OAAA,EAAS;IACX,OAAO,CAACrZ,GAAA,EAAKoW,OAAO;EACtB;EAEA,OAAO,CAACpW,GAAA,EAAKsgB,QAAQ;AACvB;;;ACtGO,SAAS0B,OAAO;EAAEzgB,QAAA;EAAU,GAAGrC;AAAM,GAAQ;EAClD,OAAOqC,QAAA,CAASgZ,SAAA,CAAUrb,KAAK,CAAC;AAClC;;;ACvBA,SAASlB,EAAA,IAAAikB,IAAA,QAAU;AAgBZ,SAASC,MAAqD;EACnEnhB,KAAA;EACAQ,QAAA;EACA,GAAGrC;AACL,GAA2D;EACzD,MAAMijB,MAAA,GAAgBhH,QAAA,CAASpa,KAAA,CAAMyC,MAAA,EAAQtE,KAAK;EAClD,OAAO6B,KAAA,CAAMqB,GAAA,CAAI,CAAC2K,IAAA,EAAMmM,KAAA,KAAU;IAChC,MAAMrO,MAAA,GAAStJ,QAAA,CAASwL,IAAA,EAAMmM,KAAK;IACnC,OAAO+I,IAAA,CAAGvjB,GAAA,CAAImM,MAAM,IAAIA,MAAA,CAAOsX,MAAA,CAAOjJ,KAAK,CAAC,IAAIrO,MAAA;EAClD,CAAC;AACH;;;AClBO,SAASuX,WAAW;EACzBrhB,KAAA;EACAQ,QAAA;EACA,GAAGrC;AACL,GAAkC;EAChC,OAAOgd,aAAA,CAAcnb,KAAA,EAAO7B,KAAK,EAAEqC,QAAQ;AAC7C;;;AChBA,SAAqB4L,oBAAA,IAAAkV,qBAAA,QAA4B;;;ACCjD,SACErkB,EAAA,IAAAskB,IAAA,EACApd,GAAA,IAAAqd,IAAA,EACAzkB,IAAA,IAAA0kB,MAAA,EACAnd,OAAA,IAAAod,QAAA,EACAxkB,OAAA,IAAAykB,QAAA,EACAld,SAAA,IAAAmd,UAAA,EAEAxkB,aAAA,IAAAykB,cAAA,EACAC,kBAAA,EACAxkB,OAAA,IAAWykB,EAAA,EACXhd,kBAAA,IAAAid,mBAAA,EACA9c,gBAAA,IAAA+c,iBAAA,EACA9c,mBAAA,IAAA+c,oBAAA,EACAjd,aAAA,IAAAkd,cAAA,QACK;AAGP,SACE3c,WAAA,IAAA4c,YAAA,EACA1c,WAAA,IAAA2c,YAAA,EACA1c,eAAA,IAAA2c,gBAAA,EACA/c,UAAA,IAAAgd,WAAA,QACK;AAUA,IAAMC,aAAA,GAAN,cAGG/V,UAAA,CAAmB;EAa3B7F,YAEW6b,MAAA,EACT/kB,IAAA,EACA;IACA,MAAM;IAHG,KAAA+kB,MAAA,GAAAA,MAAA;IAVX;IAAA,KAAAhV,IAAA,GAAO;IAMP;IAAA,KAAU8H,OAAA,GAAU,mBAAI5G,GAAA,CAAgB;IAQtC,KAAK+T,IAAA,GAAOZ,kBAAA,CAAmB,GAAGpkB,IAAI;IAEtC,MAAMD,KAAA,GAAQ,KAAKklB,IAAA,CAAK;IACxB,MAAMlP,QAAA,GAAW6O,gBAAA,CAAgB7kB,KAAK;IAGtC4kB,YAAA,CAAY,MAAM5O,QAAA,CAASQ,MAAA,CAAOxW,KAAK,CAAC;EAC1C;EAEA2R,QAAQwT,GAAA,EAAc;IACpB,MAAMnlB,KAAA,GAAQ,KAAKklB,IAAA,CAAK;IACxB,MAAME,QAAA,GAAW,KAAK5Y,GAAA,CAAI;IAC1B,IAAI,CAACyX,QAAA,CAAQjkB,KAAA,EAAOolB,QAAQ,GAAG;MAC7BT,YAAA,CAAY,IAAI,EAAG7Q,QAAA,CAAS9T,KAAK;MACjC,KAAK+P,SAAA,CAAU/P,KAAA,EAAO,KAAKgQ,IAAI;IACjC;IAEA,IAAI,CAAC,KAAKA,IAAA,IAAQqV,SAAA,CAAU,KAAKvN,OAAO,GAAG;MACzCwN,UAAA,CAAW,IAAI;IACjB;EACF;EAEUJ,KAAA,EAAO;IACf,MAAMK,MAAA,GAAwBzB,IAAA,CAAGngB,GAAA,CAAI,KAAKqhB,MAAM,IAC5C,KAAKA,MAAA,CAAOphB,GAAA,CAAIwgB,cAAa,IAC5BF,QAAA,CAAQE,cAAA,CAAc,KAAKY,MAAM,CAAC;IAEvC,OAAO,KAAKC,IAAA,CAAK,GAAGM,MAAM;EAC5B;EAEU3Q,OAAA,EAAS;IACjB,IAAI,KAAK5E,IAAA,IAAQ,CAACqV,SAAA,CAAU,KAAKvN,OAAO,GAAG;MACzC,KAAK9H,IAAA,GAAO;MAEZgU,MAAA,CAAKc,WAAA,CAAW,IAAI,GAAIvV,IAAA,IAAQ;QAC9BA,IAAA,CAAKyC,IAAA,GAAO;MACd,CAAC;MAED,IAAIsS,EAAA,CAAEhZ,aAAA,EAAe;QACnByY,IAAA,CAAIzV,cAAA,CAAe,MAAM,KAAKqD,OAAA,CAAQ,CAAC;QACvC2T,UAAA,CAAW,IAAI;MACjB,OAAO;QACLnB,UAAA,CAAU/e,KAAA,CAAM,IAAI;MACtB;IACF;EACF;EAAA;EAGUwK,QAAA,EAAU;IAClB,IAAIP,QAAA,GAAW;IACf2U,MAAA,CAAKE,QAAA,CAAQ,KAAKc,MAAM,GAAGA,MAAA,IAAU;MACnC,IAAIN,cAAA,CAAcM,MAAM,GAAG;QACzBR,iBAAA,CAAiBQ,MAAA,EAAQ,IAAI;MAC/B;MACA,IAAIjW,YAAA,CAAaiW,MAAM,GAAG;QACxB,IAAI,CAACA,MAAA,CAAOhV,IAAA,EAAM;UAChB,KAAK8H,OAAA,CAAQvT,GAAA,CAAIygB,MAAM;QACzB;QACA3V,QAAA,GAAWzF,IAAA,CAAK4b,GAAA,CAAInW,QAAA,EAAU2V,MAAA,CAAO3V,QAAA,GAAW,CAAC;MACnD;IACF,CAAC;IACD,KAAKA,QAAA,GAAWA,QAAA;IAChB,KAAKuF,MAAA,CAAO;EACd;EAAA;EAGU9E,QAAA,EAAU;IAClBkU,MAAA,CAAKE,QAAA,CAAQ,KAAKc,MAAM,GAAGA,MAAA,IAAU;MACnC,IAAIN,cAAA,CAAcM,MAAM,GAAG;QACzBP,oBAAA,CAAoBO,MAAA,EAAQ,IAAI;MAClC;IACF,CAAC;IACD,KAAKlN,OAAA,CAAQrJ,KAAA,CAAM;IACnB6W,UAAA,CAAW,IAAI;EACjB;EAAA;EAGA5Q,cAAcC,KAAA,EAAyB;IAGrC,IAAIA,KAAA,CAAM1E,IAAA,IAAQ,UAAU;MAC1B,IAAI0E,KAAA,CAAM3E,IAAA,EAAM;QACd,KAAK2B,OAAA,CAAQ;MACf,OAAO;QACL,KAAKmG,OAAA,CAAQvT,GAAA,CAAIoQ,KAAA,CAAMzE,MAAM;QAC7B,KAAK0E,MAAA,CAAO;MACd;IACF,WAGSD,KAAA,CAAM1E,IAAA,IAAQ,QAAQ;MAC7B,KAAK6H,OAAA,CAAQzT,MAAA,CAAOsQ,KAAA,CAAMzE,MAAM;IAClC,WAGSyE,KAAA,CAAM1E,IAAA,IAAQ,YAAY;MACjC,KAAKZ,QAAA,GAAW6U,QAAA,CAAQ,KAAKc,MAAM,EAAES,MAAA,CACnC,CAACC,OAAA,EAAiBxV,MAAA,KAChBtG,IAAA,CAAK4b,GAAA,CAAIE,OAAA,GAAU3W,YAAA,CAAamB,MAAM,IAAIA,MAAA,CAAOb,QAAA,GAAW,KAAK,CAAC,GACpE,CACF;IACF;EACF;AACF;AAGA,SAASsW,OAAOX,MAAA,EAAa;EAC3B,OAAOA,MAAA,CAAOhV,IAAA,KAAS;AACzB;AAGA,SAASqV,UAAUxU,MAAA,EAAyB;EAG1C,OAAO,CAACA,MAAA,CAAOgG,IAAA,IAAQC,KAAA,CAAMxV,IAAA,CAAKuP,MAAM,EAAEpE,KAAA,CAAMkZ,MAAM;AACxD;AAGA,SAASL,WAAWM,IAAA,EAAqB;EACvC,IAAI,CAACA,IAAA,CAAK5V,IAAA,EAAM;IACd4V,IAAA,CAAK5V,IAAA,GAAO;IAEZgU,MAAA,CAAKc,WAAA,CAAWc,IAAI,GAAIrW,IAAA,IAAQ;MAC9BA,IAAA,CAAKyC,IAAA,GAAO;IACd,CAAC;IAEDuS,mBAAA,CAAmBqB,IAAA,EAAM;MACvB3V,IAAA,EAAM;MACNC,MAAA,EAAQ0V;IACV,CAAC;EACH;AACF;;;AD/KO,IAAMrkB,EAAA,GAAmBA,CAACyjB,MAAA,KAAgB/kB,IAAA,KAC/C,IAAI8kB,aAAA,CAAcC,MAAA,EAAQ/kB,IAAI;AAGzB,IAAMwP,WAAA,GAA4BA,CAACuV,MAAA,KAAgB/kB,IAAA,MACxD4jB,qBAAA,CAAqB,GAAG,IAAIkB,aAAA,CAAcC,MAAA,EAAQ/kB,IAAI;;;AEjBxD,SACEJ,OAAA,EACAmH,SAAA,IAAA6e,UAAA,EACAhiB,wBAAA,QACK;AAIPhE,OAAA,CAAQwJ,MAAA,CAAO;EACbxF,wBAAA;EACAtC,EAAA,EAAIA,CAACyjB,MAAA,EAAQ/kB,IAAA,KAAS,IAAI8kB,aAAA,CAAcC,MAAA,EAAQ/kB,IAAI;AACtD,CAAC;AAKM,IAAM4C,MAAA,GAASgjB,UAAA,CAAUlU,OAAA;;;ACFhC,SACE0S,kBAAA,IAAAyB,mBAAA,EACAvmB,yBAAA,IAAAwmB,0BAAA,EACAC,gBAAA,EACA5d,OAAA,IAAA6d,QAAA,QACK;AAIP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}