import { useState, useEffect } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Container, Box, Paper, Typography, Grid, Alert, Chip } from '@mui/material';
import Header from './components/Header';
import FloodMap from './components/FloodMap';
import PredictionForm from './components/PredictionForm';
import ResultDisplay from './components/ResultDisplay';
import RiskFactorsChart from './components/RiskFactorsChart';
import TimelineRiskPredictor from './components/TimelineRiskPredictor';
import VoiceEmergencyAssistant from './components/VoiceEmergencyAssistant';
import CommunityReports from './components/CommunityReports';
import { SlideUp, ScaleIn } from './components/animations/AnimatedComponents';
import LoadingAnimation from './components/animations/LoadingAnimation';
import axios from 'axios';
import { floodguardTheme } from './theme/floodguardTheme';
import './components/ResponsiveLayout.css';

// Use the comprehensive Floodguard theme
const theme = floodguardTheme;

function App() {
  const [mapData, setMapData] = useState([]);
  const [options, setOptions] = useState({ land_cover: [], soil_type: [] });
  const [prediction, setPrediction] = useState(null);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [showPredictionResult, setShowPredictionResult] = useState(false);
  const [forecastSummary, setForecastSummary] = useState(null);
  const [showForecastAlert, setShowForecastAlert] = useState(false);

  useEffect(() => {
    // Fetch map data and options when component mounts
    const fetchData = async () => {
      setInitialLoading(true);
      let loadingTimer;

      try {
        const [mapResponse, optionsResponse] = await Promise.all([
          axios.get('/api/map-data'),
          axios.get('/api/options')
        ]);
        setMapData(mapResponse.data);
        setOptions(optionsResponse.data);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        // Add a slight delay to make the loading animation visible
        // but ensure it gets cleared if component unmounts
        loadingTimer = setTimeout(() => {
          setInitialLoading(false);
        }, 1500);
      }

      // Cleanup function to ensure loading state is reset if component unmounts
      return () => {
        if (loadingTimer) clearTimeout(loadingTimer);
        setInitialLoading(false);
      };
    };

    fetchData();
  }, []);

  const handleSubmit = async (formData) => {
    setLoading(true);
    setShowPredictionResult(false);
    setShowForecastAlert(false);

    try {
      // Add a slight delay to make the loading animation visible
      await new Promise(resolve => setTimeout(resolve, 1200));

      const response = await axios.post('/api/predict', formData);
      setPrediction(response.data);

      // Add a slight delay before showing the result for better animation
      setTimeout(() => {
        setShowPredictionResult(true);
      }, 300);
    } catch (error) {
      console.error('Error making prediction:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleForecastGenerated = (summary) => {
    setForecastSummary(summary);
    setShowForecastAlert(true);

    // Hide the alert after 10 seconds
    setTimeout(() => {
      setShowForecastAlert(false);
    }, 10000);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Header />
      <VoiceEmergencyAssistant />

      {initialLoading ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '80vh',
            background: theme.palette.background.default
          }}
        >
          <Box sx={{ position: 'relative', width: 200, height: 200 }}>
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                borderRadius: '50%',
                border: '4px solid transparent',
                borderTopColor: theme.palette.primary.main,
                animation: 'spin 1.5s linear infinite',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                top: 15,
                left: 15,
                width: 'calc(100% - 30px)',
                height: 'calc(100% - 30px)',
                borderRadius: '50%',
                border: '4px solid transparent',
                borderTopColor: theme.palette.secondary.main,
                animation: 'spin 2s linear infinite',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                top: 30,
                left: 30,
                width: 'calc(100% - 60px)',
                height: 'calc(100% - 60px)',
                borderRadius: '50%',
                border: '4px solid transparent',
                borderTopColor: theme.palette.info.main,
                animation: 'spin 2.5s linear infinite',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }}
            />
          </Box>
          <Typography
            variant="h4"
            sx={{
              mt: 4,
              fontWeight: 600,
              background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,
              backgroundClip: 'text',
              textFillColor: 'transparent',
              animation: 'pulse 2s infinite',
              '@keyframes pulse': {
                '0%': { opacity: 0.6 },
                '50%': { opacity: 1 },
                '100%': { opacity: 0.6 }
              }
            }}
          >
            Loading Flood Prediction System...
          </Typography>
        </Box>
      ) : (
        <Container
          maxWidth="xl"
          sx={{
            mt: { xs: 3, sm: 4, md: 5 },
            mb: { xs: 5, sm: 7, md: 10 },
            px: { xs: 2, sm: 3, md: 4 },
            py: { xs: 4, sm: 5, md: 6 },
            overflow: 'hidden',
            backgroundColor: theme.palette.background.elevated,
            borderRadius: '24px',
            boxShadow: `
              inset 1px 1px 2px rgba(255, 255, 255, 0.5),
              inset -1px -1px 2px rgba(174, 174, 192, 0.3)
            `,
            position: 'relative',
            zIndex: 1
          }}
        >
          <Grid
            container
            spacing={{ xs: 3, sm: 4, md: 5 }}
            alignItems="stretch"
            sx={{
              '& .MuiGrid-item': {
                display: 'flex',
                flexDirection: 'column'
              }
            }}
          >
            <Grid item xs={12}>
              <Paper
                elevation={3}
                sx={{
                  p: { xs: 4, sm: 5, md: 6 },
                  mb: { xs: 3, sm: 4, md: 5 },
                  backgroundColor: theme.palette.background.paper,
                  position: 'relative',
                  overflow: 'hidden',
                  borderRadius: 3,
                  transition: 'all 0.3s ease-in-out',
                  boxShadow: `
                    6px 6px 12px rgba(174, 174, 192, 0.3),
                    -6px -6px 12px rgba(255, 255, 255, 0.5)
                  `,
                  '&:hover': {
                    boxShadow: `
                      8px 8px 16px rgba(174, 174, 192, 0.35),
                      -8px -8px 16px rgba(255, 255, 255, 0.6)
                    `
                  },
                  minHeight: { xs: 'auto', sm: '200px' }
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    width: '180px',
                    height: '180px',
                    background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',
                    borderRadius: '0 0 0 100%',
                    zIndex: 0
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    width: '120px',
                    height: '120px',
                    background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',
                    borderRadius: '0 100% 0 0',
                    zIndex: 0
                  }}
                />
                <Box sx={{ position: 'relative', zIndex: 1 }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    flexDirection: { xs: 'column', sm: 'row' },
                    mb: { xs: 3, sm: 4 },
                    gap: { xs: 2, sm: 0 }
                  }}>
                    <Box
                      sx={{
                        mr: { xs: 0, sm: 2 },
                        p: { xs: 1.5, sm: 2 },
                        borderRadius: '50%',
                        background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        boxShadow: '0 4px 12px rgba(76, 201, 240, 0.15)',
                        alignSelf: { xs: 'center', sm: 'flex-start' }
                      }}
                    >
                      <Typography variant="h4" component="span">🌊</Typography>
                    </Box>
                    <Typography
                      variant="h2"
                      component="h1"
                      sx={{
                        background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.secondary.main} 90%)`,
                        backgroundClip: 'text',
                        textFillColor: 'transparent',
                        fontWeight: 800,
                        letterSpacing: '-0.5px',
                        fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                        textAlign: { xs: 'center', sm: 'left' },
                        lineHeight: 1.2
                      }}
                    >
                      Flood Risk Prediction System
                    </Typography>
                  </Box>
                  <Typography
                    variant="body1"
                    paragraph
                    sx={{
                      fontSize: { xs: '1rem', sm: '1.1rem' },
                      maxWidth: { xs: '100%', sm: '90%' },
                      color: 'text.secondary',
                      lineHeight: 1.6,
                      mb: { xs: 3, sm: 4 },
                      textAlign: { xs: 'center', sm: 'left' }
                    }}
                  >
                    This interactive tool helps predict flood risk based on various environmental and geographical factors.
                    For Indian cities, we provide accurate predictions using historical flood data and real-time weather conditions.
                  </Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      gap: { xs: 1.5, sm: 2 },
                      flexWrap: 'wrap',
                      mt: { xs: 3, sm: 4 },
                      justifyContent: { xs: 'center', sm: 'flex-start' }
                    }}
                  >
                    <Chip
                      label="Real-time Weather Data"
                      color="primary"
                      size="medium"
                      icon={<span>⛈️</span>}
                      sx={{ fontWeight: 500, px: 1 }}
                    />
                    <Chip
                      label="Historical Flood Analysis"
                      color="info"
                      size="medium"
                      icon={<span>📊</span>}
                      sx={{ fontWeight: 500, px: 1 }}
                    />
                    <Chip
                      label="Indian Cities Database"
                      color="success"
                      size="medium"
                      icon={<span>🏙️</span>}
                      sx={{ fontWeight: 500, px: 1 }}
                    />
                  </Box>
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} lg={6}>
              <Paper
                elevation={3}
                sx={{
                  p: { xs: 3, sm: 4, md: 5 },
                  height: '100%',
                  minHeight: { xs: 'auto', lg: '600px' },
                  position: 'relative',
                  overflow: 'hidden',
                  borderRadius: 3,
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'
                  },
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100px',
                    height: '100px',
                    background: 'radial-gradient(circle, rgba(58,134,255,0.08) 0%, rgba(0,0,0,0) 70%)',
                    borderRadius: '0 0 100% 0',
                    zIndex: 0
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    right: 0,
                    width: '80px',
                    height: '80px',
                    background: 'radial-gradient(circle, rgba(76,201,240,0.05) 0%, rgba(0,0,0,0) 70%)',
                    borderRadius: '100% 0 0 0',
                    zIndex: 0
                  }}
                />
                <Box sx={{ position: 'relative', zIndex: 1 }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    flexDirection: { xs: 'column', sm: 'row' },
                    mb: { xs: 3, sm: 4 },
                    gap: { xs: 2, sm: 0 }
                  }}>
                    <Box
                      sx={{
                        mr: { xs: 0, sm: 2 },
                        p: { xs: 1, sm: 1.5 },
                        borderRadius: '12px',
                        background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        alignSelf: { xs: 'center', sm: 'flex-start' }
                      }}
                    >
                      <Typography variant="h5" component="span">📝</Typography>
                    </Box>
                    <Typography
                      variant="h4"
                      sx={{
                        fontWeight: 700,
                        color: theme.palette.primary.main,
                        letterSpacing: '-0.5px',
                        fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' },
                        textAlign: { xs: 'center', sm: 'left' }
                      }}
                    >
                      Predict Flood Risk
                    </Typography>
                  </Box>
                  <Typography
                    variant="body1"
                    sx={{
                      mb: { xs: 3, sm: 4 },
                      color: 'text.secondary',
                      maxWidth: '100%',
                      textAlign: { xs: 'center', sm: 'left' }
                    }}
                  >
                    Enter location and environmental factors to get a precise flood risk assessment.
                    For Indian cities, we provide enhanced accuracy using historical data.
                  </Typography>
                  <PredictionForm
                    options={options}
                    onSubmit={handleSubmit}
                    loading={loading}
                  />
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} lg={6}>
              <Paper
                elevation={3}
                sx={{
                  p: { xs: 3, sm: 4, md: 5 },
                  height: '100%',
                  minHeight: { xs: 'auto', lg: '600px' },
                  position: 'relative',
                  overflow: 'hidden',
                  borderRadius: 3,
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'
                  },
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    width: '100px',
                    height: '100px',
                    background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',
                    borderRadius: '0 0 0 100%',
                    zIndex: 0
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    width: '80px',
                    height: '80px',
                    background: 'radial-gradient(circle, rgba(255,89,94,0.05) 0%, rgba(0,0,0,0) 70%)',
                    borderRadius: '0 100% 0 0',
                    zIndex: 0
                  }}
                />
                <Box sx={{ position: 'relative', zIndex: 1 }}>
                  {loading ? (
                    <LoadingAnimation theme={theme} />
                  ) : prediction && showPredictionResult ? (
                    <Box sx={{
                      opacity: showPredictionResult ? 1 : 0,
                      transform: showPredictionResult ? 'translateY(0)' : 'translateY(20px)',
                      transition: 'all 0.5s ease-in-out'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Box
                          sx={{
                            mr: 2,
                            p: 1,
                            borderRadius: '12px',
                            background: 'linear-gradient(135deg, rgba(76, 201, 240, 0.1) 0%, rgba(58, 134, 255, 0.1) 100%)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <Typography variant="h5" component="span">📊</Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontWeight: 700,
                            color: theme.palette.primary.main,
                            letterSpacing: '-0.5px'
                          }}
                        >
                          Risk Assessment
                        </Typography>
                      </Box>
                      <ResultDisplay prediction={prediction} />
                    </Box>
                  ) : (
                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      py: 8,
                      opacity: loading ? 0 : 1,
                      transition: 'opacity 0.3s ease-in-out'
                    }}>
                      <Box
                        sx={{
                          width: 100,
                          height: 100,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',
                          boxShadow: '0 8px 32px rgba(76, 201, 240, 0.12)',
                          mb: 3,
                          animation: 'float 3s ease-in-out infinite',
                          '@keyframes float': {
                            '0%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                            '100%': { transform: 'translateY(0px)' }
                          }
                        }}
                      >
                        <Typography variant="h2" component="span">📊</Typography>
                      </Box>
                      <Typography variant="h5" sx={{ fontWeight: 600, color: theme.palette.primary.main, textAlign: 'center', mb: 1 }}>
                        Results will appear here
                      </Typography>
                      <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', maxWidth: '80%', mx: 'auto' }}>
                        Fill out the form on the left to generate a detailed flood risk assessment
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Paper>
            </Grid>

            {prediction && prediction.risk_assessment && showPredictionResult && (
              <SlideUp delay={200}>
                <Grid item xs={12}>
                  <Paper
                    elevation={3}
                    sx={{
                      p: { xs: 3, md: 4 },
                      background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',
                      borderRadius: 2,
                      position: 'relative',
                      overflow: 'hidden',
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'
                      }
                    }}
                  >
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 0,
                        right: 0,
                        width: '150px',
                        height: '150px',
                        background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',
                        borderRadius: '0 0 0 100%',
                        zIndex: 0
                      }}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        width: '120px',
                        height: '120px',
                        background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',
                        borderRadius: '0 100% 0 0',
                        zIndex: 0
                      }}
                    />
                    <Box sx={{ position: 'relative', zIndex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Box
                          sx={{
                            mr: 2,
                            p: 1,
                            borderRadius: '12px',
                            background: 'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <Typography variant="h5" component="span">📈</Typography>
                        </Box>
                        <Typography
                          variant="h4"
                          sx={{
                            fontWeight: 700,
                            color: theme.palette.primary.dark,
                            letterSpacing: '-0.5px'
                          }}
                        >
                          Risk Factor Analysis
                        </Typography>
                      </Box>
                      <Typography
                        variant="body1"
                        sx={{
                          mb: 3,
                          color: 'text.secondary',
                          maxWidth: '800px'
                        }}
                      >
                        This chart shows the contribution of different environmental and geographical factors to the overall flood risk assessment.
                        {prediction.accurate_data && " For Indian cities, this includes historical flood data and real-time weather conditions."}
                      </Typography>
                      <RiskFactorsChart riskAssessment={prediction.risk_assessment} />
                    </Box>
                  </Paper>
                </Grid>
              </SlideUp>
            )}

            {/* Forecast Alert */}
            {showForecastAlert && forecastSummary && (
              <ScaleIn delay={100}>
                <Grid item xs={12}>
                  <Alert
                    severity={forecastSummary.maxRiskScore > 70 ? "error" : forecastSummary.maxRiskScore > 40 ? "warning" : "info"}
                    variant="filled"
                    sx={{
                      mb: 3,
                      borderRadius: 2,
                      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
                      '& .MuiAlert-icon': { fontSize: '1.8rem' },
                      p: 2,
                      animation: 'pulse 2s infinite',
                      '@keyframes pulse': {
                        '0%': { boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)' },
                        '50%': { boxShadow: '0 8px 36px rgba(0, 0, 0, 0.25)' },
                        '100%': { boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)' }
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                          {forecastSummary.maxRiskScore > 70
                            ? "⚠️ High flood risk detected in the forecast!"
                            : forecastSummary.maxRiskScore > 40
                              ? "⚠️ Medium flood risk detected in the forecast"
                              : "ℹ️ Flood risk forecast generated"}
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          Location: <strong>{forecastSummary.location || 'Selected area'}</strong>
                        </Typography>
                        <Typography variant="body1">
                          Peak risk score of <strong>{forecastSummary.maxRiskScore.toFixed(1)}</strong> expected around <strong>{forecastSummary.maxRiskTime}</strong>.
                          Risk trend is <strong>{forecastSummary.riskTrend}</strong>.
                        </Typography>
                        {forecastSummary.maxRiskScore > 60 && (
                          <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
                            Please monitor local weather updates and follow emergency guidelines.
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Alert>
                </Grid>
              </ScaleIn>
            )}

            {/* Temporal Prediction Component */}
            {prediction && showPredictionResult && (
              <SlideUp delay={300}>
                <Grid item xs={12}>
                  <Paper
                    elevation={3}
                    sx={{
                      borderRadius: 2,
                      overflow: 'hidden',
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'
                      }
                    }}
                  >
                    <TimelineRiskPredictor
                      formData={prediction.input_data}
                      onForecastGenerated={handleForecastGenerated}
                    />
                  </Paper>
                </Grid>
              </SlideUp>
            )}

            <SlideUp delay={200}>
              <Grid item xs={12}>
                <Paper
                  elevation={3}
                  sx={{
                    p: { xs: 3, sm: 4, md: 5 },
                    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',
                    borderRadius: 3,
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'
                    }
                  }}
                >
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      width: '150px',
                      height: '150px',
                      background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',
                      borderRadius: '0 0 0 100%',
                      zIndex: 0
                    }}
                  />
                  <Box sx={{ position: 'relative', zIndex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <Box
                        sx={{
                          mr: 2,
                          p: 1,
                          borderRadius: '12px',
                          background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <Typography variant="h5" component="span">🗺️</Typography>
                      </Box>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 700,
                          color: theme.palette.primary.dark,
                          letterSpacing: '-0.5px'
                        }}
                      >
                        Flood Risk Map
                      </Typography>
                    </Box>
                    <Typography
                      variant="body1"
                      paragraph
                      sx={{
                        mb: 3,
                        color: 'text.secondary',
                        maxWidth: '800px'
                      }}
                    >
                      This interactive map shows areas with predicted flood risk based on our analysis.
                      Green markers indicate low risk areas, while red markers indicate high risk zones.
                      Click on markers to see detailed information.
                    </Typography>
                    <Box sx={{
                      borderRadius: 3,
                      overflow: 'hidden',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      height: { xs: '400px', sm: '500px', md: '600px' },
                      mt: { xs: 2, sm: 3 }
                    }}>
                      <FloodMap mapData={mapData} />
                    </Box>
                  </Box>
                </Paper>
              </Grid>
            </SlideUp>

            {/* Community Reports Section */}
            <SlideUp delay={300}>
              <Grid item xs={12}>
                <Paper
                  elevation={3}
                  sx={{
                    p: { xs: 3, sm: 4, md: 5 },
                    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',
                    borderRadius: 3,
                    position: 'relative',
                    overflow: 'hidden',
                    transition: 'all 0.3s ease-in-out',
                    '&:hover': {
                      boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'
                    }
                  }}
                >
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      width: '150px',
                      height: '150px',
                      background: 'radial-gradient(circle, rgba(255,89,94,0.08) 0%, rgba(0,0,0,0) 70%)',
                      borderRadius: '0 0 0 100%',
                      zIndex: 0
                    }}
                  />
                  <Box sx={{ position: 'relative', zIndex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <Box
                        sx={{
                          mr: 2,
                          p: 1,
                          borderRadius: '12px',
                          background: 'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <Typography variant="h5" component="span">👥</Typography>
                      </Box>
                      <Typography
                        variant="h4"
                        sx={{
                          fontWeight: 700,
                          color: theme.palette.primary.dark,
                          letterSpacing: '-0.5px'
                        }}
                      >
                        Community Reports
                      </Typography>
                    </Box>
                    <Typography
                      variant="body1"
                      paragraph
                      sx={{
                        mb: 3,
                        color: 'text.secondary',
                        maxWidth: '800px'
                      }}
                    >
                      View and submit real-time flood reports from community members. These reports help validate our predictions
                      and provide valuable on-the-ground information during flood events.
                    </Typography>
                    <CommunityReports />
                  </Box>
                </Paper>
              </Grid>
            </SlideUp>
          </Grid>
        </Container>
      )}
    </ThemeProvider>
  );
}

export default App;
