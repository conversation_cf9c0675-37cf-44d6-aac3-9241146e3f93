/**
 * Floodguard AI-Powered Risk Mapping System
 * Comprehensive Theme Configuration
 * 
 * This theme is specifically designed for flood management applications with:
 * - WCAG 2.1 AA accessibility compliance
 * - Colorblind-friendly color choices
 * - Water, safety, and environmental color semantics
 * - Professional appearance for government/municipal use
 */

import { createTheme } from '@mui/material/styles';
import { getFloodguardPalette, getNeuromorphicShadow } from './neuromorphicUtils';

// Get the Floodguard color palette
const palette = getFloodguardPalette();

// Create the comprehensive Floodguard theme
export const createFloodguardTheme = () => {
  return createTheme({
    palette: {
      mode: 'light',
      
      // Core palette
      primary: palette.primary,
      secondary: palette.secondary,
      error: palette.error,
      warning: palette.warning,
      success: palette.success,
      info: palette.info,
      
      // Background and surface colors
      background: {
        default: palette.background.default,
        paper: palette.background.paper,
        elevated: palette.background.elevated
      },
      
      // Text colors
      text: {
        primary: palette.text.primary,
        secondary: palette.text.secondary,
        disabled: palette.text.disabled,
        contrast: palette.text.contrast
      },
      
      // Divider colors
      divider: palette.divider,
      
      // Custom color extensions for flood management
      floodRisk: palette.floodRisk,
      water: palette.water,
      environment: palette.environment,
      surface: palette.surface,
      border: palette.border
    },
    
    typography: {
      fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
      
      h1: {
        fontSize: 'clamp(2.5rem, 5vw, 3.5rem)',
        fontWeight: 800,
        letterSpacing: '-0.02em',
        lineHeight: 1.1,
        color: palette.text.primary
      },
      
      h2: {
        fontSize: 'clamp(2rem, 4vw, 2.75rem)',
        fontWeight: 700,
        letterSpacing: '-0.01em',
        lineHeight: 1.2,
        color: palette.text.primary
      },
      
      h3: {
        fontSize: 'clamp(1.5rem, 3vw, 2rem)',
        fontWeight: 600,
        letterSpacing: '-0.01em',
        lineHeight: 1.3,
        color: palette.text.primary
      },
      
      h4: {
        fontSize: 'clamp(1.25rem, 2.5vw, 1.75rem)',
        fontWeight: 600,
        letterSpacing: '0em',
        lineHeight: 1.4,
        color: palette.text.primary
      },
      
      h5: {
        fontSize: 'clamp(1.1rem, 2vw, 1.5rem)',
        fontWeight: 500,
        letterSpacing: '0em',
        lineHeight: 1.4,
        color: palette.text.primary
      },
      
      h6: {
        fontSize: 'clamp(1rem, 1.5vw, 1.25rem)',
        fontWeight: 500,
        letterSpacing: '0.01em',
        lineHeight: 1.5,
        color: palette.text.primary
      },
      
      body1: {
        fontSize: 'clamp(0.875rem, 1.5vw, 1rem)',
        lineHeight: 1.6,
        color: palette.text.secondary
      },
      
      body2: {
        fontSize: 'clamp(0.8125rem, 1.25vw, 0.875rem)',
        lineHeight: 1.6,
        color: palette.text.secondary
      },
      
      button: {
        fontWeight: 600,
        textTransform: 'none',
        letterSpacing: '0.02em',
        fontSize: 'clamp(0.875rem, 1.5vw, 1rem)'
      },
      
      subtitle1: {
        fontSize: 'clamp(0.9375rem, 1.75vw, 1.125rem)',
        fontWeight: 500,
        lineHeight: 1.5,
        color: palette.text.secondary
      },
      
      subtitle2: {
        fontSize: 'clamp(0.8125rem, 1.5vw, 0.9375rem)',
        fontWeight: 500,
        lineHeight: 1.5,
        color: palette.text.secondary
      }
    },
    
    shape: {
      borderRadius: 12
    },
    
    // Enhanced shadow system for depth and hierarchy
    shadows: [
      'none',
      '0px 1px 2px rgba(15, 23, 42, 0.05)',
      '0px 2px 4px rgba(15, 23, 42, 0.06), 0px 1px 2px rgba(15, 23, 42, 0.04)',
      '0px 4px 6px rgba(15, 23, 42, 0.07), 0px 2px 4px rgba(15, 23, 42, 0.05)',
      '0px 6px 8px rgba(15, 23, 42, 0.08), 0px 3px 6px rgba(15, 23, 42, 0.06)',
      '0px 8px 12px rgba(15, 23, 42, 0.09), 0px 4px 8px rgba(15, 23, 42, 0.07)',
      '0px 10px 15px rgba(15, 23, 42, 0.10), 0px 6px 10px rgba(15, 23, 42, 0.08)',
      '0px 12px 18px rgba(15, 23, 42, 0.11), 0px 7px 12px rgba(15, 23, 42, 0.09)',
      '0px 14px 21px rgba(15, 23, 42, 0.12), 0px 8px 14px rgba(15, 23, 42, 0.10)',
      '0px 16px 24px rgba(15, 23, 42, 0.13), 0px 9px 16px rgba(15, 23, 42, 0.11)',
      '0px 18px 27px rgba(15, 23, 42, 0.14), 0px 10px 18px rgba(15, 23, 42, 0.12)',
      '0px 20px 30px rgba(15, 23, 42, 0.15), 0px 11px 20px rgba(15, 23, 42, 0.13)',
      '0px 22px 33px rgba(15, 23, 42, 0.16), 0px 12px 22px rgba(15, 23, 42, 0.14)',
      '0px 24px 36px rgba(15, 23, 42, 0.17), 0px 13px 24px rgba(15, 23, 42, 0.15)',
      '0px 26px 39px rgba(15, 23, 42, 0.18), 0px 14px 26px rgba(15, 23, 42, 0.16)',
      '0px 28px 42px rgba(15, 23, 42, 0.19), 0px 15px 28px rgba(15, 23, 42, 0.17)',
      '0px 30px 45px rgba(15, 23, 42, 0.20), 0px 16px 30px rgba(15, 23, 42, 0.18)',
      '0px 32px 48px rgba(15, 23, 42, 0.21), 0px 17px 32px rgba(15, 23, 42, 0.19)',
      '0px 34px 51px rgba(15, 23, 42, 0.22), 0px 18px 34px rgba(15, 23, 42, 0.20)',
      '0px 36px 54px rgba(15, 23, 42, 0.23), 0px 19px 36px rgba(15, 23, 42, 0.21)',
      '0px 38px 57px rgba(15, 23, 42, 0.24), 0px 20px 38px rgba(15, 23, 42, 0.22)',
      '0px 40px 60px rgba(15, 23, 42, 0.25), 0px 21px 40px rgba(15, 23, 42, 0.23)',
      '0px 42px 63px rgba(15, 23, 42, 0.26), 0px 22px 42px rgba(15, 23, 42, 0.24)',
      '0px 44px 66px rgba(15, 23, 42, 0.27), 0px 23px 44px rgba(15, 23, 42, 0.25)',
      '0px 46px 69px rgba(15, 23, 42, 0.28), 0px 24px 46px rgba(15, 23, 42, 0.26)'
    ],
    
    breakpoints: {
      values: {
        xs: 0,
        sm: 600,
        md: 960,
        lg: 1280,
        xl: 1920
      }
    },

    // Component style overrides with Floodguard theme
    components: {
      // Container component
      MuiContainer: {
        styleOverrides: {
          root: {
            paddingLeft: 16,
            paddingRight: 16,
            '@media (min-width:600px)': {
              paddingLeft: 24,
              paddingRight: 24,
            },
            '@media (min-width:960px)': {
              paddingLeft: 32,
              paddingRight: 32,
            },
            '@media (min-width:1200px)': {
              paddingLeft: 48,
              paddingRight: 48,
            },
            maxWidth: '100%',
            '@media (min-width:1280px)': {
              maxWidth: '1280px',
            },
            '@media (min-width:1920px)': {
              maxWidth: '1920px',
            },
          },
        },
      },

      // Grid component
      MuiGrid: {
        styleOverrides: {
          container: {
            marginTop: 0,
            marginLeft: 0,
            width: '100%',
          },
          item: {
            paddingTop: 12,
            paddingLeft: 12,
            '@media (min-width:600px)': {
              paddingTop: 16,
              paddingLeft: 16,
            },
            '@media (min-width:960px)': {
              paddingTop: 20,
              paddingLeft: 20,
            },
            '@media (min-width:1200px)': {
              paddingTop: 24,
              paddingLeft: 24,
            },
          },
        },
      },

      // Paper component with neuromorphic styling
      MuiPaper: {
        styleOverrides: {
          root: {
            backgroundColor: palette.background.paper,
            borderRadius: 16,
            boxShadow: getNeuromorphicShadow(palette.background.paper),
            transition: 'all 0.3s ease-in-out',
            border: `1px solid ${palette.border.light}`,
            '&:hover': {
              boxShadow: getNeuromorphicShadow(palette.background.paper, 1.2),
              borderColor: palette.border.medium,
            },
          },
          elevation1: {
            backgroundColor: palette.surface.level1,
          },
          elevation2: {
            backgroundColor: palette.surface.level2,
          },
          elevation3: {
            backgroundColor: palette.surface.level3,
          },
        },
      },

      // Button component with enhanced styling
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            fontWeight: 600,
            textTransform: 'none',
            transition: 'all 0.2s ease-in-out',
            boxShadow: 'none',
            '&:hover': {
              boxShadow: '0 4px 12px rgba(15, 23, 42, 0.15)',
              transform: 'translateY(-1px)',
            },
            '&:active': {
              transform: 'translateY(0)',
            },
          },
          containedPrimary: {
            backgroundColor: palette.primary.main,
            color: palette.primary.contrastText,
            '&:hover': {
              backgroundColor: palette.primary.dark,
              boxShadow: `0 6px 16px ${palette.primary.main}40`,
            },
            '&:active': {
              backgroundColor: palette.primary.dark,
            },
          },
          containedSecondary: {
            backgroundColor: palette.secondary.main,
            color: palette.secondary.contrastText,
            '&:hover': {
              backgroundColor: palette.secondary.dark,
              boxShadow: `0 6px 16px ${palette.secondary.main}40`,
            },
          },
          outlined: {
            borderColor: palette.border.medium,
            color: palette.text.primary,
            '&:hover': {
              borderColor: palette.primary.main,
              backgroundColor: `${palette.primary.main}08`,
            },
          },
          text: {
            color: palette.text.primary,
            '&:hover': {
              backgroundColor: `${palette.primary.main}08`,
            },
          },
        },
      },

      // TextField component
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              backgroundColor: palette.background.paper,
              borderRadius: 12,
              transition: 'all 0.2s ease-in-out',
              '& fieldset': {
                borderColor: palette.border.medium,
                borderWidth: 1,
              },
              '&:hover fieldset': {
                borderColor: palette.border.strong,
              },
              '&.Mui-focused fieldset': {
                borderColor: palette.primary.main,
                borderWidth: 2,
              },
              '&.Mui-error fieldset': {
                borderColor: palette.error.main,
              },
            },
            '& .MuiInputLabel-root': {
              color: palette.text.secondary,
              '&.Mui-focused': {
                color: palette.primary.main,
              },
              '&.Mui-error': {
                color: palette.error.main,
              },
            },
            '& .MuiInputBase-input': {
              color: palette.text.primary,
            },
          },
        },
      },

      // Alert component with flood-specific styling
      MuiAlert: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            fontWeight: 500,
            border: `1px solid ${palette.border.light}`,
          },
          standardSuccess: {
            backgroundColor: palette.floodRisk.minimal,
            color: palette.success.dark,
            borderColor: palette.success.light,
            '& .MuiAlert-icon': {
              color: palette.success.main,
            },
          },
          standardError: {
            backgroundColor: palette.floodRisk.severe,
            color: palette.error.dark,
            borderColor: palette.error.light,
            '& .MuiAlert-icon': {
              color: palette.error.main,
            },
          },
          standardWarning: {
            backgroundColor: palette.floodRisk.moderate,
            color: palette.warning.dark,
            borderColor: palette.warning.light,
            '& .MuiAlert-icon': {
              color: palette.warning.main,
            },
          },
          standardInfo: {
            backgroundColor: `${palette.info.main}10`,
            color: palette.info.dark,
            borderColor: palette.info.light,
            '& .MuiAlert-icon': {
              color: palette.info.main,
            },
          },
        },
      },

      // Chip component with enhanced styling
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            fontWeight: 500,
            border: `1px solid ${palette.border.light}`,
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              transform: 'translateY(-1px)',
              boxShadow: '0 4px 12px rgba(15, 23, 42, 0.15)',
            },
          },
          colorPrimary: {
            backgroundColor: palette.primary.main,
            color: palette.primary.contrastText,
            borderColor: palette.primary.main,
          },
          colorSecondary: {
            backgroundColor: palette.secondary.main,
            color: palette.secondary.contrastText,
            borderColor: palette.secondary.main,
          },
          colorSuccess: {
            backgroundColor: palette.success.main,
            color: palette.success.contrastText,
            borderColor: palette.success.main,
          },
          colorError: {
            backgroundColor: palette.error.main,
            color: palette.error.contrastText,
            borderColor: palette.error.main,
          },
          colorWarning: {
            backgroundColor: palette.warning.main,
            color: palette.warning.contrastText,
            borderColor: palette.warning.main,
          },
          colorInfo: {
            backgroundColor: palette.info.main,
            color: palette.info.contrastText,
            borderColor: palette.info.main,
          },
        },
      },

      // Card component
      MuiCard: {
        styleOverrides: {
          root: {
            backgroundColor: palette.background.paper,
            borderRadius: 16,
            border: `1px solid ${palette.border.light}`,
            transition: 'all 0.3s ease-in-out',
            '&:hover': {
              borderColor: palette.border.medium,
              transform: 'translateY(-2px)',
              boxShadow: '0 8px 24px rgba(15, 23, 42, 0.12)',
            },
          },
        },
      },

      // Typography component
      MuiTypography: {
        styleOverrides: {
          root: {
            color: palette.text.primary,
          },
          h1: {
            color: palette.text.primary,
            fontWeight: 800,
          },
          h2: {
            color: palette.text.primary,
            fontWeight: 700,
          },
          h3: {
            color: palette.text.primary,
            fontWeight: 600,
          },
          h4: {
            color: palette.text.primary,
            fontWeight: 600,
          },
          h5: {
            color: palette.text.primary,
            fontWeight: 500,
          },
          h6: {
            color: palette.text.primary,
            fontWeight: 500,
          },
          subtitle1: {
            color: palette.text.secondary,
          },
          subtitle2: {
            color: palette.text.secondary,
          },
          body1: {
            color: palette.text.secondary,
          },
          body2: {
            color: palette.text.secondary,
          },
        },
      }
    }
  });
};

// Export the default theme instance
export const floodguardTheme = createFloodguardTheme();

// Export color utilities for direct use in components
export { palette as floodguardColors };

// Export specific color sets for easy access
export const riskColors = palette.floodRisk;
export const waterColors = palette.water;
export const environmentColors = palette.environment;
