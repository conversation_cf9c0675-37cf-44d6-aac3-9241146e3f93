# Branch Information

## Default Branch: master

This repository uses `master` as the default branch containing the complete Floodguard AI-Powered Risk Mapping System.

## Branch Structure

- **master** (default) - Contains the complete application with all features and improvements
- **main** - Contains the same code (for compatibility)

## Latest Updates

The master branch contains:
- Complete React frontend with responsive design improvements
- Flask backend with ML models
- Professional layout fixes and mobile optimization
- Comprehensive documentation
- Automated setup scripts

## Development

For development and contributions, please use the master branch:

```bash
git clone https://github.com/UjjwalMaletha/Floodguard-AI-Powered-Risk-Mapping-System-for-Flood-Management-in-India.git
cd Floodguard-AI-Powered-Risk-Mapping-System-for-Flood-Management-in-India
git checkout master
```

## Setup

Follow the instructions in README.md to set up the application locally.
